2025-08-06 00:07:47 | SUCCESS | 读取主设置成功
2025-08-06 00:07:47 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-06 00:07:47 | INFO | 2025/08/06 00:07:47 GetRedisAddr: 127.0.0.1:6379
2025-08-06 00:07:47 | INFO | 2025/08/06 00:07:47 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-06 00:07:47 | INFO | 2025/08/06 00:07:47 Server start at :9000
2025-08-06 00:07:48 | SUCCESS | WechatAPI服务已启动
2025-08-06 00:07:48 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-06 00:07:48 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-06 00:07:48 | SUCCESS | 登录成功
2025-08-06 00:07:48 | SUCCESS | 已开启自动心跳
2025-08-06 00:07:48 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-06 00:07:48 | SUCCESS | 数据库初始化成功
2025-08-06 00:07:49 | SUCCESS | 定时任务已启动
2025-08-06 00:07:49 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-06 00:07:49 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:07:50 | INFO | 播客API初始化成功
2025-08-06 00:07:50 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-06 00:07:50 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-06 00:07:50 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-06 00:07:50 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-06 00:07:50 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-06 00:07:50 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-06 00:07:50 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-06 00:07:50 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-06 00:07:50 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-06 00:07:50 | INFO | [ChatSummary] 数据库初始化成功
2025-08-06 00:07:50 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-06 00:07:50 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-06 00:07:50 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-06 00:07:50 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-06 00:07:50 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-06 00:07:50 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-06 00:07:50 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-06 00:07:50 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-06 00:07:50 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-06 00:07:50 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-06 00:07:50 | DEBUG |   - 启用状态: True
2025-08-06 00:07:50 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-06 00:07:50 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-06 00:07:50 | DEBUG |   - Web ID: 7532989324985157172
2025-08-06 00:07:50 | DEBUG |   - Cookies配置: 已配置
2025-08-06 00:07:50 | DEBUG |   - 限制机制: 已禁用
2025-08-06 00:07:50 | DEBUG |   - 数字选择超时: 120秒
2025-08-06 00:07:50 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-06 00:07:50 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-06 00:07:50 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-06 00:07:50 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-06 00:07:50 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-06 00:07:50 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-06 00:07:50 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-06 00:07:50 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:07:50 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-06 00:07:50 | INFO | [RenameReminder] 开始启用插件...
2025-08-06 00:07:50 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-06 00:07:50 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-06 00:07:50 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-06 00:07:50 | INFO | 已设置检查间隔为 3600 秒
2025-08-06 00:07:50 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-06 00:07:50 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-06 00:07:51 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-06 00:07:51 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-06 00:07:51 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-06 00:07:51 | INFO | [VoiceMusicPlugin] 插件初始化完成
2025-08-06 00:07:52 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-06 00:07:52 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:07:52 | INFO | [yuanbao] 插件初始化完成
2025-08-06 00:07:52 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-06 00:07:52 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-06 00:07:52 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-06 00:07:52 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceMusicPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-06 00:07:52 | INFO | 处理堆积消息中
2025-08-06 00:07:52 | SUCCESS | 处理堆积消息完毕
2025-08-06 00:07:52 | SUCCESS | 开始处理消息
2025-08-06 00:08:02 | DEBUG | 收到消息: {'MsgId': 1888840172, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 超级英雄'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410094, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_PK6saOEP|v1_SJd1kxo2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 超级英雄', 'NewMsgId': 8842143267002960749, 'MsgSeq': 871430328}
2025-08-06 00:08:02 | INFO | 收到文本消息: 消息ID:1888840172 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 超级英雄
2025-08-06 00:08:02 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 超级英雄' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 00:08:02 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '超级英雄']
2025-08-06 00:08:02 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-06 00:08:02 | DEBUG | 处理消息内容: '语音点歌 超级英雄'
2025-08-06 00:08:02 | DEBUG | 消息内容 '语音点歌 超级英雄' 不匹配任何命令，忽略
2025-08-06 00:08:04 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '龚十一 - 超级英雄狂想曲-ㅤ1111', 'singer': 'ㅤ001', 'url': 'http://ring.bssdlbig.kugou.com/04d78f62244500954bcb7565a85e8ead.mp3'}}
2025-08-06 00:08:05 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 00:08:05 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 396291 bytes
2025-08-06 00:08:06 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:24726 格式:mp3 音频base64略
2025-08-06 00:08:06 | INFO | [VoiceMusicPlugin] 成功发送音乐: 龚十一 - 超级英雄狂想曲-ㅤ1111 - ㅤ001
2025-08-06 00:08:22 | DEBUG | 收到消息: {'MsgId': 1096491855, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 超级英雄'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410114, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_wPwCKtbB|v1_s6M9poeJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5111652338678289397, 'MsgSeq': 871430331}
2025-08-06 00:08:22 | INFO | 收到文本消息: 消息ID:1096491855 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 超级英雄
2025-08-06 00:08:22 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 超级英雄' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:08:22 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '超级英雄']
2025-08-06 00:08:22 | DEBUG | 处理消息内容: '语音点歌 超级英雄'
2025-08-06 00:08:22 | DEBUG | 消息内容 '语音点歌 超级英雄' 不匹配任何命令，忽略
2025-08-06 00:08:23 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '龚十一 - 超级英雄狂想曲-ㅤ1111', 'singer': 'ㅤ001', 'url': 'http://ring.bssdlbig.kugou.com/04d78f62244500954bcb7565a85e8ead.mp3'}}
2025-08-06 00:08:24 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 00:08:24 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 396291 bytes
2025-08-06 00:08:26 | INFO | 发送语音消息: 对方wxid:27852221909@chatroom 时长:24726 格式:mp3 音频base64略
2025-08-06 00:08:26 | INFO | [VoiceMusicPlugin] 成功发送音乐: 龚十一 - 超级英雄狂想曲-ㅤ1111 - ㅤ001
2025-08-06 00:09:38 | DEBUG | 收到消息: {'MsgId': 2081848648, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 gogogo'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410190, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_HgQRd1lT|v1_CTS1NJK7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 gogogo', 'NewMsgId': 1393587583243213964, 'MsgSeq': 871430334}
2025-08-06 00:09:38 | INFO | 收到文本消息: 消息ID:2081848648 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 gogogo
2025-08-06 00:09:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 gogogo' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 00:09:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', 'gogogo']
2025-08-06 00:09:39 | DEBUG | 处理消息内容: '语音点歌 gogogo'
2025-08-06 00:09:39 | DEBUG | 消息内容 '语音点歌 gogogo' 不匹配任何命令，忽略
2025-08-06 00:09:41 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '刘耀文-加油加油gogogo', 'singer': 'yy', 'url': 'http://ring.bssdlbig.kugou.com/ad320ae644bb2a47238baf8135e172be.mp3'}}
2025-08-06 00:09:42 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 00:09:42 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 47975 bytes
2025-08-06 00:09:43 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:3004 格式:mp3 音频base64略
2025-08-06 00:09:43 | INFO | [VoiceMusicPlugin] 成功发送音乐: 刘耀文-加油加油gogogo - yy
2025-08-06 00:10:08 | DEBUG | 收到消息: {'MsgId': 1226254309, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 丑八怪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410220, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_gYivBsUn|v1_oVNd/mXg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 丑八怪', 'NewMsgId': 8811081151159691012, 'MsgSeq': 871430337}
2025-08-06 00:10:08 | INFO | 收到文本消息: 消息ID:1226254309 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 丑八怪
2025-08-06 00:10:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 丑八怪' from wxid_ubbh6q832tcs21 in 47325400669@chatroom
2025-08-06 00:10:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '丑八怪']
2025-08-06 00:10:08 | DEBUG | 处理消息内容: '语音点歌 丑八怪'
2025-08-06 00:10:08 | DEBUG | 消息内容 '语音点歌 丑八怪' 不匹配任何命令，忽略
2025-08-06 00:10:08 | INFO | 发送文字消息: 对方wxid:47325400669@chatroom at: 内容:⚠️ 点歌太频繁啦~请稍后再试
2025-08-06 00:10:09 | DEBUG | 收到消息: {'MsgId': 1584022424, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': "wxid_1jjimgid98no12:\n音色 '点歌' 不存在！\n发送 '语音菜单' 查看可用音色列表"}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410221, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_nOlB0i2p|v1_uE3PErA3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': "ting : 音色 '点歌' 不存在！\n发送 '语音菜单' 查看可用音色列表", 'NewMsgId': 6328539553118214771, 'MsgSeq': 871430340}
2025-08-06 00:10:09 | INFO | 收到文本消息: 消息ID:1584022424 来自:47325400669@chatroom 发送人:wxid_1jjimgid98no12 @:[] 内容:音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表
2025-08-06 00:10:09 | DEBUG | [DouBaoImageToImage] 收到文本消息: '音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表' from wxid_1jjimgid98no12 in 47325400669@chatroom
2025-08-06 00:10:09 | DEBUG | [DouBaoImageToImage] 命令解析: ['音色', "'点歌'", "不存在！\n发送 '语音菜单' 查看可用音色列表"]
2025-08-06 00:10:09 | DEBUG | 处理消息内容: '音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表'
2025-08-06 00:10:09 | DEBUG | 消息内容 '音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表' 不匹配任何命令，忽略
2025-08-06 00:10:27 | DEBUG | 收到消息: {'MsgId': 326410320, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "47325400669@chatroom" type="2" idbuffer="media:0_0" md5="3c5d9173b23ecc025e1ec8c858142b57" len = "1005541" productid="" androidmd5="3c5d9173b23ecc025e1ec8c858142b57" androidlen="1005541" s60v3md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v3len="1005541" s60v5md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v5len="1005541" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3c5d9173b23ecc025e1ec8c858142b57&amp;filekey=30440201010430302e02016e040253480420336335643931373362323365636330323565316563386338353831343262353702030f57e5040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b00015f7c687175f00000006e01004fb153480fd65b01e02592370&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4bab97cabbe1706f0a603389b3529eeb&amp;filekey=30440201010430302e02016e040253480420346261623937636162626531373036663061363033333839623335323965656202030f57f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0003cd1f687175f00000006e02004fb253480fd65b01e02592390&amp;ef=2&amp;bizid=1022" aeskey= "0d82b033625c4bf18bc5c338aeb7333e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0064acb5c83b81eef262bb4c27a223c8&amp;filekey=30440201010430302e02016e04025348042030303634616362356338336238316565663236326262346332376132323363380203020430040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0005d930687175f00000006e03004fb353480fd65b01e025923ad&amp;ef=3&amp;bizid=1022" externmd5 = "ef10f433c759c4681e3dcaecf0b0a32d" width= "640" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410239, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_X67OftiD|v1_vs0xj/IZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 3672499742142549374, 'MsgSeq': 871430341}
2025-08-06 00:10:27 | INFO | 收到表情消息: 消息ID:326410320 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:3c5d9173b23ecc025e1ec8c858142b57 大小:1005541
2025-08-06 00:10:27 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3672499742142549374
2025-08-06 00:10:40 | DEBUG | 收到消息: {'MsgId': 1368962081, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 丑八怪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410252, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_QB/RvyCI|v1_PAVESeE0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 丑八怪', 'NewMsgId': 2903545309992487965, 'MsgSeq': 871430342}
2025-08-06 00:10:40 | INFO | 收到文本消息: 消息ID:1368962081 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 丑八怪
2025-08-06 00:10:40 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 丑八怪' from wxid_ubbh6q832tcs21 in 47325400669@chatroom
2025-08-06 00:10:40 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '丑八怪']
2025-08-06 00:10:40 | DEBUG | 处理消息内容: '语音点歌 丑八怪'
2025-08-06 00:10:40 | DEBUG | 消息内容 '语音点歌 丑八怪' 不匹配任何命令，忽略
2025-08-06 00:10:41 | DEBUG | 收到消息: {'MsgId': 1439196307, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': "wxid_1jjimgid98no12:\n音色 '点歌' 不存在！\n发送 '语音菜单' 查看可用音色列表"}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410253, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_ltPXhrVx|v1_mWWmWNHN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': "ting : 音色 '点歌' 不存在！\n发送 '语音菜单' 查看可用音色列表", 'NewMsgId': 7165865456155397456, 'MsgSeq': 871430343}
2025-08-06 00:10:41 | INFO | 收到文本消息: 消息ID:1439196307 来自:47325400669@chatroom 发送人:wxid_1jjimgid98no12 @:[] 内容:音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表
2025-08-06 00:10:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表' from wxid_1jjimgid98no12 in 47325400669@chatroom
2025-08-06 00:10:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['音色', "'点歌'", "不存在！\n发送 '语音菜单' 查看可用音色列表"]
2025-08-06 00:10:41 | DEBUG | 处理消息内容: '音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表'
2025-08-06 00:10:41 | DEBUG | 消息内容 '音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表' 不匹配任何命令，忽略
2025-08-06 00:11:56 | DEBUG | 收到消息: {'MsgId': 948679541, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 gogogo'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410328, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>4</cf>\n\t\t<inlenlist>11</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_7jbsFhs0|v1_eauJrY3Y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 gogogo', 'NewMsgId': 8629410336859669668, 'MsgSeq': 871430344}
2025-08-06 00:11:56 | INFO | 收到文本消息: 消息ID:948679541 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 gogogo
2025-08-06 00:11:56 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 gogogo' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 00:11:56 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', 'gogogo']
2025-08-06 00:11:56 | DEBUG | 处理消息内容: '语音点歌 gogogo'
2025-08-06 00:11:56 | DEBUG | 消息内容 '语音点歌 gogogo' 不匹配任何命令，忽略
