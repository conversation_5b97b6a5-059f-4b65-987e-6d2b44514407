2025-08-05 23:03:48 | SUCCESS | 读取主设置成功
2025-08-05 23:03:48 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 23:03:48 | INFO | 2025/08/05 23:03:48 GetRedisAddr: 127.0.0.1:6379
2025-08-05 23:03:48 | INFO | 2025/08/05 23:03:48 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 23:03:48 | INFO | 2025/08/05 23:03:48 Server start at :9000
2025-08-05 23:03:49 | SUCCESS | WechatAPI服务已启动
2025-08-05 23:03:49 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 23:03:49 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 23:03:49 | SUCCESS | 登录成功
2025-08-05 23:03:49 | SUCCESS | 已开启自动心跳
2025-08-05 23:03:49 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:03:49 | SUCCESS | 数据库初始化成功
2025-08-05 23:03:49 | SUCCESS | 定时任务已启动
2025-08-05 23:03:49 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 23:03:49 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:03:50 | INFO | 播客API初始化成功
2025-08-05 23:03:50 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:03:50 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:03:50 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 23:03:50 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 23:03:50 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 23:03:50 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 23:03:50 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 23:03:50 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 23:03:50 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 23:03:51 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 23:03:51 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 23:03:51 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 23:03:51 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 23:03:51 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 23:03:51 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 23:03:51 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 23:03:51 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 23:03:51 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 23:03:51 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 23:03:51 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 23:03:51 | DEBUG |   - 启用状态: True
2025-08-05 23:03:51 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 23:03:51 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 23:03:51 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 23:03:51 | DEBUG |   - Cookies配置: 已配置
2025-08-05 23:03:51 | DEBUG |   - 限制机制: 已禁用
2025-08-05 23:03:51 | DEBUG |   - 数字选择超时: 120秒
2025-08-05 23:03:51 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 23:03:51 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-05 23:03:51 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 23:03:51 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 23:03:51 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 23:03:51 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 23:03:51 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 23:03:51 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:03:51 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 23:03:51 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 23:03:51 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 23:03:51 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 23:03:51 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 23:03:51 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 23:03:51 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 23:03:51 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 23:03:52 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-05 23:03:52 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 23:03:52 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 23:03:53 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 23:03:53 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:03:53 | INFO | [yuanbao] 插件初始化完成
2025-08-05 23:03:53 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 23:03:53 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 23:03:53 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 23:03:53 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 23:03:53 | INFO | 处理堆积消息中
2025-08-05 23:03:53 | DEBUG | 接受到 1 条消息
2025-08-05 23:03:54 | SUCCESS | 处理堆积消息完毕
2025-08-05 23:03:54 | SUCCESS | 开始处理消息
2025-08-05 23:03:59 | DEBUG | 收到消息: {'MsgId': 1498648330, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="2685" length="4146" bufid="0" aeskey="409e568108a0a0863362a801a17a3469" voiceurl="3052020100044b30490201000204a95c809d02032df9270204d4089324020468921d6a042434326661393863382d393161312d346438622d383363392d61613163303632306163653402040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600072304080525e376c3158f5105" fromusername="wxid_ubbh6q832tcs21" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406251, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_4tkvDe3n|v1_czoL46T5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段语音', 'NewMsgId': 930741750471791762, 'MsgSeq': 871430162}
2025-08-05 23:03:59 | INFO | 收到语音消息: 消息ID:1498648330 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="2685" length="4146" bufid="0" aeskey="409e568108a0a0863362a801a17a3469" voiceurl="3052020100044b30490201000204a95c809d02032df9270204d4089324020468921d6a042434326661393863382d393161312d346438622d383363392d61613163303632306163653402040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600072304080525e376c3158f5105" fromusername="wxid_ubbh6q832tcs21" /></msg>
2025-08-05 23:03:59 | DEBUG | [VoiceTest] 缓存语音 MsgId: 1498648330
2025-08-05 23:03:59 | DEBUG | [VoiceTest] 缓存语音 NewMsgId: 930741750471791762
2025-08-05 23:03:59 | INFO | [VoiceTest] 已缓存语音消息: MsgId=1498648330, NewMsgId=930741750471791762
2025-08-05 23:04:04 | DEBUG | 收到消息: {'MsgId': 363716265, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>930741750471791762</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:2685:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754406251</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406256, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>e4f7afd7dd0d0891cf0f9c921940a71a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_dbDdtZel|v1_SGzLJLlk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': *****************71, 'MsgSeq': 871430163}
2025-08-05 23:04:04 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 23:04:04 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:04:04 | INFO | 收到引用消息: 消息ID:363716265 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 23:04:04 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:04:04 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 23:04:04 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 23:04:04 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:04:04 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:04:04 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:04:04 | INFO |   - 消息内容: 跟着唱
2025-08-05 23:04:04 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 23:04:04 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 23:04:04 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:2685:0\n', 'Msgid': '930741750471791762', 'NewMsgId': '930741750471791762', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754406251', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 23:04:04 | INFO |   - 引用消息ID: 
2025-08-05 23:04:04 | INFO |   - 引用消息类型: 
2025-08-05 23:04:04 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:2685:0

2025-08-05 23:04:04 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 23:04:05 | ERROR | [VoiceTest] 处理引用语音消息异常: Extra data: line 1 column 69 (char 68)
2025-08-05 23:04:05 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 处理引用语音时出错
2025-08-05 23:04:42 | DEBUG | 收到消息: {'MsgId': 1181178847, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_4183511832012:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那我表现好吗</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8008704128405253562</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>tianen532965049</chatusr>\n\t\t\t<displayname>饿飞</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_zlAC0Vc2|v1_UPOMqOeU&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n表现好爬位包了</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754405810</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_4183511832012</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406294, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>26c2cda37f44e2597016e1eac1b3ba31_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_+EUPNiCE|v1_qgZ/fWoW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5441110708709611358, 'MsgSeq': 871430166}
2025-08-05 23:04:42 | DEBUG | 从群聊消息中提取发送者: wxid_4183511832012
2025-08-05 23:04:42 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:04:42 | INFO | 收到引用消息: 消息ID:1181178847 来自:27852221909@chatroom 发送人:wxid_4183511832012 内容:那我表现好吗 引用类型:1
2025-08-05 23:04:42 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:04:42 | INFO | [DouBaoImageToImage] 消息内容: '那我表现好吗' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:04:42 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['那我表现好吗']
2025-08-05 23:04:42 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:04:42 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:04:42 | INFO |   - 消息内容: 那我表现好吗
2025-08-05 23:04:42 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:04:42 | INFO |   - 发送人: wxid_4183511832012
2025-08-05 23:04:42 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n表现好爬位包了', 'Msgid': '8008704128405253562', 'NewMsgId': '8008704128405253562', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '饿飞', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_zlAC0Vc2|v1_UPOMqOeU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754405810', 'SenderWxid': 'wxid_4183511832012'}
2025-08-05 23:04:42 | INFO |   - 引用消息ID: 
2025-08-05 23:04:42 | INFO |   - 引用消息类型: 
2025-08-05 23:04:42 | INFO |   - 引用消息内容: 
表现好爬位包了
2025-08-05 23:04:42 | INFO |   - 引用消息发送人: wxid_4183511832012
2025-08-05 23:04:43 | DEBUG | 收到消息: {'MsgId': 1974318618, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_g173eyu8nbm522:\n九千多钻石就拿了一个盒子一个坐骑'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406294, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_aE8DkRs+|v1_7zJjkU8U</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8721883221546971785, 'MsgSeq': 871430167}
2025-08-05 23:04:43 | INFO | 收到文本消息: 消息ID:1974318618 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 @:[] 内容:九千多钻石就拿了一个盒子一个坐骑
2025-08-05 23:04:43 | DEBUG | [DouBaoImageToImage] 收到文本消息: '九千多钻石就拿了一个盒子一个坐骑' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 23:04:43 | DEBUG | [DouBaoImageToImage] 命令解析: ['九千多钻石就拿了一个盒子一个坐骑']
2025-08-05 23:04:43 | DEBUG | 处理消息内容: '九千多钻石就拿了一个盒子一个坐骑'
2025-08-05 23:04:43 | DEBUG | 消息内容 '九千多钻石就拿了一个盒子一个坐骑' 不匹配任何命令，忽略
2025-08-05 23:05:07 | DEBUG | 收到消息: {'MsgId': 287196989, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_g173eyu8nbm522:\n我太……霉了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406319, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_p/9IaRRI|v1_4Y5Jp5cb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1569380620197891556, 'MsgSeq': 871430168}
2025-08-05 23:05:07 | INFO | 收到文本消息: 消息ID:287196989 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 @:[] 内容:我太……霉了
2025-08-05 23:05:07 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我太……霉了' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 23:05:07 | DEBUG | [DouBaoImageToImage] 命令解析: ['我太……霉了']
2025-08-05 23:05:07 | DEBUG | 处理消息内容: '我太……霉了'
2025-08-05 23:05:07 | DEBUG | 消息内容 '我太……霉了' 不匹配任何命令，忽略
2025-08-05 23:06:24 | DEBUG | 收到消息: {'MsgId': 805540545, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="455c7859ba566dc6767cb330625d52d6" len="958428" productid="" androidmd5="455c7859ba566dc6767cb330625d52d6" androidlen="958428" s60v3md5="455c7859ba566dc6767cb330625d52d6" s60v3len="958428" s60v5md5="455c7859ba566dc6767cb330625d52d6" s60v5len="958428" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=455c7859ba566dc6767cb330625d52d6&amp;filekey=30440201010430302e02016e0402535a0420343535633738353962613536366463363736376362333330363235643532643602030e9fdc040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2683fd4400007d5ee7de4992c0000006e01004fb1535a0026dbc1e685e41f4&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=6c3eb07a198aa37553bec96501dc99d2&amp;filekey=30440201010430302e02016e0402535a0420366333656230376131393861613337353533626563393635303164633939643202030e9fe0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2683fd44000096e1c7de4992c0000006e02004fb2535a0026dbc1e685e4221&amp;ef=2&amp;bizid=1022" aeskey="9c352ce584ac4e9583c3f43f720e0c92" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=964a333b05a3898e7259f68f9964a19c&amp;filekey=30440201010430302e02016e0402535a04203936346133333362303561333839386537323539663638663939363461313963020300add0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2683fd440000ae6d67de4992c0000006e03004fb3535a0026dbc1e685e423d&amp;ef=3&amp;bizid=1022" externmd5="53f492c27fc62e0f60b39ff7b0d2677a" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406395, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_1O9I3NhY|v1_XwdY4Rzo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 3382692206997614541, 'MsgSeq': 871430169}
2025-08-05 23:06:24 | INFO | 收到表情消息: 消息ID:805540545 来自:48097389945@chatroom 发送人:last--exile MD5:455c7859ba566dc6767cb330625d52d6 大小:958428
2025-08-05 23:06:24 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3382692206997614541
2025-08-05 23:06:28 | DEBUG | 收到消息: {'MsgId': 1139144663, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n有个叫凌妙的申请了，看谁能同意下'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406399, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_1i72pdL1|v1_TlXG2/8B</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7150048250198138967, 'MsgSeq': 871430170}
2025-08-05 23:06:28 | INFO | 收到文本消息: 消息ID:1139144663 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:有个叫凌妙的申请了，看谁能同意下
2025-08-05 23:06:28 | DEBUG | [DouBaoImageToImage] 收到文本消息: '有个叫凌妙的申请了，看谁能同意下' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:06:28 | DEBUG | [DouBaoImageToImage] 命令解析: ['有个叫凌妙的申请了，看谁能同意下']
2025-08-05 23:06:28 | DEBUG | 处理消息内容: '有个叫凌妙的申请了，看谁能同意下'
2025-08-05 23:06:28 | DEBUG | 消息内容 '有个叫凌妙的申请了，看谁能同意下' 不匹配任何命令，忽略
2025-08-05 23:06:30 | DEBUG | 收到消息: {'MsgId': 537428165, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n@锦岚\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406400, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_wlnzvr8ivgd422]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_4Qzf+k2R|v1_P0NGap+j</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : @锦岚\u2005', 'NewMsgId': 6648134458358264486, 'MsgSeq': 871430171}
2025-08-05 23:06:30 | INFO | 收到文本消息: 消息ID:537428165 来自:48097389945@chatroom 发送人:last--exile @:['wxid_wlnzvr8ivgd422'] 内容:@锦岚 
2025-08-05 23:06:30 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@锦岚' from last--exile in 48097389945@chatroom
2025-08-05 23:06:30 | DEBUG | [DouBaoImageToImage] 命令解析: ['@锦岚']
2025-08-05 23:06:30 | DEBUG | 处理消息内容: '@锦岚'
2025-08-05 23:06:30 | DEBUG | 消息内容 '@锦岚' 不匹配任何命令，忽略
2025-08-05 23:06:34 | DEBUG | 收到消息: {'MsgId': 1384804740, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>正常操作，习惯就好</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1569380620197891556</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_g173eyu8nbm522</chatusr>\n\t\t\t<displayname>清弦</displayname>\n\t\t\t<content>我太……霉了</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;810053667&lt;/sequence_id&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_euGnKWzU|v1_2bqLEyW3&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754406319</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_x4s6k999g6qg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406406, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>e9125b80aa25a037eff09d7d9976c941_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Et4Gx1tf|v1_Ekk7dulU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2918486080164907687, 'MsgSeq': 871430172}
2025-08-05 23:06:34 | DEBUG | 从群聊消息中提取发送者: wxid_x4s6k999g6qg22
2025-08-05 23:06:34 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:06:34 | INFO | 收到引用消息: 消息ID:1384804740 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 内容:正常操作，习惯就好 引用类型:1
2025-08-05 23:06:34 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:06:34 | INFO | [DouBaoImageToImage] 消息内容: '正常操作，习惯就好' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 23:06:34 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['正常操作，习惯就好']
2025-08-05 23:06:34 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:06:34 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:06:34 | INFO |   - 消息内容: 正常操作，习惯就好
2025-08-05 23:06:34 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:06:34 | INFO |   - 发送人: wxid_x4s6k999g6qg22
2025-08-05 23:06:34 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '我太……霉了', 'Msgid': '1569380620197891556', 'NewMsgId': '1569380620197891556', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '清弦', 'MsgSource': '<msgsource><sequence_id>810053667</sequence_id>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_euGnKWzU|v1_2bqLEyW3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754406319', 'SenderWxid': 'wxid_x4s6k999g6qg22'}
2025-08-05 23:06:34 | INFO |   - 引用消息ID: 
2025-08-05 23:06:34 | INFO |   - 引用消息类型: 
2025-08-05 23:06:34 | INFO |   - 引用消息内容: 我太……霉了
2025-08-05 23:06:34 | INFO |   - 引用消息发送人: wxid_x4s6k999g6qg22
2025-08-05 23:06:41 | DEBUG | 收到消息: {'MsgId': 1012796787, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n我给她说了活动结束前最少花1.5w钻'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406413, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_5cdLPPLu|v1_LCF8oGpv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 901180458411093107, 'MsgSeq': 871430173}
2025-08-05 23:06:41 | INFO | 收到文本消息: 消息ID:1012796787 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:我给她说了活动结束前最少花1.5w钻
2025-08-05 23:06:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我给她说了活动结束前最少花1.5w钻' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:06:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['我给她说了活动结束前最少花1.5w钻']
2025-08-05 23:06:41 | DEBUG | 处理消息内容: '我给她说了活动结束前最少花1.5w钻'
2025-08-05 23:06:41 | DEBUG | 消息内容 '我给她说了活动结束前最少花1.5w钻' 不匹配任何命令，忽略
2025-08-05 23:06:46 | DEBUG | 收到消息: {'MsgId': 1706631654, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n你还不睡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406418, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_/F2vr6Ys|v1_o3kggkMC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 你还不睡', 'NewMsgId': 8210730674911122669, 'MsgSeq': 871430174}
2025-08-05 23:06:46 | INFO | 收到文本消息: 消息ID:1706631654 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:你还不睡
2025-08-05 23:06:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你还不睡' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-05 23:06:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['你还不睡']
2025-08-05 23:06:46 | DEBUG | 处理消息内容: '你还不睡'
2025-08-05 23:06:46 | DEBUG | 消息内容 '你还不睡' 不匹配任何命令，忽略
2025-08-05 23:06:54 | DEBUG | 收到消息: {'MsgId': 832789133, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n你不十点多就睡了吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406426, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_Ckc+h/hg|v1_4e8F4XjB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 你不十点多就睡了吗', 'NewMsgId': 320515197791721381, 'MsgSeq': 871430175}
2025-08-05 23:06:54 | INFO | 收到文本消息: 消息ID:832789133 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:你不十点多就睡了吗
2025-08-05 23:06:54 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你不十点多就睡了吗' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-05 23:06:54 | DEBUG | [DouBaoImageToImage] 命令解析: ['你不十点多就睡了吗']
2025-08-05 23:06:54 | DEBUG | 处理消息内容: '你不十点多就睡了吗'
2025-08-05 23:06:54 | DEBUG | 消息内容 '你不十点多就睡了吗' 不匹配任何命令，忽略
2025-08-05 23:06:58 | DEBUG | 收到消息: {'MsgId': 24629226, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>通过了</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>901180458411093107</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_xfxd40diz3bd22</chatusr>\n\t\t\t<displayname>初见</displayname>\n\t\t\t<content>我给她说了活动结束前最少花1.5w钻</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;810053679&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_W/8qquPy|v1_Zm7oZhkA&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754406413</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_x4s6k999g6qg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406430, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>38b6189168865805d2fbd41206120cf9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_/DROjakD|v1_QOfFTm80</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6972019621401240218, 'MsgSeq': 871430176}
2025-08-05 23:06:58 | DEBUG | 从群聊消息中提取发送者: wxid_x4s6k999g6qg22
2025-08-05 23:06:58 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:06:58 | INFO | 收到引用消息: 消息ID:24629226 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 内容:通过了 引用类型:1
2025-08-05 23:06:58 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:06:58 | INFO | [DouBaoImageToImage] 消息内容: '通过了' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 23:06:58 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['通过了']
2025-08-05 23:06:58 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:06:58 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:06:58 | INFO |   - 消息内容: 通过了
2025-08-05 23:06:58 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:06:58 | INFO |   - 发送人: wxid_x4s6k999g6qg22
2025-08-05 23:06:58 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '我给她说了活动结束前最少花1.5w钻', 'Msgid': '901180458411093107', 'NewMsgId': '901180458411093107', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '初见', 'MsgSource': '<msgsource><sequence_id>810053679</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_W/8qquPy|v1_Zm7oZhkA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754406413', 'SenderWxid': 'wxid_x4s6k999g6qg22'}
2025-08-05 23:06:58 | INFO |   - 引用消息ID: 
2025-08-05 23:06:58 | INFO |   - 引用消息类型: 
2025-08-05 23:06:58 | INFO |   - 引用消息内容: 我给她说了活动结束前最少花1.5w钻
2025-08-05 23:06:58 | INFO |   - 引用消息发送人: wxid_x4s6k999g6qg22
2025-08-05 23:07:04 | DEBUG | 收到消息: {'MsgId': 904320150, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n好的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406436, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_D27weHa7|v1_EQC4f+t8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5197959910895506555, 'MsgSeq': 871430177}
2025-08-05 23:07:04 | INFO | 收到文本消息: 消息ID:904320150 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:好的
2025-08-05 23:07:04 | DEBUG | [DouBaoImageToImage] 收到文本消息: '好的' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:07:04 | DEBUG | [DouBaoImageToImage] 命令解析: ['好的']
2025-08-05 23:07:04 | DEBUG | 处理消息内容: '好的'
2025-08-05 23:07:04 | DEBUG | 消息内容 '好的' 不匹配任何命令，忽略
2025-08-05 23:07:33 | DEBUG | 收到消息: {'MsgId': 1314725237, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n多花点最好[爱心]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406465, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_W+yeHvcH|v1_KpzDBMOG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2292151308931816512, 'MsgSeq': 871430178}
2025-08-05 23:07:33 | INFO | 收到文本消息: 消息ID:1314725237 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:多花点最好[爱心]
2025-08-05 23:07:33 | DEBUG | [DouBaoImageToImage] 收到文本消息: '多花点最好[爱心]' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 23:07:33 | DEBUG | [DouBaoImageToImage] 命令解析: ['多花点最好[爱心]']
2025-08-05 23:07:33 | DEBUG | 处理消息内容: '多花点最好[爱心]'
2025-08-05 23:07:33 | DEBUG | 消息内容 '多花点最好[爱心]' 不匹配任何命令，忽略
