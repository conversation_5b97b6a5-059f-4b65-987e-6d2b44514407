2025-08-06 00:03:04 | DEBUG | 收到消息: {'MsgId': 1168466630, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n重载所有插件'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754409796, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_Kn13zyyJ|v1_Nl7lY4CT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 重载所有插件', 'NewMsgId': 4534562974677958356, 'MsgSeq': 871430323}
2025-08-06 00:03:04 | INFO | 收到文本消息: 消息ID:1168466630 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:重载所有插件
2025-08-06 00:03:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '重载所有插件' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 00:03:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['重载所有插件']
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | [BaiduAgents] 插件已禁用,资源清理完成
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | [VoiceMusicPlugin] 插件已禁用
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | INFO | 已卸载定时任务: set()
2025-08-06 00:03:05 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-06 00:03:05 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:03:06 | INFO | 播客API初始化成功
2025-08-06 00:03:06 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-06 00:03:06 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-06 00:03:06 | INFO | [ChatSummary] 数据库初始化成功
2025-08-06 00:03:06 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-06 00:03:06 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-06 00:03:06 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-06 00:03:06 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-06 00:03:06 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-06 00:03:06 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-06 00:03:06 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-06 00:03:06 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-06 00:03:06 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-06 00:03:06 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-06 00:03:06 | DEBUG |   - 启用状态: True
2025-08-06 00:03:06 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-06 00:03:06 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-06 00:03:06 | DEBUG |   - Web ID: 7532989324985157172
2025-08-06 00:03:06 | DEBUG |   - Cookies配置: 已配置
2025-08-06 00:03:06 | DEBUG |   - 限制机制: 已禁用
2025-08-06 00:03:06 | DEBUG |   - 数字选择超时: 120秒
2025-08-06 00:03:06 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-06 00:03:06 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-06 00:03:06 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-06 00:03:06 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-06 00:03:06 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-06 00:03:06 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-06 00:03:06 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-06 00:03:06 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:03:06 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-06 00:03:06 | INFO | [RenameReminder] 开始启用插件...
2025-08-06 00:03:06 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-06 00:03:06 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-06 00:03:06 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-06 00:03:06 | INFO | 已设置检查间隔为 3600 秒
2025-08-06 00:03:06 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-06 00:03:07 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-06 00:03:07 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-06 00:03:07 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-06 00:03:07 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-06 00:03:07 | ERROR | 加载插件时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 51, in load_plugin
    plugin = plugin_class()
             ^^^^^^^^^^^^^^
  File "C:\XYBotV2\plugins\VoiceMusicPlugin\main.py", line 35, in __init__
    self.request_limit = config.get("request-limit", 3)  # 5分钟内最多允许3次点歌
                         ^^^^^^
UnboundLocalError: cannot access local variable 'config' where it is not associated with a value

2025-08-06 00:03:07 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-06 00:03:07 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:03:07 | INFO | [yuanbao] 插件初始化完成
2025-08-06 00:03:07 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-06 00:03:07 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-06 00:03:07 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-06 00:03:08 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅所有插件重载成功
2025-08-06 00:03:08 | DEBUG | 处理消息内容: '重载所有插件'
2025-08-06 00:03:08 | DEBUG | 消息内容 '重载所有插件' 不匹配任何命令，忽略
2025-08-06 00:03:16 | DEBUG | 收到消息: {'MsgId': 295726759, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 超级英雄'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754409808, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_JMs6c2y3|v1_BszfERy6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 超级英雄', 'NewMsgId': 8832209596228923038, 'MsgSeq': 871430326}
2025-08-06 00:03:16 | INFO | 收到文本消息: 消息ID:295726759 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 超级英雄
2025-08-06 00:03:16 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 超级英雄' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 00:03:16 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '超级英雄']
2025-08-06 00:03:16 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-06 00:03:16 | DEBUG | 处理消息内容: '语音点歌 超级英雄'
2025-08-06 00:03:16 | DEBUG | 消息内容 '语音点歌 超级英雄' 不匹配任何命令，忽略
