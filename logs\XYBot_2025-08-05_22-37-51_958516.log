2025-08-05 22:37:53 | SUCCESS | 读取主设置成功
2025-08-05 22:37:53 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 22:37:53 | INFO | 2025/08/05 22:37:53 GetRedisAddr: 127.0.0.1:6379
2025-08-05 22:37:53 | INFO | 2025/08/05 22:37:53 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 22:37:53 | INFO | 2025/08/05 22:37:53 Server start at :9000
2025-08-05 22:37:53 | SUCCESS | WechatAPI服务已启动
2025-08-05 22:37:54 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 22:37:54 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 22:37:54 | SUCCESS | 登录成功
2025-08-05 22:37:54 | SUCCESS | 已开启自动心跳
2025-08-05 22:37:54 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 22:37:54 | SUCCESS | 数据库初始化成功
2025-08-05 22:37:54 | SUCCESS | 定时任务已启动
2025-08-05 22:37:54 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 22:37:54 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 22:37:55 | INFO | 播客API初始化成功
2025-08-05 22:37:55 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['***********@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '***********@chatroom']}}
2025-08-05 22:37:55 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['***********@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '***********@chatroom']}}
2025-08-05 22:37:55 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 22:37:55 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 22:37:55 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 22:37:55 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 22:37:55 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 22:37:55 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 22:37:55 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 22:37:55 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 22:37:55 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 22:37:55 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 22:37:55 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 22:37:55 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 22:37:55 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 22:37:55 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 22:37:55 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 22:37:55 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 22:37:55 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 22:37:55 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 22:37:55 | DEBUG |   - 启用状态: True
2025-08-05 22:37:55 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 22:37:55 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 22:37:55 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 22:37:55 | DEBUG |   - Cookies配置: 已配置
2025-08-05 22:37:55 | DEBUG |   - 限制机制: 已禁用
2025-08-05 22:37:55 | DEBUG |   - 数字选择超时: 120秒
2025-08-05 22:37:55 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 22:37:55 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-05 22:37:55 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 22:37:55 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 22:37:55 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 22:37:55 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 22:37:55 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 22:37:55 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 22:37:55 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 22:37:55 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 22:37:55 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 22:37:55 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 22:37:55 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 22:37:55 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 22:37:55 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 22:37:56 | DEBUG | 已更新群 ***********@chatroom 的成员列表
2025-08-05 22:37:56 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 22:37:56 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 22:37:56 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 22:37:57 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 22:37:57 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 22:37:57 | INFO | [yuanbao] 插件初始化完成
2025-08-05 22:37:57 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 22:37:57 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 22:37:57 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 22:37:57 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 22:37:57 | INFO | 处理堆积消息中
2025-08-05 22:37:57 | DEBUG | 接受到 4 条消息
2025-08-05 22:37:58 | DEBUG | 接受到 1 条消息
2025-08-05 22:37:59 | SUCCESS | 处理堆积消息完毕
2025-08-05 22:37:59 | SUCCESS | 开始处理消息
2025-08-05 22:38:02 | DEBUG | 收到消息: {'MsgId': 1104964116, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n没想到还是草率了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404693, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ZcEDbC/x|v1_qPd5y3z1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4370073959771032307, 'MsgSeq': 871430028}
2025-08-05 22:38:02 | INFO | 收到文本消息: 消息ID:1104964116 来自:***********@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:没想到还是草率了
2025-08-05 22:38:02 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没想到还是草率了' from wxid_x4s6k999g6qg22 in ***********@chatroom
2025-08-05 22:38:02 | DEBUG | [DouBaoImageToImage] 命令解析: ['没想到还是草率了']
2025-08-05 22:38:02 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 22:38:02 | DEBUG | 处理消息内容: '没想到还是草率了'
2025-08-05 22:38:02 | DEBUG | 消息内容 '没想到还是草率了' 不匹配任何命令，忽略
2025-08-05 22:38:13 | DEBUG | 收到消息: {'MsgId': 449555020, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="3156" length="5007" bufid="0" aeskey="4298399ab3b450fdb329641d1109123c" voiceurl="3052020100044b30490201000204a95c809d02032df927020429089324020468921761042433646538393935372d363939662d343230392d386461302d36663664666164386434363602040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600202238080525e376c31bece100" fromusername="wxid_ubbh6q832tcs21" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404705, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_na2iky2x|v1_UinL/JDU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段语音', 'NewMsgId': 1567534237489326680, 'MsgSeq': 871430029}
2025-08-05 22:38:13 | INFO | 收到语音消息: 消息ID:449555020 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="3156" length="5007" bufid="0" aeskey="4298399ab3b450fdb329641d1109123c" voiceurl="3052020100044b30490201000204a95c809d02032df927020429089324020468921761042433646538393935372d363939662d343230392d386461302d36663664666164386434363602040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600202238080525e376c31bece100" fromusername="wxid_ubbh6q832tcs21" /></msg>
2025-08-05 22:38:13 | DEBUG | [VoiceTest] 缓存语音消息 MsgId: 449555020
2025-08-05 22:38:13 | DEBUG | [VoiceTest] 缓存语音消息 NewMsgId: 1567534237489326680
2025-08-05 22:38:13 | INFO | [VoiceTest] 语音消息缓存状态: 当前缓存数量=2
2025-08-05 22:38:13 | INFO | [VoiceTest] 语音消息ID: MsgId=449555020, NewMsgId=1567534237489326680
2025-08-05 22:38:13 | DEBUG | 收到消息: {'MsgId': 2003721194, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n我也以为很好打  我已经很控制p'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404705, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_I9kUJ3u5|v1_IzgLOvLA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8116656768203452076, 'MsgSeq': 871430030}
2025-08-05 22:38:13 | INFO | 收到文本消息: 消息ID:2003721194 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:我也以为很好打  我已经很控制p
2025-08-05 22:38:13 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我也以为很好打  我已经很控制p' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:38:13 | DEBUG | [DouBaoImageToImage] 命令解析: ['我也以为很好打', '', '我已经很控制p']
2025-08-05 22:38:13 | DEBUG | 处理消息内容: '我也以为很好打  我已经很控制p'
2025-08-05 22:38:13 | DEBUG | 消息内容 '我也以为很好打  我已经很控制p' 不匹配任何命令，忽略
2025-08-05 22:38:24 | DEBUG | 收到消息: {'MsgId': 1240989820, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n不超过50有时候12个p'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404716, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_l4xQufIb|v1_90VzGNpp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5442722886423546645, 'MsgSeq': 871430031}
2025-08-05 22:38:24 | INFO | 收到文本消息: 消息ID:1240989820 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:不超过50有时候12个p
2025-08-05 22:38:24 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不超过50有时候12个p' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:38:24 | DEBUG | [DouBaoImageToImage] 命令解析: ['不超过50有时候12个p']
2025-08-05 22:38:24 | DEBUG | 处理消息内容: '不超过50有时候12个p'
2025-08-05 22:38:24 | DEBUG | 消息内容 '不超过50有时候12个p' 不匹配任何命令，忽略
2025-08-05 22:38:28 | DEBUG | 收到消息: {'MsgId': 1583904135, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n都输'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404720, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_sx28bG39|v1_ycfC/BFU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8088427657469008586, 'MsgSeq': 871430032}
2025-08-05 22:38:28 | INFO | 收到文本消息: 消息ID:1583904135 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:都输
2025-08-05 22:38:28 | DEBUG | [DouBaoImageToImage] 收到文本消息: '都输' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:38:28 | DEBUG | [DouBaoImageToImage] 命令解析: ['都输']
2025-08-05 22:38:28 | DEBUG | 处理消息内容: '都输'
2025-08-05 22:38:28 | DEBUG | 消息内容 '都输' 不匹配任何命令，忽略
2025-08-05 22:38:30 | DEBUG | 收到消息: {'MsgId': 277318293, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<msg><emoji fromusername="wxid_c3jkq1ylevnb12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="771055217bf0c0d8c5b1831074588f2e" len="2756441" productid="" androidmd5="771055217bf0c0d8c5b1831074588f2e" androidlen="2756441" s60v3md5="771055217bf0c0d8c5b1831074588f2e" s60v3len="2756441" s60v5md5="771055217bf0c0d8c5b1831074588f2e" s60v5len="2756441" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=771055217bf0c0d8c5b1831074588f2e&amp;filekey=30440201010430302e02016e0402535a0420373731303535323137626630633064386335623138333130373435383866326502032a0f59040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f00064fe561bf27bc0000006e01004fb1535a24267bc1e6dd7c75c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=3ef3f4ada792e12ab819f3e45d1f7f06&amp;filekey=30440201010430302e02016e0402535a0420336566336634616461373932653132616238313966336534356431663766303602032a0f60040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f0008ff0a61bf27bc0000006e02004fb2535a24267bc1e6dd7c786&amp;ef=2&amp;bizid=1022" aeskey="fc4006a48e6f4385af52021c4ad9d113" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=05ccfbfdf6d3c65e95974a6e9c5c7b4b&amp;filekey=30440201010430302e02016e0402535a04203035636366626664663664336336356539353937346136653963356337623462020301f140040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f000bf49261bf27bc0000006e03004fb3535a24267bc1e6dd7c7b9&amp;ef=3&amp;bizid=1022" externmd5="5e679291c174c052a3ab4cbb7d12a2d6" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404722, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_4c9rzSQK|v1_AoxUz0Ei</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1289222815319311064, 'MsgSeq': 871430033}
2025-08-05 22:38:30 | INFO | 收到表情消息: 消息ID:277318293 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 MD5:771055217bf0c0d8c5b1831074588f2e 大小:2756441
2025-08-05 22:38:30 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1289222815319311064
2025-08-05 22:38:32 | DEBUG | 收到消息: {'MsgId': 1023195506, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n我就没输'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404723, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Kbr5C3vm|v1_qTq10yo+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8935043265331310505, 'MsgSeq': 871430034}
2025-08-05 22:38:32 | INFO | 收到文本消息: 消息ID:1023195506 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:我就没输
2025-08-05 22:38:32 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我就没输' from tianen532965049 in ***********@chatroom
2025-08-05 22:38:32 | DEBUG | [DouBaoImageToImage] 命令解析: ['我就没输']
2025-08-05 22:38:32 | DEBUG | 处理消息内容: '我就没输'
2025-08-05 22:38:32 | DEBUG | 消息内容 '我就没输' 不匹配任何命令，忽略
2025-08-05 22:38:34 | DEBUG | 收到消息: {'MsgId': 609977855, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>1567534237489326680</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:3156:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754404704</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404726, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>fb824753397de94298231be93948476d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_pUhOX377|v1_rywyQXUV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': 1861264362907433348, 'MsgSeq': 871430035}
2025-08-05 22:38:34 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 22:38:34 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:38:34 | INFO | 收到引用消息: 消息ID:609977855 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 22:38:34 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:38:34 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 22:38:34 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 22:38:34 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:38:34 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:38:34 | INFO |   - 消息内容: 跟着唱
2025-08-05 22:38:34 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 22:38:34 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 22:38:34 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:3156:0\n', 'Msgid': '1567534237489326680', 'NewMsgId': '1567534237489326680', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754404704', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 22:38:34 | INFO |   - 引用消息ID: 
2025-08-05 22:38:34 | INFO |   - 引用消息类型: 
2025-08-05 22:38:34 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:3156:0

2025-08-05 22:38:34 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 22:38:34 | INFO | [VoiceTest] 尝试获取引用语音: quote_msg_id=1567534237489326680
2025-08-05 22:38:34 | INFO | [VoiceTest] 当前缓存键列表: ['449555020', '1567534237489326680']
2025-08-05 22:38:35 | ERROR | [VoiceTest] 处理引用语音消息异常: Extra data: line 1 column 69 (char 68)
2025-08-05 22:38:35 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 处理引用语音时出错
2025-08-05 22:38:36 | DEBUG | 收到消息: {'MsgId': 1754402402, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<sysmsg type="functionmsg">\n<functionmsg>\n<cgi>/cgi-bin/micromsg-bin/pullfunctionmsg</cgi>\n<cmdid>614</cmdid>\n<functionmsgid>MMKANYIKAN_JINGXUANREDDOT_1754402700</functionmsgid>\n<retryinterval>150</retryinterval>\n<retrycount>3</retrycount>\n<custombuff>YIqkAXIKMTc1NDQwMjcwMHoGd2VpeGluigEkTU1LQU5ZSUtBTl9KSU5HWFVBTlJFRERPVF8xNzU0NDAyNzAwqgFsaWFSbXV1WUZEVFBnVzBEZFZKZlpJOGlEZUhQM3Z1QzZJbGdWL0tZS2RWY2plOWZQNkoxV1VHVFhVL1BTMHlTSHBNQnRRSEovcGJVdXRwR3hVMzJDbVNSYkRkQnU0SDNBd2owVFQxUjlzTGs9</custombuff>\n<businessid>21002</businessid>\n<actiontime>1754402402</actiontime>\n<functionmsgversion>2</functionmsgversion>\n</functionmsg>\n</sysmsg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404725, 'NewMsgId': 1754402402, 'MsgSeq': 0}
2025-08-05 22:38:36 | DEBUG | 系统消息类型: functionmsg
2025-08-05 22:38:36 | INFO | 未知的系统消息类型: {'MsgId': 1754402402, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<sysmsg type="functionmsg">\n<functionmsg>\n<cgi>/cgi-bin/micromsg-bin/pullfunctionmsg</cgi>\n<cmdid>614</cmdid>\n<functionmsgid>MMKANYIKAN_JINGXUANREDDOT_1754402700</functionmsgid>\n<retryinterval>150</retryinterval>\n<retrycount>3</retrycount>\n<custombuff>YIqkAXIKMTc1NDQwMjcwMHoGd2VpeGluigEkTU1LQU5ZSUtBTl9KSU5HWFVBTlJFRERPVF8xNzU0NDAyNzAwqgFsaWFSbXV1WUZEVFBnVzBEZFZKZlpJOGlEZUhQM3Z1QzZJbGdWL0tZS2RWY2plOWZQNkoxV1VHVFhVL1BTMHlTSHBNQnRRSEovcGJVdXRwR3hVMzJDbVNSYkRkQnU0SDNBd2owVFQxUjlzTGs9</custombuff>\n<businessid>21002</businessid>\n<actiontime>1754402402</actiontime>\n<functionmsgversion>2</functionmsgversion>\n</functionmsg>\n</sysmsg>', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404725, 'NewMsgId': 1754402402, 'MsgSeq': 0, 'FromWxid': 'weixin', 'SenderWxid': 'weixin', 'IsGroup': False}
2025-08-05 22:38:44 | DEBUG | 收到消息: {'MsgId': 221644600, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n那是不可能的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404736, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_qLVLC8y8|v1_qe7pUVOr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3849104034359439751, 'MsgSeq': 871430038}
2025-08-05 22:38:44 | INFO | 收到文本消息: 消息ID:221644600 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:那是不可能的
2025-08-05 22:38:44 | DEBUG | [DouBaoImageToImage] 收到文本消息: '那是不可能的' from tianen532965049 in ***********@chatroom
2025-08-05 22:38:44 | DEBUG | [DouBaoImageToImage] 命令解析: ['那是不可能的']
2025-08-05 22:38:44 | DEBUG | 处理消息内容: '那是不可能的'
2025-08-05 22:38:44 | DEBUG | 消息内容 '那是不可能的' 不匹配任何命令，忽略
2025-08-05 22:38:48 | DEBUG | 收到消息: {'MsgId': 1753821950, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>你好会杀人</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8935043265331310505</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>tianen532965049</chatusr>\n\t\t\t<displayname>饿飞</displayname>\n\t\t\t<content>我就没输</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777843237&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_YGhllukS|v1_ATNn6S90&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754404723</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404740, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>ca7a7dc58d238c0ec76c97577866e005_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_9gHJhKqL|v1_T4A/k37k</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1372611232297047691, 'MsgSeq': 871430039}
2025-08-05 22:38:48 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-05 22:38:48 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:38:48 | INFO | 收到引用消息: 消息ID:1753821950 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 内容:你好会杀人 引用类型:1
2025-08-05 22:38:48 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:38:48 | INFO | [DouBaoImageToImage] 消息内容: '你好会杀人' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:38:48 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['你好会杀人']
2025-08-05 22:38:48 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:38:48 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:38:48 | INFO |   - 消息内容: 你好会杀人
2025-08-05 22:38:48 | INFO |   - 群组ID: ***********@chatroom
2025-08-05 22:38:48 | INFO |   - 发送人: wxid_c3jkq1ylevnb12
2025-08-05 22:38:48 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '我就没输', 'Msgid': '8935043265331310505', 'NewMsgId': '8935043265331310505', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '饿飞', 'MsgSource': '<msgsource><sequence_id>777843237</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_YGhllukS|v1_ATNn6S90</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754404723', 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-05 22:38:48 | INFO |   - 引用消息ID: 
2025-08-05 22:38:48 | INFO |   - 引用消息类型: 
2025-08-05 22:38:48 | INFO |   - 引用消息内容: 我就没输
2025-08-05 22:38:48 | INFO |   - 引用消息发送人: wxid_c3jkq1ylevnb12
2025-08-05 22:39:05 | DEBUG | 收到消息: {'MsgId': 1952954980, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="2dd5ce1060d1524d74f7b38ac8568371" len = "212274" productid="" androidmd5="2dd5ce1060d1524d74f7b38ac8568371" androidlen="212274" s60v3md5 = "2dd5ce1060d1524d74f7b38ac8568371" s60v3len="212274" s60v5md5 = "2dd5ce1060d1524d74f7b38ac8568371" s60v5len="212274" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=2dd5ce1060d1524d74f7b38ac8568371&amp;filekey=30440201010430302e02016e04025348042032646435636531303630643135323464373466376233386163383536383337310203033d32040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680afd68000491d73f79cf860000006e01004fb153482f435b00b71af4553&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=a12a3d5f84a140867e3ee220cf0929bf&amp;filekey=30440201010430302e02016e04025348042061313261336435663834613134303836376533656532323063663039323962660203033d40040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680afd68000576853f79cf860000006e02004fb253482f435b00b71af4568&amp;ef=2&amp;bizid=1022" aeskey= "45930c115c424b9d8110b2892033ca93" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0cbeca7c73b9339f4d9363392c675c18&amp;filekey=30440201010430302e02016e0402534804203063626563613763373362393333396634643933363333393263363735633138020300b260040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680afd680006607e3f79cf860000006e03004fb353482f435b00b71af4579&amp;ef=3&amp;bizid=1022" externmd5 = "904cc423d71642a9912005d010ab56b5" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404756, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_pJ53lwxU|v1_EZYoT5zv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2024441761278122037, 'MsgSeq': 871430040}
2025-08-05 22:39:05 | INFO | 收到表情消息: 消息ID:1952954980 来自:***********@chatroom 发送人:tianen532965049 MD5:2dd5ce1060d1524d74f7b38ac8568371 大小:212274
2025-08-05 22:39:05 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2024441761278122037
2025-08-05 22:39:09 | DEBUG | 收到消息: {'MsgId': 727462163, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_vuywamzgu2z012:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>6赢3</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>6971760132515393623</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_x4s6k999g6qg22</chatusr>\n\t\t\t<displayname>栀栀</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;0&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_wdykTorh|v1_6bDxdE05&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n单排10把赢2把</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754404630</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t\t<listenItem>null</listenItem>\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_vuywamzgu2z012</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404761, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>e85113cfdc05101330481dc4413a5b8b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_/mu3SnSa|v1_miGcYOxH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5037459167104017632, 'MsgSeq': 871430041}
2025-08-05 22:39:09 | DEBUG | 从群聊消息中提取发送者: wxid_vuywamzgu2z012
2025-08-05 22:39:09 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:39:09 | INFO | 收到引用消息: 消息ID:727462163 来自:***********@chatroom 发送人:wxid_vuywamzgu2z012 内容:6赢3 引用类型:1
2025-08-05 22:39:09 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:39:09 | INFO | [DouBaoImageToImage] 消息内容: '6赢3' from wxid_vuywamzgu2z012 in ***********@chatroom
2025-08-05 22:39:09 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['6赢3']
2025-08-05 22:39:09 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:39:09 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:39:09 | INFO |   - 消息内容: 6赢3
2025-08-05 22:39:09 | INFO |   - 群组ID: ***********@chatroom
2025-08-05 22:39:09 | INFO |   - 发送人: wxid_vuywamzgu2z012
2025-08-05 22:39:09 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n单排10把赢2把', 'Msgid': '6971760132515393623', 'NewMsgId': '6971760132515393623', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '栀栀', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_wdykTorh|v1_6bDxdE05</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754404630', 'SenderWxid': 'wxid_vuywamzgu2z012'}
2025-08-05 22:39:09 | INFO |   - 引用消息ID: 
2025-08-05 22:39:09 | INFO |   - 引用消息类型: 
2025-08-05 22:39:09 | INFO |   - 引用消息内容: 
单排10把赢2把
2025-08-05 22:39:09 | INFO |   - 引用消息发送人: wxid_vuywamzgu2z012
2025-08-05 22:39:10 | DEBUG | 收到消息: {'MsgId': 62943729, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n他比一般人牛逼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404762, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_PJynbNpL|v1_OTjfvR4G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 244707945313694459, 'MsgSeq': 871430042}
2025-08-05 22:39:10 | INFO | 收到文本消息: 消息ID:62943729 来自:***********@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:他比一般人牛逼
2025-08-05 22:39:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '他比一般人牛逼' from wxid_x4s6k999g6qg22 in ***********@chatroom
2025-08-05 22:39:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['他比一般人牛逼']
2025-08-05 22:39:10 | DEBUG | 处理消息内容: '他比一般人牛逼'
2025-08-05 22:39:10 | DEBUG | 消息内容 '他比一般人牛逼' 不匹配任何命令，忽略
2025-08-05 22:39:12 | DEBUG | 收到消息: {'MsgId': 950399885, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_vuywamzgu2z012:\n<msg><emoji fromusername = "wxid_vuywamzgu2z012" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="314db052c1847c0b51794ce3eff22482" len = "5167" productid="" androidmd5="314db052c1847c0b51794ce3eff22482" androidlen="5167" s60v3md5 = "314db052c1847c0b51794ce3eff22482" s60v3len="5167" s60v5md5 = "314db052c1847c0b51794ce3eff22482" s60v5len="5167" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=314db052c1847c0b51794ce3eff22482&amp;filekey=30340201010420301e02020106040253480410314db052c1847c0b51794ce3eff224820202142f040d00000004627466730000000132&amp;hy=SH&amp;storeid=26303a1b5000ee6ad950c9c370000010600004f50534828034b00b6d05aeac&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=30521179addfd3e55a1a8baeb38feff6&amp;filekey=30340201010420301e0202010604025348041030521179addfd3e55a1a8baeb38feff602021430040d00000004627466730000000132&amp;hy=SH&amp;storeid=26303a1b60000df53950c9c370000010600004f5053481bd34b00b6cfd4d17&amp;bizid=1023" aeskey= "1cd9b41a5bdf1d6ba4d514bdc1e24df4" externurl = "" externmd5 = "" width= "80" height= "80" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404762, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Av60MmVm|v1_iAClAyiU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8436793139613805379, 'MsgSeq': 871430043}
2025-08-05 22:39:12 | INFO | 收到表情消息: 消息ID:950399885 来自:***********@chatroom 发送人:wxid_vuywamzgu2z012 MD5:314db052c1847c0b51794ce3eff22482 大小:5167
2025-08-05 22:39:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8436793139613805379
2025-08-05 22:39:21 | DEBUG | 收到消息: {'MsgId': 1563356148, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_vuywamzgu2z012:\n可能让我碰上了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404772, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_hi9sl2XO|v1_BfdVm/hb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3628324536595991083, 'MsgSeq': 871430044}
2025-08-05 22:39:21 | INFO | 收到文本消息: 消息ID:1563356148 来自:***********@chatroom 发送人:wxid_vuywamzgu2z012 @:[] 内容:可能让我碰上了
2025-08-05 22:39:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '可能让我碰上了' from wxid_vuywamzgu2z012 in ***********@chatroom
2025-08-05 22:39:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['可能让我碰上了']
2025-08-05 22:39:21 | DEBUG | 处理消息内容: '可能让我碰上了'
2025-08-05 22:39:21 | DEBUG | 消息内容 '可能让我碰上了' 不匹配任何命令，忽略
2025-08-05 22:39:23 | DEBUG | 收到消息: {'MsgId': 76357597, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<msg><emoji fromusername="wxid_c3jkq1ylevnb12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="771055217bf0c0d8c5b1831074588f2e" len="2756441" productid="" androidmd5="771055217bf0c0d8c5b1831074588f2e" androidlen="2756441" s60v3md5="771055217bf0c0d8c5b1831074588f2e" s60v3len="2756441" s60v5md5="771055217bf0c0d8c5b1831074588f2e" s60v5len="2756441" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=771055217bf0c0d8c5b1831074588f2e&amp;filekey=30440201010430302e02016e0402535a0420373731303535323137626630633064386335623138333130373435383866326502032a0f59040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f00064fe561bf27bc0000006e01004fb1535a24267bc1e6dd7c75c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=3ef3f4ada792e12ab819f3e45d1f7f06&amp;filekey=30440201010430302e02016e0402535a0420336566336634616461373932653132616238313966336534356431663766303602032a0f60040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f0008ff0a61bf27bc0000006e02004fb2535a24267bc1e6dd7c786&amp;ef=2&amp;bizid=1022" aeskey="fc4006a48e6f4385af52021c4ad9d113" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=05ccfbfdf6d3c65e95974a6e9c5c7b4b&amp;filekey=30440201010430302e02016e0402535a04203035636366626664663664336336356539353937346136653963356337623462020301f140040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f000bf49261bf27bc0000006e03004fb3535a24267bc1e6dd7c7b9&amp;ef=3&amp;bizid=1022" externmd5="5e679291c174c052a3ab4cbb7d12a2d6" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404772, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_nDPZJqEg|v1_IbdEh7He</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6258596223149165295, 'MsgSeq': 871430045}
2025-08-05 22:39:23 | INFO | 收到表情消息: 消息ID:76357597 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 MD5:771055217bf0c0d8c5b1831074588f2e 大小:2756441
2025-08-05 22:39:23 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6258596223149165295
2025-08-05 22:39:23 | DEBUG | 收到消息: {'MsgId': 1736964355, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n他系煮包'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404773, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_9Gjdc0j+|v1_yaSUyax+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6011696338989297760, 'MsgSeq': 871430046}
2025-08-05 22:39:23 | INFO | 收到文本消息: 消息ID:1736964355 来自:***********@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:他系煮包
2025-08-05 22:39:23 | DEBUG | [DouBaoImageToImage] 收到文本消息: '他系煮包' from wxid_x4s6k999g6qg22 in ***********@chatroom
2025-08-05 22:39:23 | DEBUG | [DouBaoImageToImage] 命令解析: ['他系煮包']
2025-08-05 22:39:23 | DEBUG | 处理消息内容: '他系煮包'
2025-08-05 22:39:23 | DEBUG | 消息内容 '他系煮包' 不匹配任何命令，忽略
2025-08-05 22:39:26 | DEBUG | 收到消息: {'MsgId': 1742291252, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n我的心'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404778, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_EfVQMSzZ|v1_DJ0dLymX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7997739755330559124, 'MsgSeq': 871430047}
2025-08-05 22:39:26 | INFO | 收到文本消息: 消息ID:1742291252 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:我的心
2025-08-05 22:39:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我的心' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:39:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['我的心']
2025-08-05 22:39:26 | DEBUG | 处理消息内容: '我的心'
2025-08-05 22:39:26 | DEBUG | 消息内容 '我的心' 不匹配任何命令，忽略
2025-08-05 22:39:28 | DEBUG | 收到消息: {'MsgId': 1394059888, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n哇凉哇凉的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404780, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_BpA4OX5U|v1_nClyGqMc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2067351655430115204, 'MsgSeq': 871430048}
2025-08-05 22:39:28 | INFO | 收到文本消息: 消息ID:1394059888 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:哇凉哇凉的
2025-08-05 22:39:28 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哇凉哇凉的' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:39:28 | DEBUG | [DouBaoImageToImage] 命令解析: ['哇凉哇凉的']
2025-08-05 22:39:28 | DEBUG | 处理消息内容: '哇凉哇凉的'
2025-08-05 22:39:28 | DEBUG | 消息内容 '哇凉哇凉的' 不匹配任何命令，忽略
2025-08-05 22:39:31 | DEBUG | 收到消息: {'MsgId': 133373815, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n就像玻璃碎片'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404782, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_32UAzX/D|v1_la4ssrN5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2035886893398873104, 'MsgSeq': 871430049}
2025-08-05 22:39:31 | INFO | 收到文本消息: 消息ID:133373815 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:就像玻璃碎片
2025-08-05 22:39:31 | DEBUG | [DouBaoImageToImage] 收到文本消息: '就像玻璃碎片' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:39:31 | DEBUG | [DouBaoImageToImage] 命令解析: ['就像玻璃碎片']
2025-08-05 22:39:31 | DEBUG | 处理消息内容: '就像玻璃碎片'
2025-08-05 22:39:31 | DEBUG | 消息内容 '就像玻璃碎片' 不匹配任何命令，忽略
2025-08-05 22:39:36 | DEBUG | 收到消息: {'MsgId': 1674776043, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n我还碰到两次我们团的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404788, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_UvTFOlKd|v1_TMO8iap8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 173943737349449971, 'MsgSeq': 871430050}
2025-08-05 22:39:36 | INFO | 收到文本消息: 消息ID:1674776043 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:我还碰到两次我们团的
2025-08-05 22:39:36 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我还碰到两次我们团的' from tianen532965049 in ***********@chatroom
2025-08-05 22:39:36 | DEBUG | [DouBaoImageToImage] 命令解析: ['我还碰到两次我们团的']
2025-08-05 22:39:36 | DEBUG | 处理消息内容: '我还碰到两次我们团的'
2025-08-05 22:39:36 | DEBUG | 消息内容 '我还碰到两次我们团的' 不匹配任何命令，忽略
2025-08-05 22:39:39 | DEBUG | 收到消息: {'MsgId': 235616384, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<msg><emoji fromusername="wxid_x4s6k999g6qg22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="237735de5ee54c9f2b29741c9a4d6005" len="607297" productid="" androidmd5="237735de5ee54c9f2b29741c9a4d6005" androidlen="607297" s60v3md5="237735de5ee54c9f2b29741c9a4d6005" s60v3len="607297" s60v5md5="237735de5ee54c9f2b29741c9a4d6005" s60v5len="607297" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=237735de5ee54c9f2b29741c9a4d6005&amp;filekey=30350201010421301f02020106040253480410237735de5ee54c9f2b29741c9a4d60050203094441040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333034343533333030306165643136303030303030303063386464613030623030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=674a7f762989334ddaa2c4538b7508bf&amp;filekey=30350201010421301f02020106040253480410674a7f762989334ddaa2c4538b7508bf0203094450040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333034343533343030303563376131303030303030303064623564393930623030303030313036&amp;bizid=1023" aeskey="c3d9bbac07fa1425d8abbe616fe9b7d3" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=bb606606127a74668bf46d3d289146f4&amp;filekey=30350201010421301f02020106040253480410bb606606127a74668bf46d3d289146f40203020c00040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831333034343533343030306331313963303030303030303039656464613030623030303030313036&amp;bizid=1023" externmd5="5d9a0f014b8bd6bb036888206147b0cf" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404789, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_EkWsShvw|v1_YQFJqZKv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8776288697697712409, 'MsgSeq': 871430051}
2025-08-05 22:39:39 | INFO | 收到表情消息: 消息ID:235616384 来自:***********@chatroom 发送人:wxid_x4s6k999g6qg22 MD5:237735de5ee54c9f2b29741c9a4d6005 大小:607297
2025-08-05 22:39:39 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8776288697697712409
2025-08-05 22:39:41 | DEBUG | 收到消息: {'MsgId': 264936685, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="dc8439fe4537b0472e844ccab8422c70" len = "367061" productid="" androidmd5="dc8439fe4537b0472e844ccab8422c70" androidlen="367061" s60v3md5 = "dc8439fe4537b0472e844ccab8422c70" s60v3len="367061" s60v5md5 = "dc8439fe4537b0472e844ccab8422c70" s60v5len="367061" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=dc8439fe4537b0472e844ccab8422c70&amp;filekey=30350201010421301f020201060402535a0410dc8439fe4537b0472e844ccab8422c7002030599d5040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f26660000baa91ccfef41d0000010600004f50535a2c863bc1e786d4cfe&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=809f312713a09fe8d034b9564e7e7f5e&amp;filekey=30350201010421301f020201060402535a0410809f312713a09fe8d034b9564e7e7f5e02030599e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f26660000e8c3cccfef41d0000010600004f50535a26267bc1e7e006ddd&amp;bizid=1023" aeskey= "c9460931467dfa2c505f01fcc133ff99" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=4aea69e6407a246b54f3db2303e0d0b6&amp;filekey=30350201010421301f020201060402535a04104aea69e6407a246b54f3db2303e0d0b6020301d6d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f2a6b00003fdba153237b40000010600004f50535a2134b88096734fc87&amp;bizid=1023" externmd5 = "5079bddab4225575c258e8b98b98ab6c" width= "111" height= "81" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404793, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_XVwJ60s1|v1_YtVBqyFm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1044041224967008289, 'MsgSeq': 871430052}
2025-08-05 22:39:41 | INFO | 收到表情消息: 消息ID:264936685 来自:***********@chatroom 发送人:tianen532965049 MD5:dc8439fe4537b0472e844ccab8422c70 大小:367061
2025-08-05 22:39:41 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1044041224967008289
2025-08-05 22:40:18 | DEBUG | 收到消息: {'MsgId': 1784659542, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n我以为我能单排征服排位  没想到被排位给征服'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404829, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_O+0YnvqZ|v1_so4KiZTo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2022696590114952997, 'MsgSeq': 871430053}
2025-08-05 22:40:18 | INFO | 收到文本消息: 消息ID:1784659542 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:我以为我能单排征服排位  没想到被排位给征服
2025-08-05 22:40:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我以为我能单排征服排位  没想到被排位给征服' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:40:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['我以为我能单排征服排位', '', '没想到被排位给征服']
2025-08-05 22:40:18 | DEBUG | 处理消息内容: '我以为我能单排征服排位  没想到被排位给征服'
2025-08-05 22:40:18 | DEBUG | 消息内容 '我以为我能单排征服排位  没想到被排位给征服' 不匹配任何命令，忽略
2025-08-05 22:40:23 | DEBUG | 收到消息: {'MsgId': 1113791562, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>放水了没</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>173943737349449971</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>tianen532965049</chatusr>\n\t\t\t<displayname>饿飞</displayname>\n\t\t\t<content>我还碰到两次我们团的</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;810053467&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_/3S46Wcz|v1_8f4Skd44&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754404788</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_x4s6k999g6qg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404835, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>aed41074db7765a03a4c772dabd47260_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_OeqZTCrs|v1_VmMKzehq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3443574515602087688, 'MsgSeq': 871430054}
2025-08-05 22:40:23 | DEBUG | 从群聊消息中提取发送者: wxid_x4s6k999g6qg22
2025-08-05 22:40:23 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:40:23 | INFO | 收到引用消息: 消息ID:1113791562 来自:***********@chatroom 发送人:wxid_x4s6k999g6qg22 内容:放水了没 引用类型:1
2025-08-05 22:40:23 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:40:23 | INFO | [DouBaoImageToImage] 消息内容: '放水了没' from wxid_x4s6k999g6qg22 in ***********@chatroom
2025-08-05 22:40:23 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['放水了没']
2025-08-05 22:40:23 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:40:23 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:40:23 | INFO |   - 消息内容: 放水了没
2025-08-05 22:40:23 | INFO |   - 群组ID: ***********@chatroom
2025-08-05 22:40:23 | INFO |   - 发送人: wxid_x4s6k999g6qg22
2025-08-05 22:40:23 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '我还碰到两次我们团的', 'Msgid': '173943737349449971', 'NewMsgId': '173943737349449971', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '饿飞', 'MsgSource': '<msgsource><sequence_id>810053467</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_/3S46Wcz|v1_8f4Skd44</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754404788', 'SenderWxid': 'wxid_x4s6k999g6qg22'}
2025-08-05 22:40:23 | INFO |   - 引用消息ID: 
2025-08-05 22:40:23 | INFO |   - 引用消息类型: 
2025-08-05 22:40:23 | INFO |   - 引用消息内容: 我还碰到两次我们团的
2025-08-05 22:40:23 | INFO |   - 引用消息发送人: wxid_x4s6k999g6qg22
2025-08-05 22:40:55 | DEBUG | 收到消息: {'MsgId': 1733909640, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n这就好比被截胡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404866, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_/IboumoB|v1_PwQyRo4Z</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6060152371673112905, 'MsgSeq': 871430055}
2025-08-05 22:40:55 | INFO | 收到文本消息: 消息ID:1733909640 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:这就好比被截胡
2025-08-05 22:40:55 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这就好比被截胡' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:40:55 | DEBUG | [DouBaoImageToImage] 命令解析: ['这就好比被截胡']
2025-08-05 22:40:55 | DEBUG | 处理消息内容: '这就好比被截胡'
2025-08-05 22:40:55 | DEBUG | 消息内容 '这就好比被截胡' 不匹配任何命令，忽略
2025-08-05 22:41:11 | DEBUG | 收到消息: {'MsgId': 1202361891, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<msg><emoji fromusername="wxid_c3jkq1ylevnb12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="66cb469ba338b002a2c71637559af077" len="29592" productid="com.tencent.xin.emoticon.person.stiker_157829801139bdab97565ddb27" androidmd5="66cb469ba338b002a2c71637559af077" androidlen="29592" s60v3md5="66cb469ba338b002a2c71637559af077" s60v3len="29592" s60v5md5="66cb469ba338b002a2c71637559af077" s60v5len="29592" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=66cb469ba338b002a2c71637559af077&amp;filekey=30340201010420301e020201060402535a041066cb469ba338b002a2c71637559af07702027398040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363131303031343030303165643562356161666237343066623135356630393030303030313036&amp;bizid=1023" designerid="" thumburl="http://mmbiz.qpic.cn/mmemoticon/Q3auHgzwzM77fkVv3h31dVAlYAHv83eiazVdb4lUXRSalp2YCmibNYw5eCNvSeH4h4/0" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=e01f6ea8aa6c4a215d0ce4a5c6c65b31&amp;filekey=30340201010420301e020201060402535a0410e01f6ea8aa6c4a215d0ce4a5c6c65b31020273a0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363131303031343030303437633330356161666237343061343239353630393030303030313036&amp;bizid=1023" aeskey="46e88f523caa3c26143c858262d6d220" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=1da4751e5873e7071fe800f293a59b2a&amp;filekey=30340201010420301e020201060402535a04101da4751e5873e7071fe800f293a59b2a020216f0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363131303031343030303562646663356161666237343061313466356630393030303030313036&amp;bizid=1023" externmd5="3a0e3982cbdb4c8b6dc05fd2a3de4fa4" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="CgwKBXpoX2NuEgPlk60KCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA="></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404882, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_biFAgJnm|v1_orgmWpPo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5265918352171183821, 'MsgSeq': 871430056}
2025-08-05 22:41:11 | INFO | 收到表情消息: 消息ID:1202361891 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 MD5:66cb469ba338b002a2c71637559af077 大小:29592
2025-08-05 22:41:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5265918352171183821
2025-08-05 22:41:48 | DEBUG | 收到消息: {'MsgId': 1933116079, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n13幺被屁胡截了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404920, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_0tiVSc2I|v1_HCElmgiW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5887133308048179292, 'MsgSeq': 871430057}
2025-08-05 22:41:48 | INFO | 收到文本消息: 消息ID:1933116079 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:13幺被屁胡截了
2025-08-05 22:41:48 | DEBUG | [DouBaoImageToImage] 收到文本消息: '13幺被屁胡截了' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:41:48 | DEBUG | [DouBaoImageToImage] 命令解析: ['13幺被屁胡截了']
2025-08-05 22:41:48 | DEBUG | 处理消息内容: '13幺被屁胡截了'
2025-08-05 22:41:48 | DEBUG | 消息内容 '13幺被屁胡截了' 不匹配任何命令，忽略
2025-08-05 22:41:54 | DEBUG | 收到消息: {'MsgId': 287773598, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<msg><emoji fromusername="wxid_c3jkq1ylevnb12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="771055217bf0c0d8c5b1831074588f2e" len="2756441" productid="" androidmd5="771055217bf0c0d8c5b1831074588f2e" androidlen="2756441" s60v3md5="771055217bf0c0d8c5b1831074588f2e" s60v3len="2756441" s60v5md5="771055217bf0c0d8c5b1831074588f2e" s60v5len="2756441" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=771055217bf0c0d8c5b1831074588f2e&amp;filekey=30440201010430302e02016e0402535a0420373731303535323137626630633064386335623138333130373435383866326502032a0f59040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f00064fe561bf27bc0000006e01004fb1535a24267bc1e6dd7c75c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=3ef3f4ada792e12ab819f3e45d1f7f06&amp;filekey=30440201010430302e02016e0402535a0420336566336634616461373932653132616238313966336534356431663766303602032a0f60040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f0008ff0a61bf27bc0000006e02004fb2535a24267bc1e6dd7c786&amp;ef=2&amp;bizid=1022" aeskey="fc4006a48e6f4385af52021c4ad9d113" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=05ccfbfdf6d3c65e95974a6e9c5c7b4b&amp;filekey=30440201010430302e02016e0402535a04203035636366626664663664336336356539353937346136653963356337623462020301f140040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f000bf49261bf27bc0000006e03004fb3535a24267bc1e6dd7c7b9&amp;ef=3&amp;bizid=1022" externmd5="5e679291c174c052a3ab4cbb7d12a2d6" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404926, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_qFewz8Sb|v1_rzIAUftJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4341297761363824303, 'MsgSeq': 871430058}
2025-08-05 22:41:54 | INFO | 收到表情消息: 消息ID:287773598 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 MD5:771055217bf0c0d8c5b1831074588f2e 大小:2756441
2025-08-05 22:41:54 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4341297761363824303
2025-08-05 22:42:04 | DEBUG | 收到消息: {'MsgId': 788496191, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n我没放 下次会放的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404936, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_x+Ycomip|v1_uS5wY3Em</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7389939973144379539, 'MsgSeq': 871430059}
2025-08-05 22:42:04 | INFO | 收到文本消息: 消息ID:788496191 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:我没放 下次会放的
2025-08-05 22:42:04 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我没放 下次会放的' from tianen532965049 in ***********@chatroom
2025-08-05 22:42:04 | DEBUG | [DouBaoImageToImage] 命令解析: ['我没放', '下次会放的']
2025-08-05 22:42:04 | DEBUG | 处理消息内容: '我没放 下次会放的'
2025-08-05 22:42:04 | DEBUG | 消息内容 '我没放 下次会放的' 不匹配任何命令，忽略
2025-08-05 22:42:07 | DEBUG | 收到消息: {'MsgId': 1529044468, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="438849a397f5a1ed0288d8177a856857" len = "87404" productid="" androidmd5="438849a397f5a1ed0288d8177a856857" androidlen="87404" s60v3md5 = "438849a397f5a1ed0288d8177a856857" s60v3len="87404" s60v5md5 = "438849a397f5a1ed0288d8177a856857" s60v5len="87404" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=438849a397f5a1ed0288d8177a856857&amp;filekey=30350201010421301f02020106040253480410438849a397f5a1ed0288d8177a856857020301556c040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630cd542000826d5000000000000010600004f5053480e267b40b78d4ce32&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=27b710f75b7d0b53357e29e138211610&amp;filekey=30350201010421301f020201060402535a041027b710f75b7d0b53357e29e1382116100203015570040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2630cd542000de7e5000000000000010600004f50535a01c278809676a5b8d&amp;bizid=1023" aeskey= "82825e0be1ff1c3fa763f9e6f59bce1b" externurl = "" externmd5 = "" width= "640" height= "172" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404939, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_tZk+rKLU|v1_dVUWrl0s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9050563383286693921, 'MsgSeq': 871430060}
2025-08-05 22:42:07 | INFO | 收到表情消息: 消息ID:1529044468 来自:***********@chatroom 发送人:tianen532965049 MD5:438849a397f5a1ed0288d8177a856857 大小:87404
2025-08-05 22:42:07 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9050563383286693921
2025-08-05 22:42:24 | DEBUG | 收到消息: {'MsgId': 1000392218, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我怀疑我遇到的就是你</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>7389939973144379539</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>tianen532965049</chatusr>\n\t\t\t<displayname>饿飞</displayname>\n\t\t\t<content>我没放 下次会放的</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777843272&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_47QwqRKX|v1_33367V2/&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754404936</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404956, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>830bfccdf0dca393305625673bf7e3e2_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Lc02Bde6|v1_4rxsLQMt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5570099676904130789, 'MsgSeq': 871430061}
2025-08-05 22:42:24 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-05 22:42:24 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:42:24 | INFO | 收到引用消息: 消息ID:1000392218 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 内容:我怀疑我遇到的就是你 引用类型:1
2025-08-05 22:42:24 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:42:24 | INFO | [DouBaoImageToImage] 消息内容: '我怀疑我遇到的就是你' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:42:24 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['我怀疑我遇到的就是你']
2025-08-05 22:42:24 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:42:24 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:42:24 | INFO |   - 消息内容: 我怀疑我遇到的就是你
2025-08-05 22:42:24 | INFO |   - 群组ID: ***********@chatroom
2025-08-05 22:42:24 | INFO |   - 发送人: wxid_c3jkq1ylevnb12
2025-08-05 22:42:24 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '我没放 下次会放的', 'Msgid': '7389939973144379539', 'NewMsgId': '7389939973144379539', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '饿飞', 'MsgSource': '<msgsource><sequence_id>777843272</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_47QwqRKX|v1_33367V2/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754404936', 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-05 22:42:24 | INFO |   - 引用消息ID: 
2025-08-05 22:42:24 | INFO |   - 引用消息类型: 
2025-08-05 22:42:24 | INFO |   - 引用消息内容: 我没放 下次会放的
2025-08-05 22:42:24 | INFO |   - 引用消息发送人: wxid_c3jkq1ylevnb12
2025-08-05 22:42:30 | DEBUG | 收到消息: {'MsgId': 1534733165, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n你把我打的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404962, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_xaGIsETY|v1_KRZdQG9S</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1303861138556144173, 'MsgSeq': 871430062}
2025-08-05 22:42:30 | INFO | 收到文本消息: 消息ID:1534733165 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:你把我打的
2025-08-05 22:42:30 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你把我打的' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:42:30 | DEBUG | [DouBaoImageToImage] 命令解析: ['你把我打的']
2025-08-05 22:42:30 | DEBUG | 处理消息内容: '你把我打的'
2025-08-05 22:42:30 | DEBUG | 消息内容 '你把我打的' 不匹配任何命令，忽略
2025-08-05 22:42:37 | DEBUG | 收到消息: {'MsgId': 922976229, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n遍体鳞伤'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404968, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_52B8p1B4|v1_Z32hvZK8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2112600654338824158, 'MsgSeq': 871430063}
2025-08-05 22:42:37 | INFO | 收到文本消息: 消息ID:922976229 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:遍体鳞伤
2025-08-05 22:42:37 | DEBUG | [DouBaoImageToImage] 收到文本消息: '遍体鳞伤' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:42:37 | DEBUG | [DouBaoImageToImage] 命令解析: ['遍体鳞伤']
2025-08-05 22:42:37 | DEBUG | 处理消息内容: '遍体鳞伤'
2025-08-05 22:42:37 | DEBUG | 消息内容 '遍体鳞伤' 不匹配任何命令，忽略
2025-08-05 22:42:39 | DEBUG | 收到消息: {'MsgId': 1242646579, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n那肯定不是'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404971, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_eX5k760p|v1_rvLGCaUV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3975068804881612563, 'MsgSeq': 871430064}
2025-08-05 22:42:39 | INFO | 收到文本消息: 消息ID:1242646579 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:那肯定不是
2025-08-05 22:42:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '那肯定不是' from tianen532965049 in ***********@chatroom
2025-08-05 22:42:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['那肯定不是']
2025-08-05 22:42:39 | DEBUG | 处理消息内容: '那肯定不是'
2025-08-05 22:42:39 | DEBUG | 消息内容 '那肯定不是' 不匹配任何命令，忽略
2025-08-05 22:42:55 | DEBUG | 收到消息: {'MsgId': 1190572653, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="3a999b243965f0ffb7c6ea44a4953351" len = "969065" productid="" androidmd5="3a999b243965f0ffb7c6ea44a4953351" androidlen="969065" s60v3md5 = "3a999b243965f0ffb7c6ea44a4953351" s60v3len="969065" s60v5md5 = "3a999b243965f0ffb7c6ea44a4953351" s60v5len="969065" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3a999b243965f0ffb7c6ea44a4953351&amp;filekey=30440201010430302e02016e040253480420336139393962323433393635663066666237633665613434613439353333353102030ec969040d00000004627466730000000132&amp;hy=SH&amp;storeid=2681d67af0008f8d76094ad410000006e01004fb153481f7f617156ecd3a42&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=894bc6e459eee7ebe4c54164d7900186&amp;filekey=30440201010430302e02016e040253480420383934626336653435396565653765626534633534313634643739303031383602030ec970040d00000004627466730000000132&amp;hy=SH&amp;storeid=2681d67af000ad6746094ad410000006e02004fb253481f7f617156ecd3a59&amp;ef=2&amp;bizid=1022" aeskey= "2d623e1c6d5b43f085ff182cf53c3b59" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=28929be1da5a107a5f7c849d5677bb6a&amp;filekey=30440201010430302e02016e040253480420323839323962653164613561313037613566376338343964353637376262366102030291f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2681d67af000d10d56094ad410000006e03004fb353481f7f617156ecd3a78&amp;ef=3&amp;bizid=1022" externmd5 = "34475afb342df87d76f829f87225496a" width= "240" height= "133" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404987, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_8IqMkwJT|v1_puO1KPFw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3235871956274857055, 'MsgSeq': 871430065}
2025-08-05 22:42:55 | INFO | 收到表情消息: 消息ID:1190572653 来自:***********@chatroom 发送人:tianen532965049 MD5:3a999b243965f0ffb7c6ea44a4953351 大小:969065
2025-08-05 22:42:55 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3235871956274857055
2025-08-05 22:43:11 | DEBUG | 收到消息: {'MsgId': 939588218, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n百分之 八十'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405003, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_R+r9/T/4|v1_HTzULeG0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7748818865193007604, 'MsgSeq': 871430066}
2025-08-05 22:43:11 | INFO | 收到文本消息: 消息ID:939588218 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:百分之 八十
2025-08-05 22:43:11 | DEBUG | [DouBaoImageToImage] 收到文本消息: '百分之 八十' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:43:11 | DEBUG | [DouBaoImageToImage] 命令解析: ['百分之', '八十']
2025-08-05 22:43:11 | DEBUG | 处理消息内容: '百分之 八十'
2025-08-05 22:43:11 | DEBUG | 消息内容 '百分之 八十' 不匹配任何命令，忽略
2025-08-05 22:43:18 | DEBUG | 收到消息: {'MsgId': 2035388611, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n几率'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405010, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_wQeeXAsR|v1_AzGNhaRz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5655742939713684576, 'MsgSeq': 871430067}
2025-08-05 22:43:18 | INFO | 收到文本消息: 消息ID:2035388611 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:几率
2025-08-05 22:43:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '几率' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:43:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['几率']
2025-08-05 22:43:18 | DEBUG | 处理消息内容: '几率'
2025-08-05 22:43:18 | DEBUG | 消息内容 '几率' 不匹配任何命令，忽略
2025-08-05 22:43:41 | DEBUG | 收到消息: {'MsgId': 772747150, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n你啥名字 我回忆一下 因为有一个我认识 另一个是两个字的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405033, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_idgZukUU|v1_21y+FyH+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5676004022252085811, 'MsgSeq': 871430068}
2025-08-05 22:43:41 | INFO | 收到文本消息: 消息ID:772747150 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:你啥名字 我回忆一下 因为有一个我认识 另一个是两个字的
2025-08-05 22:43:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你啥名字 我回忆一下 因为有一个我认识 另一个是两个字的' from tianen532965049 in ***********@chatroom
2025-08-05 22:43:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['你啥名字', '我回忆一下', '因为有一个我认识 另一个是两个字的']
2025-08-05 22:43:41 | DEBUG | 处理消息内容: '你啥名字 我回忆一下 因为有一个我认识 另一个是两个字的'
2025-08-05 22:43:41 | DEBUG | 消息内容 '你啥名字 我回忆一下 因为有一个我认识 另一个是两个字的' 不匹配任何命令，忽略
2025-08-05 22:43:46 | DEBUG | 收到消息: {'MsgId': 2066426987, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="dc8439fe4537b0472e844ccab8422c70" len = "367061" productid="" androidmd5="dc8439fe4537b0472e844ccab8422c70" androidlen="367061" s60v3md5 = "dc8439fe4537b0472e844ccab8422c70" s60v3len="367061" s60v5md5 = "dc8439fe4537b0472e844ccab8422c70" s60v5len="367061" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=dc8439fe4537b0472e844ccab8422c70&amp;filekey=30350201010421301f020201060402535a0410dc8439fe4537b0472e844ccab8422c7002030599d5040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f26660000baa91ccfef41d0000010600004f50535a2c863bc1e786d4cfe&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=809f312713a09fe8d034b9564e7e7f5e&amp;filekey=30350201010421301f020201060402535a0410809f312713a09fe8d034b9564e7e7f5e02030599e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f26660000e8c3cccfef41d0000010600004f50535a26267bc1e7e006ddd&amp;bizid=1023" aeskey= "c9460931467dfa2c505f01fcc133ff99" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=4aea69e6407a246b54f3db2303e0d0b6&amp;filekey=30350201010421301f020201060402535a04104aea69e6407a246b54f3db2303e0d0b6020301d6d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f2a6b00003fdba153237b40000010600004f50535a2134b88096734fc87&amp;bizid=1023" externmd5 = "5079bddab4225575c258e8b98b98ab6c" width= "111" height= "81" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405037, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_UleG4X+q|v1_CkppGLhE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2307844416113728294, 'MsgSeq': 871430069}
2025-08-05 22:43:46 | INFO | 收到表情消息: 消息ID:2066426987 来自:***********@chatroom 发送人:tianen532965049 MD5:dc8439fe4537b0472e844ccab8422c70 大小:367061
2025-08-05 22:43:46 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2307844416113728294
2025-08-05 22:43:58 | DEBUG | 收到消息: {'MsgId': 1633814545, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<msg><emoji fromusername="wxid_c3jkq1ylevnb12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="771055217bf0c0d8c5b1831074588f2e" len="2756441" productid="" androidmd5="771055217bf0c0d8c5b1831074588f2e" androidlen="2756441" s60v3md5="771055217bf0c0d8c5b1831074588f2e" s60v3len="2756441" s60v5md5="771055217bf0c0d8c5b1831074588f2e" s60v5len="2756441" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=771055217bf0c0d8c5b1831074588f2e&amp;filekey=30440201010430302e02016e0402535a0420373731303535323137626630633064386335623138333130373435383866326502032a0f59040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f00064fe561bf27bc0000006e01004fb1535a24267bc1e6dd7c75c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=3ef3f4ada792e12ab819f3e45d1f7f06&amp;filekey=30440201010430302e02016e0402535a0420336566336634616461373932653132616238313966336534356431663766303602032a0f60040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f0008ff0a61bf27bc0000006e02004fb2535a24267bc1e6dd7c786&amp;ef=2&amp;bizid=1022" aeskey="fc4006a48e6f4385af52021c4ad9d113" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=05ccfbfdf6d3c65e95974a6e9c5c7b4b&amp;filekey=30440201010430302e02016e0402535a04203035636366626664663664336336356539353937346136653963356337623462020301f140040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f000bf49261bf27bc0000006e03004fb3535a24267bc1e6dd7c7b9&amp;ef=3&amp;bizid=1022" externmd5="5e679291c174c052a3ab4cbb7d12a2d6" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405050, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_IWbj/ruJ|v1_/1KHzmYc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7670999102637362838, 'MsgSeq': 871430070}
2025-08-05 22:43:58 | INFO | 收到表情消息: 消息ID:1633814545 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 MD5:771055217bf0c0d8c5b1831074588f2e 大小:2756441
2025-08-05 22:43:58 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7670999102637362838
2025-08-05 22:44:12 | DEBUG | 收到消息: {'MsgId': 332943414, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n你说你用啥名'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405064, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_31c9sLhL|v1_m3WMrN0i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7423523187715209937, 'MsgSeq': 871430071}
2025-08-05 22:44:12 | INFO | 收到文本消息: 消息ID:332943414 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:你说你用啥名
2025-08-05 22:44:12 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你说你用啥名' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:44:12 | DEBUG | [DouBaoImageToImage] 命令解析: ['你说你用啥名']
2025-08-05 22:44:12 | DEBUG | 处理消息内容: '你说你用啥名'
2025-08-05 22:44:12 | DEBUG | 消息内容 '你说你用啥名' 不匹配任何命令，忽略
2025-08-05 22:44:45 | DEBUG | 收到消息: {'MsgId': 463278957, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n厄斐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405097, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_XCxeTEvp|v1_AzqjFs8x</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8727057171769816587, 'MsgSeq': 871430072}
2025-08-05 22:44:45 | INFO | 收到文本消息: 消息ID:463278957 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:厄斐
2025-08-05 22:44:45 | DEBUG | [DouBaoImageToImage] 收到文本消息: '厄斐' from tianen532965049 in ***********@chatroom
2025-08-05 22:44:45 | DEBUG | [DouBaoImageToImage] 命令解析: ['厄斐']
2025-08-05 22:44:45 | DEBUG | 处理消息内容: '厄斐'
2025-08-05 22:44:45 | DEBUG | 消息内容 '厄斐' 不匹配任何命令，忽略
2025-08-05 22:45:00 | DEBUG | 收到消息: {'MsgId': 809455684, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n那不是你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405112, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_j1UvPeZy|v1_ndmQLRso</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7486686875767038817, 'MsgSeq': 871430073}
2025-08-05 22:45:00 | INFO | 收到文本消息: 消息ID:809455684 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:那不是你
2025-08-05 22:45:00 | DEBUG | [DouBaoImageToImage] 收到文本消息: '那不是你' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:45:00 | DEBUG | [DouBaoImageToImage] 命令解析: ['那不是你']
2025-08-05 22:45:00 | DEBUG | 处理消息内容: '那不是你'
2025-08-05 22:45:00 | DEBUG | 消息内容 '那不是你' 不匹配任何命令，忽略
2025-08-05 22:45:26 | DEBUG | 收到消息: {'MsgId': 1879721141, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n我们还是好团友'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405138, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_mHyYrYrO|v1_cFvSoJn7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9164966776111545171, 'MsgSeq': 871430074}
2025-08-05 22:45:26 | INFO | 收到文本消息: 消息ID:1879721141 来自:***********@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:我们还是好团友
2025-08-05 22:45:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我们还是好团友' from wxid_c3jkq1ylevnb12 in ***********@chatroom
2025-08-05 22:45:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['我们还是好团友']
2025-08-05 22:45:26 | DEBUG | 处理消息内容: '我们还是好团友'
2025-08-05 22:45:26 | DEBUG | 消息内容 '我们还是好团友' 不匹配任何命令，忽略
2025-08-05 22:45:32 | DEBUG | 收到消息: {'MsgId': 336052002, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n好好好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405144, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_nluEcbD8|v1_Unm1H7KJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2838341270860651254, 'MsgSeq': 871430075}
2025-08-05 22:45:32 | INFO | 收到文本消息: 消息ID:336052002 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:好好好
2025-08-05 22:45:32 | DEBUG | [DouBaoImageToImage] 收到文本消息: '好好好' from tianen532965049 in ***********@chatroom
2025-08-05 22:45:32 | DEBUG | [DouBaoImageToImage] 命令解析: ['好好好']
2025-08-05 22:45:32 | DEBUG | 处理消息内容: '好好好'
2025-08-05 22:45:32 | DEBUG | 消息内容 '好好好' 不匹配任何命令，忽略
2025-08-05 22:46:26 | DEBUG | 收到消息: {'MsgId': 1238314679, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5vs4g3wmimgc22:\n@QvemiY¹_慕ؓ悦ؓ˒\u2005知道你是谁了mq 但是跟你挂的是这个号不是我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405198, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_2530z9t0joek22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>15</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_sIyXdqV5|v1_BrFcR272</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 997664716729316267, 'MsgSeq': 871430076}
2025-08-05 22:46:26 | INFO | 收到文本消息: 消息ID:1238314679 来自:***********@chatroom 发送人:wxid_5vs4g3wmimgc22 @:['wxid_2530z9t0joek22'] 内容:@QvemiY¹_慕ؓ悦ؓ˒ 知道你是谁了mq 但是跟你挂的是这个号不是我
2025-08-05 22:46:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@QvemiY¹_慕ؓ悦ؓ˒ 知道你是谁了mq 但是跟你挂的是这个号不是我' from wxid_5vs4g3wmimgc22 in ***********@chatroom
2025-08-05 22:46:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['@QvemiY¹_慕ؓ悦ؓ˒\u2005知道你是谁了mq', '但是跟你挂的是这个号不是我']
2025-08-05 22:46:26 | DEBUG | 处理消息内容: '@QvemiY¹_慕ؓ悦ؓ˒ 知道你是谁了mq 但是跟你挂的是这个号不是我'
2025-08-05 22:46:26 | DEBUG | 消息内容 '@QvemiY¹_慕ؓ悦ؓ˒ 知道你是谁了mq 但是跟你挂的是这个号不是我' 不匹配任何命令，忽略
2025-08-05 22:46:35 | DEBUG | 收到消息: {'MsgId': 348928300, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_5vs4g3wmimgc22:\n<msg><emoji fromusername="wxid_5vs4g3wmimgc22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="9f46ee97b302d1d6af6f0d6a4df7f3f6" len="77274" productid="" androidmd5="9f46ee97b302d1d6af6f0d6a4df7f3f6" androidlen="77274" s60v3md5="9f46ee97b302d1d6af6f0d6a4df7f3f6" s60v3len="77274" s60v5md5="9f46ee97b302d1d6af6f0d6a4df7f3f6" s60v5len="77274" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=9f46ee97b302d1d6af6f0d6a4df7f3f6&amp;filekey=30440201010430302e02016e0402535a042039663436656539376233303264316436616636663064366134646637663366360203012dda040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267794836000cf377d97be5430000006e01004fb1535a227f2151568a97a19&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=aac017f1f09f09c5e29445a24bcd20ca&amp;filekey=30440201010430302e02016e0402535a042061616330313766316630396630396335653239343435613234626364323063610203012de0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267794836000de0bbd97be5430000006e02004fb2535a227f2151568a97a20&amp;ef=2&amp;bizid=1022" aeskey="b66827cdec26408c8d37efde0883afa3" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=f72efbd436805c54646c322a70ea4bd5&amp;filekey=3043020101042f302d02016e0402535a04206637326566626434333638303563353436343663333232613730656134626435020218a0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267794836000ebbccd97be5430000006e03004fb3535a227f2151568a97a27&amp;ef=3&amp;bizid=1022" externmd5="eebd7ed0911b0be88b2dbaa1aaf32bcf" width="300" height="274" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405207, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_gjFMgyrB|v1_FePfE2Vr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6612060103423252784, 'MsgSeq': 871430077}
2025-08-05 22:46:35 | INFO | 收到表情消息: 消息ID:348928300 来自:***********@chatroom 发送人:wxid_5vs4g3wmimgc22 MD5:9f46ee97b302d1d6af6f0d6a4df7f3f6 大小:77274
2025-08-05 22:46:35 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6612060103423252784
2025-08-05 22:46:41 | DEBUG | 收到消息: {'MsgId': 1163070632, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wxd855cafb5b488002" sdkver="0">\n\t\t<title />\n\t\t<type>8</type>\n\t\t<appattach>\n\t\t\t<totallen>227290</totallen>\n\t\t\t<emoticonmd5>5267f9f7b3039b3f2bfa6aec7d8d1d29</emoticonmd5>\n\t\t\t<attachid>0:0:5267f9f7b3039b3f2bfa6aec7d8d1d29</attachid>\n\t\t\t<cdnthumbaeskey>6b78687a6e7870746e697765636d796f</cdnthumbaeskey>\n\t\t\t<aeskey></aeskey>\n\t\t\t<cdnthumburl>3057020100044b3049020100020409c40d8f02033d14ba0204d931949d02046892195d042464333965363862372d386634332d343366642d623134322d6161316137383864333331630204052808030201000405004c53760042ffcb11</cdnthumburl>\n\t\t\t<cdnthumblength>24861</cdnthumblength>\n\t\t\t<cdnthumbwidth>240</cdnthumbwidth>\n\t\t\t<cdnthumbheight>240</cdnthumbheight>\n\t\t\t<cdnthumbmd5>89d95b87a2032993ba91bd8000582c9a</cdnthumbmd5>\n\t\t\t<emojiinfo>CiA1MjY3ZjlmN2IzMDM5YjNmMmJmYTZhZWM3ZDhkMWQyORKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NTI2N2Y5ZjdiMzAzOWIzZjJiZmE2YWVjN2Q4ZDFkMjkmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzQ4MDQxMDUyNjdmOWY3YjMwMzliM2YyYmZhNmFlYzdkOGQxZDI5MDIwMzAzNzdkYTA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2MzBmNjg3MTAwMDU3NzQ5MDAwMDAwMDAwMDAwMDEwNjAwMDA0ZjUwNTM0ODJkMzZmYjQwYjY0NTMyMTJiJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPWFmYTA3NTI0ZDYwNTIyZDE2ODhkYjYzNTRmOWRkYjhkJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM0ODA0MTBhZmEwNzUyNGQ2MDUyMmQxNjg4ZGI2MzU0ZjlkZGI4ZDAyMDMwMzc3ZTAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U0gmc3RvcmVpZD0yNjMwZjY4NzEwMDA4ODBmZDAwMDAwMDAwMDAwMDAxMDYwMDAwNGY1MDUzNDgwNWJkMmEwMGI2NDRhNWJjNyZiaXppZD0xMDIzMiA2MDBkMGMzMGZiODM3OGEzNDAyNjc4N2RiN2E1NjhlNToAQp4CaHR0cDovL3d4YXBwLnRjLnFxLmNvbS8yNjIvMjAzMDQvc3RvZG93bmxvYWQ/bT0xYTY3YTE4MjJmMjRiOGFhNzcyNjAyZjY1ZmVhNzc3MSZmaWxla2V5PTMwMzUwMjAxMDEwNDIxMzAxZjAyMDIwMTA2MDQwMjUzNDgwNDEwMWE2N2ExODIyZjI0YjhhYTc3MjYwMmY2NWZlYTc3NzEwMjAzMDE2MjYwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNIJnN0b3JlaWQ9MjYzMGY2ODcxMDAwYzBiZWYwMDAwMDAwMDAwMDAwMTA2MDAwMDRmNTA1MzQ4MTljNjViNDBiNjQ1M2NmYjUmYml6aWQ9MTAyM0ogZWQyMGU3OWQxNGI3MzlhZTZkNDJjODY0YzE1YzI5NDaCAQA=</emojiinfo>\n\t\t</appattach>\n\t\t<statextstr>GhQKEnd4ZDg1NWNhZmI1YjQ4ODAwMg==</statextstr>\n\t\t<percent>99</percent>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>15</version>\n\t\t<appname>搜狗输入法</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405213, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>4a0c1b47d77e1f83249afca2aabd2c1d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ABnTIFbI|v1_wTMz7S7T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7113192538552418151, 'MsgSeq': 871430078}
2025-08-05 22:46:41 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-05 22:46:41 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wxd855cafb5b488002" sdkver="0">
		<title />
		<type>8</type>
		<appattach>
			<totallen>227290</totallen>
			<emoticonmd5>5267f9f7b3039b3f2bfa6aec7d8d1d29</emoticonmd5>
			<attachid>0:0:5267f9f7b3039b3f2bfa6aec7d8d1d29</attachid>
			<cdnthumbaeskey>6b78687a6e7870746e697765636d796f</cdnthumbaeskey>
			<aeskey></aeskey>
			<cdnthumburl>3057020100044b3049020100020409c40d8f02033d14ba0204d931949d02046892195d042464333965363862372d386634332d343366642d623134322d6161316137383864333331630204052808030201000405004c53760042ffcb11</cdnthumburl>
			<cdnthumblength>24861</cdnthumblength>
			<cdnthumbwidth>240</cdnthumbwidth>
			<cdnthumbheight>240</cdnthumbheight>
			<cdnthumbmd5>89d95b87a2032993ba91bd8000582c9a</cdnthumbmd5>
			<emojiinfo>CiA1MjY3ZjlmN2IzMDM5YjNmMmJmYTZhZWM3ZDhkMWQyORKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NTI2N2Y5ZjdiMzAzOWIzZjJiZmE2YWVjN2Q4ZDFkMjkmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzQ4MDQxMDUyNjdmOWY3YjMwMzliM2YyYmZhNmFlYzdkOGQxZDI5MDIwMzAzNzdkYTA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2MzBmNjg3MTAwMDU3NzQ5MDAwMDAwMDAwMDAwMDEwNjAwMDA0ZjUwNTM0ODJkMzZmYjQwYjY0NTMyMTJiJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPWFmYTA3NTI0ZDYwNTIyZDE2ODhkYjYzNTRmOWRkYjhkJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM0ODA0MTBhZmEwNzUyNGQ2MDUyMmQxNjg4ZGI2MzU0ZjlkZGI4ZDAyMDMwMzc3ZTAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U0gmc3RvcmVpZD0yNjMwZjY4NzEwMDA4ODBmZDAwMDAwMDAwMDAwMDAxMDYwMDAwNGY1MDUzNDgwNWJkMmEwMGI2NDRhNWJjNyZiaXppZD0xMDIzMiA2MDBkMGMzMGZiODM3OGEzNDAyNjc4N2RiN2E1NjhlNToAQp4CaHR0cDovL3d4YXBwLnRjLnFxLmNvbS8yNjIvMjAzMDQvc3RvZG93bmxvYWQ/bT0xYTY3YTE4MjJmMjRiOGFhNzcyNjAyZjY1ZmVhNzc3MSZmaWxla2V5PTMwMzUwMjAxMDEwNDIxMzAxZjAyMDIwMTA2MDQwMjUzNDgwNDEwMWE2N2ExODIyZjI0YjhhYTc3MjYwMmY2NWZlYTc3NzEwMjAzMDE2MjYwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNIJnN0b3JlaWQ9MjYzMGY2ODcxMDAwYzBiZWYwMDAwMDAwMDAwMDAwMTA2MDAwMDRmNTA1MzQ4MTljNjViNDBiNjQ1M2NmYjUmYml6aWQ9MTAyM0ogZWQyMGU3OWQxNGI3MzlhZTZkNDJjODY0YzE1YzI5NDaCAQA=</emojiinfo>
		</appattach>
		<statextstr>GhQKEnd4ZDg1NWNhZmI1YjQ4ODAwMg==</statextstr>
		<percent>99</percent>
	</appmsg>
	<fromusername>wxid_c3jkq1ylevnb12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>15</version>
		<appname>搜狗输入法</appname>
	</appinfo>
	<commenturl />
</msg>

2025-08-05 22:46:41 | DEBUG | XML消息类型: 8
2025-08-05 22:46:41 | DEBUG | XML消息标题: None
2025-08-05 22:46:41 | DEBUG | 附件信息 totallen: 227290
2025-08-05 22:46:41 | DEBUG | 附件信息 emoticonmd5: 5267f9f7b3039b3f2bfa6aec7d8d1d29
2025-08-05 22:46:41 | DEBUG | 附件信息 attachid: 0:0:5267f9f7b3039b3f2bfa6aec7d8d1d29
2025-08-05 22:46:41 | DEBUG | 附件信息 cdnthumbaeskey: 6b78687a6e7870746e697765636d796f
2025-08-05 22:46:41 | DEBUG | 附件信息 cdnthumburl: 3057020100044b3049020100020409c40d8f02033d14ba0204d931949d02046892195d042464333965363862372d386634332d343366642d623134322d6161316137383864333331630204052808030201000405004c53760042ffcb11
2025-08-05 22:46:41 | DEBUG | 附件信息 cdnthumblength: 24861
2025-08-05 22:46:41 | DEBUG | 附件信息 cdnthumbwidth: 240
2025-08-05 22:46:41 | DEBUG | 附件信息 cdnthumbheight: 240
2025-08-05 22:46:41 | DEBUG | 附件信息 cdnthumbmd5: 89d95b87a2032993ba91bd8000582c9a
2025-08-05 22:46:41 | DEBUG | 附件信息 emojiinfo: CiA1MjY3ZjlmN2IzMDM5YjNmMmJmYTZhZWM3ZDhkMWQyORKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NTI2N2Y5ZjdiMzAzOWIzZjJiZmE2YWVjN2Q4ZDFkMjkmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzQ4MDQxMDUyNjdmOWY3YjMwMzliM2YyYmZhNmFlYzdkOGQxZDI5MDIwMzAzNzdkYTA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2MzBmNjg3MTAwMDU3NzQ5MDAwMDAwMDAwMDAwMDEwNjAwMDA0ZjUwNTM0ODJkMzZmYjQwYjY0NTMyMTJiJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPWFmYTA3NTI0ZDYwNTIyZDE2ODhkYjYzNTRmOWRkYjhkJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM0ODA0MTBhZmEwNzUyNGQ2MDUyMmQxNjg4ZGI2MzU0ZjlkZGI4ZDAyMDMwMzc3ZTAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U0gmc3RvcmVpZD0yNjMwZjY4NzEwMDA4ODBmZDAwMDAwMDAwMDAwMDAxMDYwMDAwNGY1MDUzNDgwNWJkMmEwMGI2NDRhNWJjNyZiaXppZD0xMDIzMiA2MDBkMGMzMGZiODM3OGEzNDAyNjc4N2RiN2E1NjhlNToAQp4CaHR0cDovL3d4YXBwLnRjLnFxLmNvbS8yNjIvMjAzMDQvc3RvZG93bmxvYWQ/bT0xYTY3YTE4MjJmMjRiOGFhNzcyNjAyZjY1ZmVhNzc3MSZmaWxla2V5PTMwMzUwMjAxMDEwNDIxMzAxZjAyMDIwMTA2MDQwMjUzNDgwNDEwMWE2N2ExODIyZjI0YjhhYTc3MjYwMmY2NWZlYTc3NzEwMjAzMDE2MjYwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNIJnN0b3JlaWQ9MjYzMGY2ODcxMDAwYzBiZWYwMDAwMDAwMDAwMDAwMTA2MDAwMDRmNTA1MzQ4MTljNjViNDBiNjQ1M2NmYjUmYml6aWQ9MTAyM0ogZWQyMGU3OWQxNGI3MzlhZTZkNDJjODY0YzE1YzI5NDaCAQA=
2025-08-05 22:46:41 | INFO | 未知的XML消息类型: 8
2025-08-05 22:46:41 | INFO | 消息标题: None
2025-08-05 22:46:41 | INFO | 消息描述: N/A
2025-08-05 22:46:41 | INFO | 消息URL: N/A
2025-08-05 22:46:41 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wxd855cafb5b488002" sdkver="0">
		<title />
		<type>8</type>
		<appattach>
			<totallen>227290</totallen>
			<emoticonmd5>5267f9f7b3039b3f2bfa6aec7d8d1d29</emoticonmd5>
			<attachid>0:0:5267f9f7b3039b3f2bfa6aec7d8d1d29</attachid>
			<cdnthumbaeskey>6b78687a6e7870746e697765636d796f</cdnthumbaeskey>
			<aeskey></aeskey>
			<cdnthumburl>3057020100044b3049020100020409c40d8f02033d14ba0204d931949d02046892195d042464333965363862372d386634332d343366642d623134322d6161316137383864333331630204052808030201000405004c53760042ffcb11</cdnthumburl>
			<cdnthumblength>24861</cdnthumblength>
			<cdnthumbwidth>240</cdnthumbwidth>
			<cdnthumbheight>240</cdnthumbheight>
			<cdnthumbmd5>89d95b87a2032993ba91bd8000582c9a</cdnthumbmd5>
			<emojiinfo>CiA1MjY3ZjlmN2IzMDM5YjNmMmJmYTZhZWM3ZDhkMWQyORKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NTI2N2Y5ZjdiMzAzOWIzZjJiZmE2YWVjN2Q4ZDFkMjkmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzQ4MDQxMDUyNjdmOWY3YjMwMzliM2YyYmZhNmFlYzdkOGQxZDI5MDIwMzAzNzdkYTA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2MzBmNjg3MTAwMDU3NzQ5MDAwMDAwMDAwMDAwMDEwNjAwMDA0ZjUwNTM0ODJkMzZmYjQwYjY0NTMyMTJiJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPWFmYTA3NTI0ZDYwNTIyZDE2ODhkYjYzNTRmOWRkYjhkJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM0ODA0MTBhZmEwNzUyNGQ2MDUyMmQxNjg4ZGI2MzU0ZjlkZGI4ZDAyMDMwMzc3ZTAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U0gmc3RvcmVpZD0yNjMwZjY4NzEwMDA4ODBmZDAwMDAwMDAwMDAwMDAxMDYwMDAwNGY1MDUzNDgwNWJkMmEwMGI2NDRhNWJjNyZiaXppZD0xMDIzMiA2MDBkMGMzMGZiODM3OGEzNDAyNjc4N2RiN2E1NjhlNToAQp4CaHR0cDovL3d4YXBwLnRjLnFxLmNvbS8yNjIvMjAzMDQvc3RvZG93bmxvYWQ/bT0xYTY3YTE4MjJmMjRiOGFhNzcyNjAyZjY1ZmVhNzc3MSZmaWxla2V5PTMwMzUwMjAxMDEwNDIxMzAxZjAyMDIwMTA2MDQwMjUzNDgwNDEwMWE2N2ExODIyZjI0YjhhYTc3MjYwMmY2NWZlYTc3NzEwMjAzMDE2MjYwMDQwZDAwMDAwMDA0NjI3NDY2NzMwMDAwMDAwMTMyJmh5PVNIJnN0b3JlaWQ9MjYzMGY2ODcxMDAwYzBiZWYwMDAwMDAwMDAwMDAwMTA2MDAwMDRmNTA1MzQ4MTljNjViNDBiNjQ1M2NmYjUmYml6aWQ9MTAyM0ogZWQyMGU3OWQxNGI3MzlhZTZkNDJjODY0YzE1YzI5NDaCAQA=</emojiinfo>
		</appattach>
		<statextstr>GhQKEnd4ZDg1NWNhZmI1YjQ4ODAwMg==</statextstr>
		<percent>99</percent>
	</appmsg>
	<fromusername>wxid_c3jkq1ylevnb12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>15</version>
		<appname>搜狗输入法</appname>
	</appinfo>
	<commenturl />
</msg>

2025-08-05 22:47:14 | DEBUG | 收到消息: {'MsgId': 2099906928, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_sqbkq6p122x422:\n<msg><emoji fromusername = "wxid_sqbkq6p122x422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="9c8722bb46ada87a48b8d2279a34b96b" len = "551174" productid="" androidmd5="9c8722bb46ada87a48b8d2279a34b96b" androidlen="551174" s60v3md5 = "9c8722bb46ada87a48b8d2279a34b96b" s60v3len="551174" s60v5md5 = "9c8722bb46ada87a48b8d2279a34b96b" s60v5len="551174" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=9c8722bb46ada87a48b8d2279a34b96b&amp;filekey=30440201010430302e02016e0402535a042039633837323262623436616461383761343862386432323739613334623936620203086906040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2686ee03f000e7d4997b341650000006e01004fb1535a0724a8809026e6c5b&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=bebd3789576aaaf6a5520cf861a96788&amp;filekey=30440201010430302e02016e0402535a042062656264333738393537366161616636613535323063663836316139363738380203086910040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2686ee0400000dbd297b341650000006e02004fb2535a0724a8809026e6c62&amp;ef=2&amp;bizid=1022" aeskey= "ba5f513ef385457bb566465e6197f70a" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=3f7d42bcf84ae6ea57cd52a18824a5aa&amp;filekey=30440201010430302e02016e0402535a042033663764343262636638346165366561353763643532613138383234613561610203016f50040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2686ee04000025ce597b341650000006e03004fb3535a0724a8809026e6c6d&amp;ef=3&amp;bizid=1022" externmd5 = "5dc1ae0fce894ccfff299b45fb33600a" width= "296" height= "296" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405246, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_0GdHvV3j|v1_CGlhzSbP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '大猪蹄子在群聊中发了一个表情', 'NewMsgId': 8131439038937104737, 'MsgSeq': 871430079}
2025-08-05 22:47:14 | INFO | 收到表情消息: 消息ID:2099906928 来自:48097389945@chatroom 发送人:wxid_sqbkq6p122x422 MD5:9c8722bb46ada87a48b8d2279a34b96b 大小:551174
2025-08-05 22:47:15 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8131439038937104737
2025-08-05 22:47:56 | DEBUG | 收到消息: {'MsgId': 1513849845, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14716824901095655672</objectId>\n\t\t\t<objectNonceId>9930980758579839306_4_20_13_1_1754404492445413_59298dd2-7209-11f0-9761-1793f039415b</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>有料猫姐</nickname>\n\t\t\t<username>v2_060000231003b20faec8c7ea8e10c7d0c903ea35b07743b4809a829b2203b08886e2ffbdad70@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/Lbnjkg8IAaiaFnPURwU9YDvVnbEGN9gC0nEHEtqc2Xt7kFWwcfoRpwGia1mPwIU0M4VuhqKrpm2X4PiasGtYgZ5ZRvJq6WiaT08TM674bTtqS7eIXFutkZxBmszVd6o70icl3/0]]></avatar>\n\t\t\t<desc>皇上刷手机刷累了吧\n是时候要放松一下了</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>0</authIconType>\n\t\t\t<authIconUrl><![CDATA[]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQC9BhQQELgb4pXkibEXGEgOUDclIiaAcJyRLKKlGsBy0puEtXRUeZL25wpiasQMoPlF6pR6ibCcofm1tJOwZMMBibv1y&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a170&token=cztXnd9GyrHuZHabPTl5LuToXqjKEw5DUMgEibA0P6Q5CxPvLWaTGPiatdmgZwMZZJqVb8ALWazKkibnn7wlbPjiaVv6RJ5ibrhv986K3ljzxiaeZYZkwWo5Gn2flsTu1JsWLhHQtn9PeM4WKdmqh1FXxBZtxPOibibAicOvP8t1baALf2xU&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjcaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=T0G8B5bIPwNNq4lKPa8WZ1hWJThnSGDBeRmDeUYzg3KabxmdMjUDufBaYAEgNGqssJi30bcGg97dEUzGVcfHyg&ctsc=20&extg=108b900&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAADwJzTE%2Bv%2Bw8e%2BbjxaSaBAAAADnaHZTnGbFfAj9RgZXfw6VUeMNI9%2FZRsh5pNpilrXyEmpurkrp3692S1NnDG7qd4n0sD0qKn1gfVc%3D&svrnonce=**********]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzuxCEKUQkHlnO7X0obuKH2ia2a2p2oph8ZL9zib4BLVbDuq9XbD4jwYoAAKZJ1DSWibB6PictVcDdZCdSEBiaFL67hhQ&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxufianJ3eLRsFU1aqENHGccWAia3As05f4aGh6UcVllM00e91agIhpFJHrEqgE9vqGwFCxuXMMf3mN3kzicXFMic0tsDpAMoUWYj4asw3N56t9LeBv7TW070hiaMIUrcPyMmjO3uoTu29SyLO&ctsc=2-20]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzuxCEKUQkHlnO7X0obuKH2ia2a2p2oph8ZL9zib4BLVbDuq9XbD4jwYoAAKZJ1DSWibB6PictVcDdZCdSEBiaFL67hhQ&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxufianJ3eLRsFU1aqENHGccWAia3As05f4aGh6UcVllM00e91agIhpFJHrEqgE9vqGwFCxuXMMf3mN3kzicXFMic0tsDpAMoUWYj4asw3N56t9LeBv7TW070hiaMIUrcPyMmjO3uoTu29SyLO&ctsc=2-20]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1080.0</width>\n\t\t\t\t\t<height>1920.0</height>\n\t\t\t\t\t<videoPlayDuration>16</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>20</sourceCommentScene>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754404159636","contextId":"1-1-20-05de520ca56b482d832218bb648af3f2","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405287, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>c5c85dd21f1cc5c02af453bde94d5673_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_Wizcyi4D|v1_njaRwqRO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 1834176765569535724, 'MsgSeq': 871430080}
2025-08-05 22:47:56 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-08-05 22:47:56 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14716824901095655672</objectId>
			<objectNonceId>9930980758579839306_4_20_13_1_1754404492445413_59298dd2-7209-11f0-9761-1793f039415b</objectNonceId>
			<feedType>4</feedType>
			<nickname>有料猫姐</nickname>
			<username>v2_060000231003b20faec8c7ea8e10c7d0c903ea35b07743b4809a829b2203b08886e2ffbdad70@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/Lbnjkg8IAaiaFnPURwU9YDvVnbEGN9gC0nEHEtqc2Xt7kFWwcfoRpwGia1mPwIU0M4VuhqKrpm2X4PiasGtYgZ5ZRvJq6WiaT08TM674bTtqS7eIXFutkZxBmszVd6o70icl3/0]]></avatar>
			<desc>皇上刷手机刷累了吧
是时候要放松一下了</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQC9BhQQELgb4pXkibEXGEgOUDclIiaAcJyRLKKlGsBy0puEtXRUeZL25wpiasQMoPlF6pR6ibCcofm1tJOwZMMBibv1y&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a170&token=cztXnd9GyrHuZHabPTl5LuToXqjKEw5DUMgEibA0P6Q5CxPvLWaTGPiatdmgZwMZZJqVb8ALWazKkibnn7wlbPjiaVv6RJ5ibrhv986K3ljzxiaeZYZkwWo5Gn2flsTu1JsWLhHQtn9PeM4WKdmqh1FXxBZtxPOibibAicOvP8t1baALf2xU&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjcaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=T0G8B5bIPwNNq4lKPa8WZ1hWJThnSGDBeRmDeUYzg3KabxmdMjUDufBaYAEgNGqssJi30bcGg97dEUzGVcfHyg&ctsc=20&extg=108b900&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAADwJzTE%2Bv%2Bw8e%2BbjxaSaBAAAADnaHZTnGbFfAj9RgZXfw6VUeMNI9%2FZRsh5pNpilrXyEmpurkrp3692S1NnDG7qd4n0sD0qKn1gfVc%3D&svrnonce=**********]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzuxCEKUQkHlnO7X0obuKH2ia2a2p2oph8ZL9zib4BLVbDuq9XbD4jwYoAAKZJ1DSWibB6PictVcDdZCdSEBiaFL67hhQ&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxufianJ3eLRsFU1aqENHGccWAia3As05f4aGh6UcVllM00e91agIhpFJHrEqgE9vqGwFCxuXMMf3mN3kzicXFMic0tsDpAMoUWYj4asw3N56t9LeBv7TW070hiaMIUrcPyMmjO3uoTu29SyLO&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzuxCEKUQkHlnO7X0obuKH2ia2a2p2oph8ZL9zib4BLVbDuq9XbD4jwYoAAKZJ1DSWibB6PictVcDdZCdSEBiaFL67hhQ&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxufianJ3eLRsFU1aqENHGccWAia3As05f4aGh6UcVllM00e91agIhpFJHrEqgE9vqGwFCxuXMMf3mN3kzicXFMic0tsDpAMoUWYj4asw3N56t9LeBv7TW070hiaMIUrcPyMmjO3uoTu29SyLO&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>16</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754404159636","contextId":"1-1-20-05de520ca56b482d832218bb648af3f2","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-05 22:47:56 | DEBUG | XML消息类型: 51
2025-08-05 22:47:56 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-05 22:47:56 | DEBUG | XML消息描述: None
2025-08-05 22:47:56 | DEBUG | 附件信息 totallen: 0
2025-08-05 22:47:56 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-05 22:47:56 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-05 22:47:56 | INFO | 未知的XML消息类型: 51
2025-08-05 22:47:56 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-05 22:47:56 | INFO | 消息描述: None
2025-08-05 22:47:56 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-05 22:47:56 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14716824901095655672</objectId>
			<objectNonceId>9930980758579839306_4_20_13_1_1754404492445413_59298dd2-7209-11f0-9761-1793f039415b</objectNonceId>
			<feedType>4</feedType>
			<nickname>有料猫姐</nickname>
			<username>v2_060000231003b20faec8c7ea8e10c7d0c903ea35b07743b4809a829b2203b08886e2ffbdad70@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/Lbnjkg8IAaiaFnPURwU9YDvVnbEGN9gC0nEHEtqc2Xt7kFWwcfoRpwGia1mPwIU0M4VuhqKrpm2X4PiasGtYgZ5ZRvJq6WiaT08TM674bTtqS7eIXFutkZxBmszVd6o70icl3/0]]></avatar>
			<desc>皇上刷手机刷累了吧
是时候要放松一下了</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQC9BhQQELgb4pXkibEXGEgOUDclIiaAcJyRLKKlGsBy0puEtXRUeZL25wpiasQMoPlF6pR6ibCcofm1tJOwZMMBibv1y&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a170&token=cztXnd9GyrHuZHabPTl5LuToXqjKEw5DUMgEibA0P6Q5CxPvLWaTGPiatdmgZwMZZJqVb8ALWazKkibnn7wlbPjiaVv6RJ5ibrhv986K3ljzxiaeZYZkwWo5Gn2flsTu1JsWLhHQtn9PeM4WKdmqh1FXxBZtxPOibibAicOvP8t1baALf2xU&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjcaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=T0G8B5bIPwNNq4lKPa8WZ1hWJThnSGDBeRmDeUYzg3KabxmdMjUDufBaYAEgNGqssJi30bcGg97dEUzGVcfHyg&ctsc=20&extg=108b900&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAADwJzTE%2Bv%2Bw8e%2BbjxaSaBAAAADnaHZTnGbFfAj9RgZXfw6VUeMNI9%2FZRsh5pNpilrXyEmpurkrp3692S1NnDG7qd4n0sD0qKn1gfVc%3D&svrnonce=**********]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzuxCEKUQkHlnO7X0obuKH2ia2a2p2oph8ZL9zib4BLVbDuq9XbD4jwYoAAKZJ1DSWibB6PictVcDdZCdSEBiaFL67hhQ&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxufianJ3eLRsFU1aqENHGccWAia3As05f4aGh6UcVllM00e91agIhpFJHrEqgE9vqGwFCxuXMMf3mN3kzicXFMic0tsDpAMoUWYj4asw3N56t9LeBv7TW070hiaMIUrcPyMmjO3uoTu29SyLO&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzuxCEKUQkHlnO7X0obuKH2ia2a2p2oph8ZL9zib4BLVbDuq9XbD4jwYoAAKZJ1DSWibB6PictVcDdZCdSEBiaFL67hhQ&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxufianJ3eLRsFU1aqENHGccWAia3As05f4aGh6UcVllM00e91agIhpFJHrEqgE9vqGwFCxuXMMf3mN3kzicXFMic0tsDpAMoUWYj4asw3N56t9LeBv7TW070hiaMIUrcPyMmjO3uoTu29SyLO&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>16</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754404159636","contextId":"1-1-20-05de520ca56b482d832218bb648af3f2","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

