# 语音点歌插件 (VoiceMusicPlugin)

## 功能介绍

语音点歌插件可以根据用户提供的歌名搜索音乐，并将音乐以语音消息的形式直接发送到微信群聊或私聊中。插件采用简洁设计，无多余文字提示，直接发送音乐语音。

## 使用方法

### 基本命令
```
语音点歌 歌名
```

### 使用示例
```
语音点歌 青花瓷
语音点歌 稻香
语音点歌 告白气球
```

## 功能特点

1. **智能搜索**：基于酷狗音乐API进行歌曲搜索
2. **直接发送**：找到音乐后直接发送语音，无多余提示
3. **静默处理**：搜索失败或下载失败时不发送错误提示
4. **超时保护**：防止长时间等待，提供超时保护

## 工作流程

1. 用户发送点歌命令
2. 插件静默搜索相关音乐
3. 静默下载音乐文件
4. 直接发送语音消息（成功时）
5. 失败时不发送任何消息

## 配置说明

### config.toml 配置项

- `enable`: 是否启用插件
- `command`: 触发命令列表
- `command-format`: 命令格式说明
- `api-url`: 音乐搜索API地址
- `max-results`: 搜索结果数量（建议保持为1）
- `request-timeout`: 请求超时时间（秒）

## 注意事项

1. 音乐文件会以MP3格式的语音消息发送
2. 下载的音乐文件大小限制为10MB
3. 网络状况会影响搜索和下载速度
4. 部分音乐可能因版权原因无法获取
5. 搜索失败或下载失败时不会有任何提示

## 技术实现

- 使用酷狗音乐API进行歌曲搜索
- 支持HTTP重定向跟踪
- 异步下载和处理音乐文件
- 完善的错误处理和日志记录

## 版本信息

- 版本：1.0.0
- 作者：Claude
- 依赖：httpx, loguru
