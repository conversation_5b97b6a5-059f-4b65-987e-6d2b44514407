2025-08-05 23:00:34 | SUCCESS | 读取主设置成功
2025-08-05 23:00:34 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 23:00:34 | INFO | 2025/08/05 23:00:34 GetRedisAddr: 127.0.0.1:6379
2025-08-05 23:00:34 | INFO | 2025/08/05 23:00:34 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 23:00:34 | INFO | 2025/08/05 23:00:34 Server start at :9000
2025-08-05 23:00:34 | SUCCESS | WechatAPI服务已启动
2025-08-05 23:00:35 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 23:00:35 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 23:00:35 | SUCCESS | 登录成功
2025-08-05 23:00:35 | SUCCESS | 已开启自动心跳
2025-08-05 23:00:35 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:00:35 | SUCCESS | 数据库初始化成功
2025-08-05 23:00:35 | SUCCESS | 定时任务已启动
2025-08-05 23:00:35 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 23:00:35 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:00:36 | INFO | 播客API初始化成功
2025-08-05 23:00:36 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:00:36 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:00:36 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 23:00:36 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 23:00:36 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 23:00:36 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 23:00:36 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 23:00:36 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 23:00:36 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 23:00:36 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 23:00:36 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 23:00:36 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 23:00:36 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 23:00:36 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 23:00:36 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 23:00:36 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 23:00:36 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 23:00:36 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 23:00:36 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 23:00:36 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 23:00:36 | DEBUG |   - 启用状态: True
2025-08-05 23:00:36 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 23:00:36 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 23:00:36 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 23:00:36 | DEBUG |   - Cookies配置: 已配置
2025-08-05 23:00:36 | DEBUG |   - 限制机制: 已禁用
2025-08-05 23:00:36 | DEBUG |   - 数字选择超时: 120秒
2025-08-05 23:00:36 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 23:00:36 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-05 23:00:36 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 23:00:36 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 23:00:36 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 23:00:36 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 23:00:36 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 23:00:36 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:00:36 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 23:00:36 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 23:00:36 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 23:00:36 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 23:00:36 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 23:00:36 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 23:00:36 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 23:00:37 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 23:00:37 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-05 23:00:37 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 23:00:37 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 23:00:38 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 23:00:38 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:00:38 | INFO | [yuanbao] 插件初始化完成
2025-08-05 23:00:38 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 23:00:38 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 23:00:38 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 23:00:38 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 23:00:38 | INFO | 处理堆积消息中
2025-08-05 23:00:38 | DEBUG | 接受到 2 条消息
2025-08-05 23:00:39 | SUCCESS | 处理堆积消息完毕
2025-08-05 23:00:39 | SUCCESS | 开始处理消息
2025-08-05 23:00:39 | DEBUG | 收到消息: {'MsgId': 184251870, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1728" length="2391" bufid="0" aeskey="e200bc95c05e920191e4f69fc334b05c" voiceurl="3052020100044b30490201000204a95c809d02032df9270204d4089324020468921ca3042433363935613262352d336465382d343637372d613366332d33373731373831633662303202040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600492300080525e376c3152a1106" fromusername="wxid_ubbh6q832tcs21" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406051, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_7a20pRzX|v1_/s2cMj4H</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段语音', 'NewMsgId': 3570563512440185229, 'MsgSeq': 871430128}
2025-08-05 23:00:39 | INFO | 收到语音消息: 消息ID:184251870 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1728" length="2391" bufid="0" aeskey="e200bc95c05e920191e4f69fc334b05c" voiceurl="3052020100044b30490201000204a95c809d02032df9270204d4089324020468921ca3042433363935613262352d336465382d343637372d613366332d33373731373831633662303202040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600492300080525e376c3152a1106" fromusername="wxid_ubbh6q832tcs21" /></msg>
2025-08-05 23:00:40 | DEBUG | [VoiceTest] 缓存语音 MsgId: 184251870
2025-08-05 23:00:40 | DEBUG | [VoiceTest] 缓存语音 NewMsgId: 3570563512440185229
2025-08-05 23:00:40 | INFO | [VoiceTest] 已缓存语音消息: MsgId=184251870, NewMsgId=3570563512440185229
2025-08-05 23:00:40 | DEBUG | 收到消息: {'MsgId': 1892879731, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n感觉买这些…不值'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406052, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_pvtHpxZd|v1_AtznSE3y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4349556353455849789, 'MsgSeq': 871430129}
2025-08-05 23:00:40 | INFO | 收到文本消息: 消息ID:1892879731 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:感觉买这些…不值
2025-08-05 23:00:40 | DEBUG | [DouBaoImageToImage] 收到文本消息: '感觉买这些…不值' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:00:40 | DEBUG | [DouBaoImageToImage] 命令解析: ['感觉买这些…不值']
2025-08-05 23:00:40 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:00:40 | DEBUG | 处理消息内容: '感觉买这些…不值'
2025-08-05 23:00:40 | DEBUG | 消息内容 '感觉买这些…不值' 不匹配任何命令，忽略
2025-08-05 23:00:46 | DEBUG | 收到消息: {'MsgId': 1232127818, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>3570563512440185229</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:1728:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754406051</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406057, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>036426a1614fa11d176ce69764037f90_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_t8tI4T3o|v1_5uKBkZ0a</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': 8686941395789842188, 'MsgSeq': 871430130}
2025-08-05 23:00:46 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 23:00:46 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:00:46 | INFO | 收到引用消息: 消息ID:1232127818 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 23:00:46 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:00:46 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 23:00:46 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 23:00:46 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:00:46 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:00:46 | INFO |   - 消息内容: 跟着唱
2025-08-05 23:00:46 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 23:00:46 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 23:00:46 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:1728:0\n', 'Msgid': '3570563512440185229', 'NewMsgId': '3570563512440185229', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754406051', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 23:00:46 | INFO |   - 引用消息ID: 
2025-08-05 23:00:46 | INFO |   - 引用消息类型: 
2025-08-05 23:00:46 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:1728:0

2025-08-05 23:00:46 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 23:00:46 | INFO | [VoiceTest] 查找引用语音: 3570563512440185229
2025-08-05 23:00:46 | INFO | [VoiceTest] 当前缓存: ['184251870', '3570563512440185229']
2025-08-05 23:00:46 | ERROR | [VoiceTest] 处理引用语音消息异常: Extra data: line 1 column 69 (char 68)
2025-08-05 23:00:47 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 处理引用语音时出错
2025-08-05 23:00:48 | DEBUG | 收到消息: {'MsgId': 798241117, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n更喜欢衣服吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406060, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Y+Zji2rh|v1_fxivU4ht</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2260190749232282061, 'MsgSeq': 871430133}
2025-08-05 23:00:48 | INFO | 收到文本消息: 消息ID:798241117 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:更喜欢衣服吧
2025-08-05 23:00:48 | DEBUG | [DouBaoImageToImage] 收到文本消息: '更喜欢衣服吧' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:00:48 | DEBUG | [DouBaoImageToImage] 命令解析: ['更喜欢衣服吧']
2025-08-05 23:00:48 | DEBUG | 处理消息内容: '更喜欢衣服吧'
2025-08-05 23:00:48 | DEBUG | 消息内容 '更喜欢衣服吧' 不匹配任何命令，忽略
2025-08-05 23:00:53 | DEBUG | 收到消息: {'MsgId': 1285451889, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n买男人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406064, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_vIja/K1R|v1_gFG0h/Dh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1689929053126947531, 'MsgSeq': 871430134}
2025-08-05 23:00:53 | INFO | 收到文本消息: 消息ID:1285451889 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:买男人
2025-08-05 23:00:53 | DEBUG | [DouBaoImageToImage] 收到文本消息: '买男人' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 23:00:53 | DEBUG | [DouBaoImageToImage] 命令解析: ['买男人']
2025-08-05 23:00:53 | DEBUG | 处理消息内容: '买男人'
2025-08-05 23:00:53 | DEBUG | 消息内容 '买男人' 不匹配任何命令，忽略
2025-08-05 23:00:56 | DEBUG | 收到消息: {'MsgId': 1567681968, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_rzz8rl6gadpa19:\n翻牌就剩2天结束了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406066, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ZnICveTk|v1_eU2E5M7u</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1733324944739616818, 'MsgSeq': 871430135}
2025-08-05 23:00:56 | INFO | 收到文本消息: 消息ID:1567681968 来自:27852221909@chatroom 发送人:wxid_rzz8rl6gadpa19 @:[] 内容:翻牌就剩2天结束了
2025-08-05 23:00:56 | DEBUG | [DouBaoImageToImage] 收到文本消息: '翻牌就剩2天结束了' from wxid_rzz8rl6gadpa19 in 27852221909@chatroom
2025-08-05 23:00:56 | DEBUG | [DouBaoImageToImage] 命令解析: ['翻牌就剩2天结束了']
2025-08-05 23:00:56 | DEBUG | 处理消息内容: '翻牌就剩2天结束了'
2025-08-05 23:00:56 | DEBUG | 消息内容 '翻牌就剩2天结束了' 不匹配任何命令，忽略
2025-08-05 23:00:58 | DEBUG | 收到消息: {'MsgId': 2010881590, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n超值'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406070, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_UwhlkSoj|v1_B+iPSFJC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7238328432581387665, 'MsgSeq': 871430136}
2025-08-05 23:00:58 | INFO | 收到文本消息: 消息ID:2010881590 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:超值
2025-08-05 23:00:58 | DEBUG | [DouBaoImageToImage] 收到文本消息: '超值' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 23:00:58 | DEBUG | [DouBaoImageToImage] 命令解析: ['超值']
2025-08-05 23:00:58 | DEBUG | 处理消息内容: '超值'
2025-08-05 23:00:58 | DEBUG | 消息内容 '超值' 不匹配任何命令，忽略
2025-08-05 23:01:01 | DEBUG | 收到消息: {'MsgId': 382792863, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n没有不值 目前看坐骑和戒指'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406073, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_grhFtBEF|v1_b78iXzEz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 674372042527617318, 'MsgSeq': 871430137}
2025-08-05 23:01:01 | INFO | 收到文本消息: 消息ID:382792863 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:没有不值 目前看坐骑和戒指
2025-08-05 23:01:01 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没有不值 目前看坐骑和戒指' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:01:01 | DEBUG | [DouBaoImageToImage] 命令解析: ['没有不值', '目前看坐骑和戒指']
2025-08-05 23:01:01 | DEBUG | 处理消息内容: '没有不值 目前看坐骑和戒指'
2025-08-05 23:01:01 | DEBUG | 消息内容 '没有不值 目前看坐骑和戒指' 不匹配任何命令，忽略
2025-08-05 23:01:08 | DEBUG | 收到消息: {'MsgId': 319054348, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_g173eyu8nbm522:\n我今天花了六千钻石就拿到一个坐骑飞马[Emm]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406080, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_nW5YH6in|v1_yghXwTto</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7144582654033148611, 'MsgSeq': 871430138}
2025-08-05 23:01:08 | INFO | 收到文本消息: 消息ID:319054348 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 @:[] 内容:我今天花了六千钻石就拿到一个坐骑飞马[Emm]
2025-08-05 23:01:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我今天花了六千钻石就拿到一个坐骑飞马[Emm]' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 23:01:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['我今天花了六千钻石就拿到一个坐骑飞马[Emm]']
2025-08-05 23:01:08 | DEBUG | 处理消息内容: '我今天花了六千钻石就拿到一个坐骑飞马[Emm]'
2025-08-05 23:01:08 | DEBUG | 消息内容 '我今天花了六千钻石就拿到一个坐骑飞马[Emm]' 不匹配任何命令，忽略
2025-08-05 23:01:11 | DEBUG | 收到消息: {'MsgId': 1917978071, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n@我是男人\u2005哪里有男人买？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406083, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_bmzp9achod6922</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_7jj+369S|v1_3Ay08TxD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4524265867830101056, 'MsgSeq': 871430139}
2025-08-05 23:01:11 | INFO | 收到文本消息: 消息ID:1917978071 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:['wxid_bmzp9achod6922'] 内容:@我是男人 哪里有男人买？
2025-08-05 23:01:11 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@我是男人 哪里有男人买？' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:01:11 | DEBUG | [DouBaoImageToImage] 命令解析: ['@我是男人\u2005哪里有男人买？']
2025-08-05 23:01:11 | DEBUG | 处理消息内容: '@我是男人 哪里有男人买？'
2025-08-05 23:01:11 | DEBUG | 消息内容 '@我是男人 哪里有男人买？' 不匹配任何命令，忽略
2025-08-05 23:01:14 | DEBUG | 收到消息: {'MsgId': 824885640, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n好吧，我更喜欢衣服'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406086, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_LN1PwSid|v1_MWfbqDBu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2855691260264914546, 'MsgSeq': 871430140}
2025-08-05 23:01:14 | INFO | 收到文本消息: 消息ID:824885640 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:好吧，我更喜欢衣服
2025-08-05 23:01:14 | DEBUG | [DouBaoImageToImage] 收到文本消息: '好吧，我更喜欢衣服' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:01:14 | DEBUG | [DouBaoImageToImage] 命令解析: ['好吧，我更喜欢衣服']
2025-08-05 23:01:14 | DEBUG | 处理消息内容: '好吧，我更喜欢衣服'
2025-08-05 23:01:14 | DEBUG | 消息内容 '好吧，我更喜欢衣服' 不匹配任何命令，忽略
2025-08-05 23:01:17 | DEBUG | 收到消息: {'MsgId': 733607905, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n买我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406089, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_5O6F1XwF|v1_dPfZi2AX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3583657505979196978, 'MsgSeq': 871430141}
2025-08-05 23:01:17 | INFO | 收到文本消息: 消息ID:733607905 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:买我
2025-08-05 23:01:17 | DEBUG | [DouBaoImageToImage] 收到文本消息: '买我' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 23:01:17 | DEBUG | [DouBaoImageToImage] 命令解析: ['买我']
2025-08-05 23:01:17 | DEBUG | 处理消息内容: '买我'
2025-08-05 23:01:17 | DEBUG | 消息内容 '买我' 不匹配任何命令，忽略
2025-08-05 23:01:21 | DEBUG | 收到消息: {'MsgId': 1437453513, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>啥样子？我看看</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>7144582654033148611</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_g173eyu8nbm522</chatusr>\n\t\t\t<displayname>清弦</displayname>\n\t\t\t<content>我今天花了六千钻石就拿到一个坐骑飞马[Emm]</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777843412&lt;/sequence_id&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_cGnxp97S|v1_sgKbGiyi&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754406080</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406093, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>77e1222a700eb1d17a31b245a1fc4640_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ELdZq739|v1_MPzS2VJc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6220666574961180866, 'MsgSeq': 871430142}
2025-08-05 23:01:21 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-05 23:01:21 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:01:21 | INFO | 收到引用消息: 消息ID:1437453513 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 内容:啥样子？我看看 引用类型:1
2025-08-05 23:01:21 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:01:21 | INFO | [DouBaoImageToImage] 消息内容: '啥样子？我看看' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:01:21 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['啥样子？我看看']
2025-08-05 23:01:21 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:01:21 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:01:21 | INFO |   - 消息内容: 啥样子？我看看
2025-08-05 23:01:21 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:01:21 | INFO |   - 发送人: wxid_c3jkq1ylevnb12
2025-08-05 23:01:21 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '我今天花了六千钻石就拿到一个坐骑飞马[Emm]', 'Msgid': '7144582654033148611', 'NewMsgId': '7144582654033148611', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '清弦', 'MsgSource': '<msgsource><sequence_id>777843412</sequence_id>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_cGnxp97S|v1_sgKbGiyi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754406080', 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-05 23:01:21 | INFO |   - 引用消息ID: 
2025-08-05 23:01:21 | INFO |   - 引用消息类型: 
2025-08-05 23:01:21 | INFO |   - 引用消息内容: 我今天花了六千钻石就拿到一个坐骑飞马[Emm]
2025-08-05 23:01:21 | INFO |   - 引用消息发送人: wxid_c3jkq1ylevnb12
2025-08-05 23:01:23 | DEBUG | 收到消息: {'MsgId': 1972240734, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<msg><emoji fromusername="wxid_e3o8s2nf9u2o22" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="e5908fee0101d7d18ab834c639bc9e02" len="6193485" productid="" androidmd5="e5908fee0101d7d18ab834c639bc9e02" androidlen="6193485" s60v3md5="e5908fee0101d7d18ab834c639bc9e02" s60v3len="6193485" s60v5md5="e5908fee0101d7d18ab834c639bc9e02" s60v5len="6193485" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=e5908fee0101d7d18ab834c639bc9e02&amp;filekey=30440201010430302e02016e040253480420653539303866656530313031643764313861623833346336333962633965303202035e814d040d00000004627466730000000132&amp;hy=SH&amp;storeid=26853f99900006c99717667940000006e01004fb253482b0a41b156e2c4be5&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=d5c9b8866d996e85a032f0517b241b97&amp;filekey=30440201010430302e02016e040253480420643563396238383636643939366538356130333266303531376232343162393702035e8150040d00000004627466730000000132&amp;hy=SH&amp;storeid=26853f999000713fb717667940000006e02004fb253482b0a41b156e2c4c3a&amp;ef=2&amp;bizid=1022" aeskey="94240615d69c41798738f368ddd57108" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=bf00a806c41635fc10aa19ff9f1cf2c6&amp;filekey=30440201010430302e02016e04025348042062663030613830366334313633356663313061613139666639663163663263360203075cc0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26853f999000dc232717667940000006e03004fb353482b0a41b156e2c4c98&amp;ef=3&amp;bizid=1022" externmd5="9119a62f57aeca144175288c8158a31c" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406095, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_j7f0ML56|v1_053no6sW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '星空- 梦在群聊中发了一个表情', 'NewMsgId': 728432122658370664, 'MsgSeq': 871430143}
2025-08-05 23:01:23 | INFO | 收到表情消息: 消息ID:1972240734 来自:48097389945@chatroom 发送人:wxid_e3o8s2nf9u2o22 MD5:e5908fee0101d7d18ab834c639bc9e02 大小:6193485
2025-08-05 23:01:24 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 728432122658370664
2025-08-05 23:01:28 | DEBUG | 收到消息: {'MsgId': 1866715918, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<msg><emoji fromusername="wxid_e3o8s2nf9u2o22" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="0d6a847456501d5e7c76197d6d751b51" len="4060508" productid="" androidmd5="0d6a847456501d5e7c76197d6d751b51" androidlen="4060508" s60v3md5="0d6a847456501d5e7c76197d6d751b51" s60v3len="4060508" s60v5md5="0d6a847456501d5e7c76197d6d751b51" s60v5len="4060508" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=0d6a847456501d5e7c76197d6d751b51&amp;filekey=30440201010430302e02016e040253480420306436613834373435363530316435653763373631393764366437353162353102033df55c040d00000004627466730000000132&amp;hy=SH&amp;storeid=26853f9da0008320c717667940000006e01004fb253480963e03156d92d3ac&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=a2d973f8e14950e6a55e7cb73cedf85b&amp;filekey=30440201010430302e02016e040253480420613264393733663865313439353065366135356537636237336365646638356202033df560040d00000004627466730000000132&amp;hy=SH&amp;storeid=26853f9da000c80c9717667940000006e02004fb253480963e03156d92d3e6&amp;ef=2&amp;bizid=1022" aeskey="ad850e9d61234668a9feae4a4c63dec1" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=4b0c2006172392083768e3c660cb1f46&amp;filekey=30440201010430302e02016e0402534804203462306332303036313732333932303833373638653363363630636231663436020301f2e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26853f9db00012c42717667940000006e03004fb353480963e03156d92d41e&amp;ef=3&amp;bizid=1022" externmd5="8288d663dcca246a6f87deb26fae910d" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406099, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_ESMhxrnO|v1_ouuEa5yD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '星空- 梦在群聊中发了一个表情', 'NewMsgId': 434891365491329314, 'MsgSeq': 871430144}
2025-08-05 23:01:28 | INFO | 收到表情消息: 消息ID:1866715918 来自:48097389945@chatroom 发送人:wxid_e3o8s2nf9u2o22 MD5:0d6a847456501d5e7c76197d6d751b51 大小:4060508
2025-08-05 23:01:28 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 434891365491329314
2025-08-05 23:01:32 | DEBUG | 收到消息: {'MsgId': 1292283847, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n@我是男人\u2005你是冒牌的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406104, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_bmzp9achod6922</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_KSehuWvH|v1_jONrCnso</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 622406178874865167, 'MsgSeq': 871430145}
2025-08-05 23:01:32 | INFO | 收到文本消息: 消息ID:1292283847 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:['wxid_bmzp9achod6922'] 内容:@我是男人 你是冒牌的
2025-08-05 23:01:32 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@我是男人 你是冒牌的' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:01:32 | DEBUG | [DouBaoImageToImage] 命令解析: ['@我是男人\u2005你是冒牌的']
2025-08-05 23:01:32 | DEBUG | 处理消息内容: '@我是男人 你是冒牌的'
2025-08-05 23:01:32 | DEBUG | 消息内容 '@我是男人 你是冒牌的' 不匹配任何命令，忽略
2025-08-05 23:01:37 | DEBUG | 收到消息: {'MsgId': 1333334329, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n帮打排位就带走'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406109, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_CYCkbjvQ|v1_nt1dcjt3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8098977794826153179, 'MsgSeq': 871430146}
2025-08-05 23:01:37 | INFO | 收到文本消息: 消息ID:1333334329 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:帮打排位就带走
2025-08-05 23:01:37 | DEBUG | [DouBaoImageToImage] 收到文本消息: '帮打排位就带走' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 23:01:37 | DEBUG | [DouBaoImageToImage] 命令解析: ['帮打排位就带走']
2025-08-05 23:01:37 | DEBUG | 处理消息内容: '帮打排位就带走'
2025-08-05 23:01:37 | DEBUG | 消息内容 '帮打排位就带走' 不匹配任何命令，忽略
2025-08-05 23:01:42 | DEBUG | 收到消息: {'MsgId': 1728343215, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_g173eyu8nbm522:\n就炫衣阁里面的那个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406114, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_um3C+9Q/|v1_G4qoxzVM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2108490059716315005, 'MsgSeq': 871430147}
2025-08-05 23:01:42 | INFO | 收到文本消息: 消息ID:1728343215 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 @:[] 内容:就炫衣阁里面的那个
2025-08-05 23:01:42 | DEBUG | [DouBaoImageToImage] 收到文本消息: '就炫衣阁里面的那个' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 23:01:42 | DEBUG | [DouBaoImageToImage] 命令解析: ['就炫衣阁里面的那个']
2025-08-05 23:01:42 | DEBUG | 处理消息内容: '就炫衣阁里面的那个'
2025-08-05 23:01:42 | DEBUG | 消息内容 '就炫衣阁里面的那个' 不匹配任何命令，忽略
2025-08-05 23:01:47 | DEBUG | 收到消息: {'MsgId': 1983686136, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n[囧]身上缺两两肉'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406119, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_EqOBflXj|v1_DzgDaS+p</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5203342004425283585, 'MsgSeq': 871430148}
2025-08-05 23:01:47 | INFO | 收到文本消息: 消息ID:1983686136 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:[囧]身上缺两两肉
2025-08-05 23:01:47 | DEBUG | [DouBaoImageToImage] 收到文本消息: '[囧]身上缺两两肉' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:01:47 | DEBUG | [DouBaoImageToImage] 命令解析: ['[囧]身上缺两两肉']
2025-08-05 23:01:47 | DEBUG | 处理消息内容: '[囧]身上缺两两肉'
2025-08-05 23:01:47 | DEBUG | 消息内容 '[囧]身上缺两两肉' 不匹配任何命令，忽略
2025-08-05 23:01:51 | DEBUG | 收到消息: {'MsgId': 1360611722, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n二两肉'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406123, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_z698agHV|v1_lEByhVCU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3942016292331388787, 'MsgSeq': 871430149}
2025-08-05 23:01:51 | INFO | 收到文本消息: 消息ID:1360611722 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:二两肉
2025-08-05 23:01:51 | DEBUG | [DouBaoImageToImage] 收到文本消息: '二两肉' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:01:51 | DEBUG | [DouBaoImageToImage] 命令解析: ['二两肉']
2025-08-05 23:01:51 | DEBUG | 处理消息内容: '二两肉'
2025-08-05 23:01:51 | DEBUG | 消息内容 '二两肉' 不匹配任何命令，忽略
2025-08-05 23:02:01 | DEBUG | 收到消息: {'MsgId': 302766862, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>套路深</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8098977794826153179</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_bmzp9achod6922</chatusr>\n\t\t\t<displayname>我是男人</displayname>\n\t\t\t<content>帮打排位就带走</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777843418&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_x/e7eOfo|v1_1akFtbW9&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754406109</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406133, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>a8790a1b28d3578619e06c6f5cad1d18_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_fW5HhWHf|v1_g6C134Gl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3189073043997726929, 'MsgSeq': 871430150}
2025-08-05 23:02:01 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-05 23:02:01 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:02:01 | INFO | 收到引用消息: 消息ID:302766862 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 内容:套路深 引用类型:1
2025-08-05 23:02:01 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:02:01 | INFO | [DouBaoImageToImage] 消息内容: '套路深' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:02:01 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['套路深']
2025-08-05 23:02:01 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:02:01 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:02:01 | INFO |   - 消息内容: 套路深
2025-08-05 23:02:01 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:02:01 | INFO |   - 发送人: wxid_c3jkq1ylevnb12
2025-08-05 23:02:01 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '帮打排位就带走', 'Msgid': '8098977794826153179', 'NewMsgId': '8098977794826153179', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '我是男人', 'MsgSource': '<msgsource><sequence_id>777843418</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_x/e7eOfo|v1_1akFtbW9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754406109', 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-05 23:02:01 | INFO |   - 引用消息ID: 
2025-08-05 23:02:01 | INFO |   - 引用消息类型: 
2025-08-05 23:02:01 | INFO |   - 引用消息内容: 帮打排位就带走
2025-08-05 23:02:01 | INFO |   - 引用消息发送人: wxid_c3jkq1ylevnb12
2025-08-05 23:02:10 | DEBUG | 收到消息: {'MsgId': 901176527, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n任务一大堆[捂脸][捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406141, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_z67kedpl|v1_Pbhy7+8S</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4994740699392586228, 'MsgSeq': 871430151}
2025-08-05 23:02:10 | INFO | 收到文本消息: 消息ID:901176527 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:任务一大堆[捂脸][捂脸]
2025-08-05 23:02:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '任务一大堆[捂脸][捂脸]' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:02:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['任务一大堆[捂脸][捂脸]']
2025-08-05 23:02:10 | DEBUG | 处理消息内容: '任务一大堆[捂脸][捂脸]'
2025-08-05 23:02:10 | DEBUG | 消息内容 '任务一大堆[捂脸][捂脸]' 不匹配任何命令，忽略
2025-08-05 23:02:17 | DEBUG | 收到消息: {'MsgId': 215134419, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那个？？</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2108490059716315005</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_g173eyu8nbm522</chatusr>\n\t\t\t<displayname>清弦</displayname>\n\t\t\t<content>就炫衣阁里面的那个</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777843419&lt;/sequence_id&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_Vs14qVVJ|v1_s5Vk8AsF&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754406114</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406148, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>d3dbc0a1eafedb20a7618b32aa16d1e3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_1KK929+f|v1_FNMK/zpM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1820391155813790644, 'MsgSeq': 871430152}
2025-08-05 23:02:17 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-05 23:02:17 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:02:17 | INFO | 收到引用消息: 消息ID:215134419 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 内容:那个？？ 引用类型:1
2025-08-05 23:02:17 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:02:17 | INFO | [DouBaoImageToImage] 消息内容: '那个？？' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:02:17 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['那个？？']
2025-08-05 23:02:17 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:02:17 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:02:17 | INFO |   - 消息内容: 那个？？
2025-08-05 23:02:17 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:02:17 | INFO |   - 发送人: wxid_c3jkq1ylevnb12
2025-08-05 23:02:17 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '就炫衣阁里面的那个', 'Msgid': '2108490059716315005', 'NewMsgId': '2108490059716315005', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '清弦', 'MsgSource': '<msgsource><sequence_id>777843419</sequence_id>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Vs14qVVJ|v1_s5Vk8AsF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754406114', 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-05 23:02:17 | INFO |   - 引用消息ID: 
2025-08-05 23:02:17 | INFO |   - 引用消息类型: 
2025-08-05 23:02:17 | INFO |   - 引用消息内容: 就炫衣阁里面的那个
2025-08-05 23:02:17 | INFO |   - 引用消息发送人: wxid_c3jkq1ylevnb12
2025-08-05 23:02:28 | DEBUG | 收到消息: {'MsgId': 1701482959, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_g173eyu8nbm522:\n昂'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406159, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_6wrds26g|v1_9QujnE2b</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6609287483680050305, 'MsgSeq': 871430153}
2025-08-05 23:02:28 | INFO | 收到文本消息: 消息ID:1701482959 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 @:[] 内容:昂
2025-08-05 23:02:28 | DEBUG | [DouBaoImageToImage] 收到文本消息: '昂' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 23:02:28 | DEBUG | [DouBaoImageToImage] 命令解析: ['昂']
2025-08-05 23:02:28 | DEBUG | 处理消息内容: '昂'
2025-08-05 23:02:28 | DEBUG | 消息内容 '昂' 不匹配任何命令，忽略
2025-08-05 23:02:34 | DEBUG | 收到消息: {'MsgId': 1460657840, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_bmzp9achod6922:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>把你男人也拉进来消费</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1733324944739616818</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_rzz8rl6gadpa19</chatusr>\n\t\t\t<displayname>阿醒。</displayname>\n\t\t\t<content>翻牌就剩2天结束了</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;849854290&lt;/sequence_id&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_ynbL9Sds|v1_SDC98cxH&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754406066</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_bmzp9achod6922</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406165, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>87c5e887ffaac35fa19648af5e7fe8cc_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_4mKNP9Q9|v1_1l6yppGu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 222952606642035106, 'MsgSeq': 871430154}
2025-08-05 23:02:34 | DEBUG | 从群聊消息中提取发送者: wxid_bmzp9achod6922
2025-08-05 23:02:34 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:02:34 | INFO | 收到引用消息: 消息ID:1460657840 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 内容:把你男人也拉进来消费 引用类型:1
2025-08-05 23:02:34 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:02:34 | INFO | [DouBaoImageToImage] 消息内容: '把你男人也拉进来消费' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 23:02:34 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['把你男人也拉进来消费']
2025-08-05 23:02:34 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:02:34 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:02:34 | INFO |   - 消息内容: 把你男人也拉进来消费
2025-08-05 23:02:34 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:02:34 | INFO |   - 发送人: wxid_bmzp9achod6922
2025-08-05 23:02:34 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '翻牌就剩2天结束了', 'Msgid': '1733324944739616818', 'NewMsgId': '1733324944739616818', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '阿醒。', 'MsgSource': '<msgsource><sequence_id>849854290</sequence_id>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ynbL9Sds|v1_SDC98cxH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754406066', 'SenderWxid': 'wxid_bmzp9achod6922'}
2025-08-05 23:02:34 | INFO |   - 引用消息ID: 
2025-08-05 23:02:34 | INFO |   - 引用消息类型: 
2025-08-05 23:02:34 | INFO |   - 引用消息内容: 翻牌就剩2天结束了
2025-08-05 23:02:34 | INFO |   - 引用消息发送人: wxid_bmzp9achod6922
2025-08-05 23:02:42 | DEBUG | 收到消息: {'MsgId': 1930598754, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_g173eyu8nbm522:\n等于直接花钻买的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406174, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_576fJ6mg|v1_kEdq9DqC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1447856648832762069, 'MsgSeq': 871430155}
2025-08-05 23:02:42 | INFO | 收到文本消息: 消息ID:1930598754 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 @:[] 内容:等于直接花钻买的
2025-08-05 23:02:42 | DEBUG | [DouBaoImageToImage] 收到文本消息: '等于直接花钻买的' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 23:02:42 | DEBUG | [DouBaoImageToImage] 命令解析: ['等于直接花钻买的']
2025-08-05 23:02:42 | DEBUG | 处理消息内容: '等于直接花钻买的'
2025-08-05 23:02:42 | DEBUG | 消息内容 '等于直接花钻买的' 不匹配任何命令，忽略
2025-08-05 23:03:00 | DEBUG | 收到消息: {'MsgId': 1314548694, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_g173eyu8nbm522:\n然后带送一个赠品大翅膀'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406192, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_PcsDV9Ji|v1_6+pPcli1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8739224339858444506, 'MsgSeq': 871430156}
2025-08-05 23:03:00 | INFO | 收到文本消息: 消息ID:1314548694 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 @:[] 内容:然后带送一个赠品大翅膀
2025-08-05 23:03:00 | DEBUG | [DouBaoImageToImage] 收到文本消息: '然后带送一个赠品大翅膀' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 23:03:00 | DEBUG | [DouBaoImageToImage] 命令解析: ['然后带送一个赠品大翅膀']
2025-08-05 23:03:00 | DEBUG | 处理消息内容: '然后带送一个赠品大翅膀'
2025-08-05 23:03:00 | DEBUG | 消息内容 '然后带送一个赠品大翅膀' 不匹配任何命令，忽略
2025-08-05 23:03:23 | DEBUG | 收到消息: {'MsgId': 2091948844, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n有钻的情况下还是能花出去的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406214, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Wf31Oz56|v1_wTl7+9/l</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4642960740730338910, 'MsgSeq': 871430157}
2025-08-05 23:03:23 | INFO | 收到文本消息: 消息ID:2091948844 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:有钻的情况下还是能花出去的
2025-08-05 23:03:23 | DEBUG | [DouBaoImageToImage] 收到文本消息: '有钻的情况下还是能花出去的' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 23:03:23 | DEBUG | [DouBaoImageToImage] 命令解析: ['有钻的情况下还是能花出去的']
2025-08-05 23:03:23 | DEBUG | 处理消息内容: '有钻的情况下还是能花出去的'
2025-08-05 23:03:23 | DEBUG | 消息内容 '有钻的情况下还是能花出去的' 不匹配任何命令，忽略
2025-08-05 23:03:27 | DEBUG | 收到消息: {'MsgId': 35400989, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n只要你有'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406219, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_NcovWvuA|v1_fFfKlnPS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6870259439308914895, 'MsgSeq': 871430158}
2025-08-05 23:03:27 | INFO | 收到文本消息: 消息ID:35400989 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:只要你有
2025-08-05 23:03:27 | DEBUG | [DouBaoImageToImage] 收到文本消息: '只要你有' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 23:03:27 | DEBUG | [DouBaoImageToImage] 命令解析: ['只要你有']
2025-08-05 23:03:27 | DEBUG | 处理消息内容: '只要你有'
2025-08-05 23:03:27 | DEBUG | 消息内容 '只要你有' 不匹配任何命令，忽略
2025-08-05 23:03:32 | DEBUG | 收到消息: {'MsgId': 2039987464, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_g173eyu8nbm522:\n我还想它中途能爆出来然后我就能兑换其他的了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406224, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VuBsZV6F|v1_m953Flx2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 221200723453510284, 'MsgSeq': 871430159}
2025-08-05 23:03:32 | INFO | 收到文本消息: 消息ID:2039987464 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 @:[] 内容:我还想它中途能爆出来然后我就能兑换其他的了
2025-08-05 23:03:32 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我还想它中途能爆出来然后我就能兑换其他的了' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 23:03:32 | DEBUG | [DouBaoImageToImage] 命令解析: ['我还想它中途能爆出来然后我就能兑换其他的了']
2025-08-05 23:03:32 | DEBUG | 处理消息内容: '我还想它中途能爆出来然后我就能兑换其他的了'
2025-08-05 23:03:32 | DEBUG | 消息内容 '我还想它中途能爆出来然后我就能兑换其他的了' 不匹配任何命令，忽略
2025-08-05 23:03:34 | DEBUG | 收到消息: {'MsgId': 2001853328, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<msg><emoji fromusername="wxid_x4s6k999g6qg22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="428abbad20ed08bcd8a079811482bc4c" len="498449" productid="" androidmd5="428abbad20ed08bcd8a079811482bc4c" androidlen="498449" s60v3md5="428abbad20ed08bcd8a079811482bc4c" s60v3len="498449" s60v5md5="428abbad20ed08bcd8a079811482bc4c" s60v5len="498449" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=428abbad20ed08bcd8a079811482bc4c&amp;filekey=30440201010430302e02016e0402535a042034323861626261643230656430386263643861303739383131343832626334630203079b11040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2675022d10006d49b32d8a1d00000006e01004fb1535a2ecfb011572f50468&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=f89f8be26395802408a596e07c04e548&amp;filekey=30440201010430302e02016e0402535a042066383966386265323633393538303234303861353936653037633034653534380203079b20040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2675022d10008446432d8a1d00000006e02004fb2535a2ecfb011572f5048b&amp;ef=2&amp;bizid=1022" aeskey="71916f661471449ea3a309cb9a168fba" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=3bd2d8867a321a2fbff35b6e346c9c1e&amp;filekey=30440201010430302e02016e0402535a04203362643264383836376133323161326662666633356236653334366339633165020301dc80040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2675022d100099fe032d8a1d00000006e03004fb3535a2ecfb011572f504ae&amp;ef=3&amp;bizid=1022" externmd5="57f87fb7ca0ebfdaae2c561ef47a8237" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406224, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Dpc8li/b|v1_fVd6EOom</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2121016345235635472, 'MsgSeq': 871430160}
2025-08-05 23:03:34 | INFO | 收到表情消息: 消息ID:2001853328 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 MD5:428abbad20ed08bcd8a079811482bc4c 大小:498449
2025-08-05 23:03:34 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2121016345235635472
