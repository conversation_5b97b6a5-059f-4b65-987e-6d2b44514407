2025-08-05 23:11:04 | SUCCESS | 读取主设置成功
2025-08-05 23:11:04 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 23:11:04 | INFO | 2025/08/05 23:11:04 GetRedisAddr: 127.0.0.1:6379
2025-08-05 23:11:04 | INFO | 2025/08/05 23:11:04 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 23:11:04 | INFO | 2025/08/05 23:11:04 Server start at :9000
2025-08-05 23:11:04 | SUCCESS | WechatAPI服务已启动
2025-08-05 23:11:05 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 23:11:05 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 23:11:05 | SUCCESS | 登录成功
2025-08-05 23:11:05 | SUCCESS | 已开启自动心跳
2025-08-05 23:11:05 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:11:05 | SUCCESS | 数据库初始化成功
2025-08-05 23:11:05 | SUCCESS | 定时任务已启动
2025-08-05 23:11:05 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 23:11:05 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:11:06 | INFO | 播客API初始化成功
2025-08-05 23:11:06 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:11:06 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:11:06 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 23:11:06 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 23:11:06 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 23:11:06 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 23:11:06 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 23:11:06 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 23:11:06 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 23:11:06 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 23:11:06 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 23:11:06 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 23:11:06 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 23:11:06 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 23:11:06 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 23:11:06 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 23:11:06 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 23:11:06 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 23:11:06 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 23:11:06 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 23:11:06 | DEBUG |   - 启用状态: True
2025-08-05 23:11:06 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 23:11:06 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 23:11:06 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 23:11:06 | DEBUG |   - Cookies配置: 已配置
2025-08-05 23:11:06 | DEBUG |   - 限制机制: 已禁用
2025-08-05 23:11:06 | DEBUG |   - 数字选择超时: 120秒
2025-08-05 23:11:06 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 23:11:06 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-05 23:11:06 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 23:11:06 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 23:11:06 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 23:11:06 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 23:11:06 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 23:11:06 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:11:06 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 23:11:06 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 23:11:06 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 23:11:06 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 23:11:06 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 23:11:06 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 23:11:06 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 23:11:07 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-05 23:11:07 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 23:11:07 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 23:11:07 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 23:11:08 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 23:11:08 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:11:08 | INFO | [yuanbao] 插件初始化完成
2025-08-05 23:11:08 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 23:11:08 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 23:11:08 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 23:11:08 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 23:11:08 | INFO | 处理堆积消息中
2025-08-05 23:11:08 | SUCCESS | 处理堆积消息完毕
2025-08-05 23:11:08 | SUCCESS | 开始处理消息
2025-08-05 23:11:31 | DEBUG | 收到消息: {'MsgId': 2117356417, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="2276" length="3575" bufid="0" aeskey="1533b169e546d4c0c2d0a4d2af39e634" voiceurl="3052020100044b30490201000204a95c809d02032df927020478089324020468921f2f042439373037346661652d336634642d343430642d613533392d39346663323232303464623202040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600402311080525e376c314301106" fromusername="wxid_ubbh6q832tcs21" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406703, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_ECS8sqSu|v1_+azZmCz8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段语音', 'NewMsgId': 8927541145760461972, 'MsgSeq': 871430193}
2025-08-05 23:11:31 | INFO | 收到语音消息: 消息ID:2117356417 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="2276" length="3575" bufid="0" aeskey="1533b169e546d4c0c2d0a4d2af39e634" voiceurl="3052020100044b30490201000204a95c809d02032df927020478089324020468921f2f042439373037346661652d336634642d343430642d613533392d39346663323232303464623202040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600402311080525e376c314301106" fromusername="wxid_ubbh6q832tcs21" /></msg>
2025-08-05 23:11:32 | DEBUG | [VoiceTest] 缓存语音 MsgId: 2117356417
2025-08-05 23:11:32 | DEBUG | [VoiceTest] 缓存语音 NewMsgId: 8927541145760461972
2025-08-05 23:11:32 | INFO | [VoiceTest] 已缓存语音消息: MsgId=2117356417, NewMsgId=8927541145760461972
2025-08-05 23:11:35 | DEBUG | 收到消息: {'MsgId': 1386455772, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n能送鲜花💐吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406706, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_JX9E4fDJ|v1_/5P/iocg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7692510881553693641, 'MsgSeq': 871430194}
2025-08-05 23:11:35 | INFO | 收到文本消息: 消息ID:1386455772 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:能送鲜花💐吗
2025-08-05 23:11:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '能送鲜花💐吗' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:11:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['能送鲜花💐吗']
2025-08-05 23:11:35 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:11:35 | DEBUG | 处理消息内容: '能送鲜花💐吗'
2025-08-05 23:11:35 | DEBUG | 消息内容 '能送鲜花💐吗' 不匹配任何命令，忽略
2025-08-05 23:11:38 | DEBUG | 收到消息: {'MsgId': 965993986, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>8927541145760461972</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:2276:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754406703</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406710, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>ed278162578c94f73e5c437fcca4d3f0_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_vnZA7XeR|v1_uM9UsxKr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': 807488959249654733, 'MsgSeq': 871430195}
2025-08-05 23:11:38 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 23:11:38 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:11:38 | INFO | 收到引用消息: 消息ID:965993986 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 23:11:38 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:11:38 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 23:11:38 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 23:11:38 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:11:38 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:11:38 | INFO |   - 消息内容: 跟着唱
2025-08-05 23:11:38 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 23:11:38 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 23:11:38 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:2276:0\n', 'Msgid': '8927541145760461972', 'NewMsgId': '8927541145760461972', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754406703', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 23:11:38 | INFO |   - 引用消息ID: 
2025-08-05 23:11:38 | INFO |   - 引用消息类型: 
2025-08-05 23:11:38 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:2276:0

2025-08-05 23:11:38 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 23:11:38 | ERROR | [VoiceTest] 处理引用语音消息异常: Extra data: line 1 column 69 (char 68)
2025-08-05 23:11:38 | ERROR | [VoiceTest] 异常堆栈: Traceback (most recent call last):
  File "C:\XYBotV2\plugins\VoiceTest\main.py", line 360, in handle_quote
    silk_base64 = await bot.download_voice(original_msg_id, voiceurl, length)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\WechatAPI\Client\tool.py", line 63, in download_voice
    json_resp = await response.json()
                ^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\aiohttp\client_reqrep.py", line 1298, in json
    return loads(stripped.decode(encoding))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\json\decoder.py", line 340, in decode
    raise JSONDecodeError("Extra data", s, end)
json.decoder.JSONDecodeError: Extra data: line 1 column 69 (char 68)

2025-08-05 23:11:39 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 处理引用语音时出错
2025-08-05 23:12:34 | DEBUG | 收到消息: {'MsgId': 328323318, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n抄这个有啥用'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406766, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_PA4rYiM/|v1_Yu97IhW6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 抄这个有啥用', 'NewMsgId': 2414476912766670919, 'MsgSeq': 871430198}
2025-08-05 23:12:34 | INFO | 收到文本消息: 消息ID:328323318 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:抄这个有啥用
2025-08-05 23:12:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '抄这个有啥用' from xiaomaochong in 48097389945@chatroom
2025-08-05 23:12:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['抄这个有啥用']
2025-08-05 23:12:35 | DEBUG | 处理消息内容: '抄这个有啥用'
2025-08-05 23:12:35 | DEBUG | 消息内容 '抄这个有啥用' 不匹配任何命令，忽略
