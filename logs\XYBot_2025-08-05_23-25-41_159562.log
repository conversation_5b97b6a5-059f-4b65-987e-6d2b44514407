2025-08-05 23:25:42 | SUCCESS | 读取主设置成功
2025-08-05 23:25:42 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 23:25:42 | INFO | 2025/08/05 23:25:42 GetRedisAddr: 127.0.0.1:6379
2025-08-05 23:25:42 | INFO | 2025/08/05 23:25:42 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 23:25:42 | INFO | 2025/08/05 23:25:42 Server start at :9000
2025-08-05 23:25:42 | SUCCESS | WechatAPI服务已启动
2025-08-05 23:25:43 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 23:25:43 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 23:25:43 | SUCCESS | 登录成功
2025-08-05 23:25:43 | SUCCESS | 已开启自动心跳
2025-08-05 23:25:43 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:25:43 | SUCCESS | 数据库初始化成功
2025-08-05 23:25:43 | SUCCESS | 定时任务已启动
2025-08-05 23:25:43 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 23:25:43 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:25:44 | INFO | 播客API初始化成功
2025-08-05 23:25:44 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:25:44 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:25:44 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 23:25:44 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 23:25:44 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 23:25:44 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 23:25:44 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 23:25:44 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 23:25:44 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 23:25:45 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 23:25:45 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 23:25:45 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 23:25:45 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 23:25:45 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 23:25:45 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 23:25:45 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 23:25:45 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 23:25:45 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 23:25:45 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 23:25:45 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 23:25:45 | DEBUG |   - 启用状态: True
2025-08-05 23:25:45 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 23:25:45 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 23:25:45 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 23:25:45 | DEBUG |   - Cookies配置: 已配置
2025-08-05 23:25:45 | DEBUG |   - 限制机制: 已禁用
2025-08-05 23:25:45 | DEBUG |   - 数字选择超时: 120秒
2025-08-05 23:25:45 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 23:25:45 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-05 23:25:45 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 23:25:45 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 23:25:45 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 23:25:45 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 23:25:45 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 23:25:45 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:25:45 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 23:25:45 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 23:25:45 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 23:25:45 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 23:25:45 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 23:25:45 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 23:25:45 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 23:25:46 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-05 23:25:46 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 23:25:46 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 23:25:46 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 23:25:47 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 23:25:47 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:25:47 | INFO | [yuanbao] 插件初始化完成
2025-08-05 23:25:47 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 23:25:47 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 23:25:47 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 23:25:47 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 23:25:47 | INFO | 处理堆积消息中
2025-08-05 23:25:47 | DEBUG | 接受到 3 条消息
2025-08-05 23:25:48 | SUCCESS | 处理堆积消息完毕
2025-08-05 23:25:48 | SUCCESS | 开始处理消息
2025-08-05 23:25:51 | DEBUG | 收到消息: {'MsgId': 1917721390, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n但我没卡了，很难上的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407563, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_j1xRItih|v1_w8TqgniS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2739396405508204139, 'MsgSeq': 871430265}
2025-08-05 23:25:51 | INFO | 收到文本消息: 消息ID:1917721390 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:但我没卡了，很难上的
2025-08-05 23:25:51 | DEBUG | [DouBaoImageToImage] 收到文本消息: '但我没卡了，很难上的' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:25:51 | DEBUG | [DouBaoImageToImage] 命令解析: ['但我没卡了，很难上的']
2025-08-05 23:25:51 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:25:51 | DEBUG | 处理消息内容: '但我没卡了，很难上的'
2025-08-05 23:25:51 | DEBUG | 消息内容 '但我没卡了，很难上的' 不匹配任何命令，忽略
2025-08-05 23:26:05 | DEBUG | 收到消息: {'MsgId': 35632575, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_if9bozh3yp522:\n主打助人为乐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407577, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_G5YF1aCC|v1_IINDVU3s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7664964924280747606, 'MsgSeq': 871430266}
2025-08-05 23:26:05 | INFO | 收到文本消息: 消息ID:35632575 来自:27852221909@chatroom 发送人:wxid_if9bozh3yp522 @:[] 内容:主打助人为乐
2025-08-05 23:26:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '主打助人为乐' from wxid_if9bozh3yp522 in 27852221909@chatroom
2025-08-05 23:26:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['主打助人为乐']
2025-08-05 23:26:05 | DEBUG | 处理消息内容: '主打助人为乐'
2025-08-05 23:26:05 | DEBUG | 消息内容 '主打助人为乐' 不匹配任何命令，忽略
2025-08-05 23:26:10 | DEBUG | 收到消息: {'MsgId': 721516310, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>煮波有实力</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2739396405508204139</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_xfxd40diz3bd22</chatusr>\n\t\t\t<displayname>初见</displayname>\n\t\t\t<content>但我没卡了，很难上的</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;810053843&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_ogMnWzXg|v1_D9yJ0oHB&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754407563</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_x4s6k999g6qg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407582, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>f270430910fe514fde4b16e6fc5da6a3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_a+qiurz4|v1_l+bRUF+L</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8502285127245021872, 'MsgSeq': 871430267}
2025-08-05 23:26:10 | DEBUG | 从群聊消息中提取发送者: wxid_x4s6k999g6qg22
2025-08-05 23:26:10 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:26:10 | INFO | 收到引用消息: 消息ID:721516310 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 内容:煮波有实力 引用类型:1
2025-08-05 23:26:10 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:26:10 | INFO | [DouBaoImageToImage] 消息内容: '煮波有实力' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 23:26:10 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['煮波有实力']
2025-08-05 23:26:10 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:26:10 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:26:10 | INFO |   - 消息内容: 煮波有实力
2025-08-05 23:26:10 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:26:10 | INFO |   - 发送人: wxid_x4s6k999g6qg22
2025-08-05 23:26:10 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '但我没卡了，很难上的', 'Msgid': '2739396405508204139', 'NewMsgId': '2739396405508204139', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '初见', 'MsgSource': '<msgsource><sequence_id>810053843</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ogMnWzXg|v1_D9yJ0oHB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754407563', 'SenderWxid': 'wxid_x4s6k999g6qg22'}
2025-08-05 23:26:10 | INFO |   - 引用消息ID: 
2025-08-05 23:26:10 | INFO |   - 引用消息类型: 
2025-08-05 23:26:10 | INFO |   - 引用消息内容: 但我没卡了，很难上的
2025-08-05 23:26:10 | INFO |   - 引用消息发送人: wxid_x4s6k999g6qg22
2025-08-05 23:26:11 | DEBUG | 收到消息: {'MsgId': 198184245, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_if9bozh3yp522:\n就喜欢挑战高难度'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407583, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_qq5ZRBR9|v1_DUE6yxvL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4019513815422608173, 'MsgSeq': 871430268}
2025-08-05 23:26:11 | INFO | 收到文本消息: 消息ID:198184245 来自:27852221909@chatroom 发送人:wxid_if9bozh3yp522 @:[] 内容:就喜欢挑战高难度
2025-08-05 23:26:11 | DEBUG | [DouBaoImageToImage] 收到文本消息: '就喜欢挑战高难度' from wxid_if9bozh3yp522 in 27852221909@chatroom
2025-08-05 23:26:11 | DEBUG | [DouBaoImageToImage] 命令解析: ['就喜欢挑战高难度']
2025-08-05 23:26:11 | DEBUG | 处理消息内容: '就喜欢挑战高难度'
2025-08-05 23:26:11 | DEBUG | 消息内容 '就喜欢挑战高难度' 不匹配任何命令，忽略
2025-08-05 23:26:13 | DEBUG | 收到消息: {'MsgId': 604417869, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_if9bozh3yp522:\n哈哈哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407585, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_tpWGRiRs|v1_SIuG+yGJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9111939634437585716, 'MsgSeq': 871430269}
2025-08-05 23:26:13 | INFO | 收到文本消息: 消息ID:604417869 来自:27852221909@chatroom 发送人:wxid_if9bozh3yp522 @:[] 内容:哈哈哈哈哈哈
2025-08-05 23:26:13 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈哈哈哈哈' from wxid_if9bozh3yp522 in 27852221909@chatroom
2025-08-05 23:26:13 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈哈哈哈哈']
2025-08-05 23:26:13 | DEBUG | 处理消息内容: '哈哈哈哈哈哈'
2025-08-05 23:26:13 | DEBUG | 消息内容 '哈哈哈哈哈哈' 不匹配任何命令，忽略
2025-08-05 23:26:17 | DEBUG | 收到消息: {'MsgId': 16033176, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n因为老板消费了9万'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407589, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_qUYRAgu7|v1_XbkmX9K4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5701236698365124517, 'MsgSeq': 871430270}
2025-08-05 23:26:17 | INFO | 收到文本消息: 消息ID:16033176 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:因为老板消费了9万
2025-08-05 23:26:17 | DEBUG | [DouBaoImageToImage] 收到文本消息: '因为老板消费了9万' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:26:17 | DEBUG | [DouBaoImageToImage] 命令解析: ['因为老板消费了9万']
2025-08-05 23:26:17 | DEBUG | 处理消息内容: '因为老板消费了9万'
2025-08-05 23:26:17 | DEBUG | 消息内容 '因为老板消费了9万' 不匹配任何命令，忽略
2025-08-05 23:26:28 | DEBUG | 收到消息: {'MsgId': 1051651641, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n[囧]好吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407600, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_4Vbh4gU6|v1_JRxVLGYW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 256362873892184503, 'MsgSeq': 871430271}
2025-08-05 23:26:28 | INFO | 收到文本消息: 消息ID:1051651641 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:[囧]好吧
2025-08-05 23:26:28 | DEBUG | [DouBaoImageToImage] 收到文本消息: '[囧]好吧' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:26:28 | DEBUG | [DouBaoImageToImage] 命令解析: ['[囧]好吧']
2025-08-05 23:26:28 | DEBUG | 处理消息内容: '[囧]好吧'
2025-08-05 23:26:28 | DEBUG | 消息内容 '[囧]好吧' 不匹配任何命令，忽略
2025-08-05 23:26:31 | DEBUG | 收到消息: {'MsgId': 524143902, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n不得给老板点福利'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407603, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_y/H3tDLT|v1_/N6nqblq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8741854898956187019, 'MsgSeq': 871430272}
2025-08-05 23:26:31 | INFO | 收到文本消息: 消息ID:524143902 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:不得给老板点福利
2025-08-05 23:26:31 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不得给老板点福利' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:26:31 | DEBUG | [DouBaoImageToImage] 命令解析: ['不得给老板点福利']
2025-08-05 23:26:31 | DEBUG | 处理消息内容: '不得给老板点福利'
2025-08-05 23:26:31 | DEBUG | 消息内容 '不得给老板点福利' 不匹配任何命令，忽略
2025-08-05 23:26:34 | DEBUG | 收到消息: {'MsgId': 815597455, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n其实只消费了5.5'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407603, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_m9to64Ce|v1_tB5KhEh6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3940967129949283359, 'MsgSeq': 871430273}
2025-08-05 23:26:34 | INFO | 收到文本消息: 消息ID:815597455 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:其实只消费了5.5
2025-08-05 23:26:34 | DEBUG | [DouBaoImageToImage] 收到文本消息: '其实只消费了5.5' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:26:34 | DEBUG | [DouBaoImageToImage] 命令解析: ['其实只消费了5.5']
2025-08-05 23:26:34 | DEBUG | 处理消息内容: '其实只消费了5.5'
2025-08-05 23:26:34 | DEBUG | 消息内容 '其实只消费了5.5' 不匹配任何命令，忽略
2025-08-05 23:26:36 | DEBUG | 收到消息: {'MsgId': 1344621733, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n帮我打打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407605, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_f07sZkrZ|v1_SNow5PgN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4865649411438591756, 'MsgSeq': 871430274}
2025-08-05 23:26:36 | INFO | 收到文本消息: 消息ID:1344621733 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:帮我打打
2025-08-05 23:26:36 | DEBUG | [DouBaoImageToImage] 收到文本消息: '帮我打打' from wxid_idzryo4rneok22 in 27852221909@chatroom
2025-08-05 23:26:36 | DEBUG | [DouBaoImageToImage] 命令解析: ['帮我打打']
2025-08-05 23:26:36 | DEBUG | 处理消息内容: '帮我打打'
2025-08-05 23:26:36 | DEBUG | 消息内容 '帮我打打' 不匹配任何命令，忽略
2025-08-05 23:26:38 | DEBUG | 收到消息: {'MsgId': 2047816449, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="27852221909@chatroom" type="1" idbuffer="media:0_0" md5="cfd3cc73fbc39eca8dc39b912da89576" len="26820" productid="" androidmd5="cfd3cc73fbc39eca8dc39b912da89576" androidlen="26820" s60v3md5="cfd3cc73fbc39eca8dc39b912da89576" s60v3len="26820" s60v5md5="cfd3cc73fbc39eca8dc39b912da89576" s60v5len="26820" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=cfd3cc73fbc39eca8dc39b912da89576&amp;filekey=3043020101042f302d02016e0402534804206366643363633733666263333965636138646333396239313264613839353736020268c4040d00000004627466730000000132&amp;hy=SH&amp;storeid=266f56c6d000d220ed8430d640000006e01004fb153482773f031567915c1b&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=4fd920f5b0eb4d8ef97dc4b8e79a5052&amp;filekey=3043020101042f302d02016e0402534804203466643932306635623065623464386566393764633462386537396135303532020268d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=266f56c6d000dff8fd8430d640000006e02004fb253482773f031567915c30&amp;ef=2&amp;bizid=1022" aeskey="33bad4372f904110aea2202300878f73" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=c458d5502735c0806c772116af8b6556&amp;filekey=3043020101042f302d02016e040253480420633435386435353032373335633038303663373732313136616638623635353602023010040d00000004627466730000000132&amp;hy=SH&amp;storeid=266f56c6d000e9a52d8430d640000006e03004fb353482773f031567915c3c&amp;ef=3&amp;bizid=1022" externmd5="6f0eb0f9685e53e75feb35afa19f810b" width="467" height="503" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407607, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_OVNmnm9B|v1_kPW1dehW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3310270956601343271, 'MsgSeq': 871430275}
2025-08-05 23:26:38 | INFO | 收到表情消息: 消息ID:2047816449 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 MD5:cfd3cc73fbc39eca8dc39b912da89576 大小:26820
2025-08-05 23:26:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3310270956601343271
2025-08-05 23:26:41 | DEBUG | 收到消息: {'MsgId': 1475355562, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n有3.5在别的团'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407613, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_nk18+XGM|v1_Fmik4zw6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8398697528579487760, 'MsgSeq': 871430276}
2025-08-05 23:26:41 | INFO | 收到文本消息: 消息ID:1475355562 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:有3.5在别的团
2025-08-05 23:26:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '有3.5在别的团' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:26:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['有3.5在别的团']
2025-08-05 23:26:41 | DEBUG | 处理消息内容: '有3.5在别的团'
2025-08-05 23:26:41 | DEBUG | 消息内容 '有3.5在别的团' 不匹配任何命令，忽略
2025-08-05 23:26:44 | DEBUG | 收到消息: {'MsgId': 1982513122, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1938" length="2828" bufid="0" aeskey="0845f2b495ad56d9c52090a79aba2021" voiceurl="3052020100044b30490201000204a95c809d02032df9270204780893240204689222be042436316334656137632d383836662d343237322d613763302d35623238363266313033626402040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600522326080525e376c312be8101" fromusername="wxid_ubbh6q832tcs21" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407615, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_WTmFVRFM|v1_P0F0LYy6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段语音', 'NewMsgId': 1889589502276627264, 'MsgSeq': 871430277}
2025-08-05 23:26:44 | INFO | 收到语音消息: 消息ID:1982513122 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1938" length="2828" bufid="0" aeskey="0845f2b495ad56d9c52090a79aba2021" voiceurl="3052020100044b30490201000204a95c809d02032df9270204780893240204689222be042436316334656137632d383836662d343237322d613763302d35623238363266313033626402040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600522326080525e376c312be8101" fromusername="wxid_ubbh6q832tcs21" /></msg>
2025-08-05 23:26:44 | DEBUG | [VoiceTest] 缓存语音 MsgId: 1982513122
2025-08-05 23:26:44 | DEBUG | [VoiceTest] 缓存语音 NewMsgId: 1889589502276627264
2025-08-05 23:26:44 | INFO | [VoiceTest] 已缓存语音消息: MsgId=1982513122, NewMsgId=1889589502276627264
2025-08-05 23:26:51 | DEBUG | 收到消息: {'MsgId': 2089934720, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>1889589502276627264</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:1938:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754407614</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407622, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>6dd67691acd7c7aa515f49008675dec9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_H7Ky/JBI|v1_jesYjnNM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': 8757281950429666308, 'MsgSeq': 871430278}
2025-08-05 23:26:51 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 23:26:51 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:26:51 | INFO | 收到引用消息: 消息ID:2089934720 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 23:26:51 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:26:51 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 23:26:51 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 23:26:51 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:26:51 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:26:51 | INFO |   - 消息内容: 跟着唱
2025-08-05 23:26:51 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 23:26:51 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 23:26:51 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:1938:0\n', 'Msgid': '1889589502276627264', 'NewMsgId': '1889589502276627264', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754407614', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 23:26:51 | INFO |   - 引用消息ID: 
2025-08-05 23:26:51 | INFO |   - 引用消息类型: 
2025-08-05 23:26:51 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:1938:0

2025-08-05 23:26:51 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 23:26:51 | INFO | [VoiceTest] 下载参数: msg_id=1982513122, voiceurl=3052020100044b30490201000204a95c809d02032df9270204..., length=2828
2025-08-05 23:26:51 | DEBUG | [TempFileManager] 创建临时文件: C:\XYBotV2\temp\voice_test\1889589502276627264_r93xgvmi.silk
2025-08-05 23:26:52 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:1720 格式:amr 音频base64略
2025-08-05 23:26:53 | DEBUG | 收到消息: {'MsgId': 858443985, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_idzryo4rneok22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>老板实诚人</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8398697528579487760</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_xfxd40diz3bd22</chatusr>\n\t\t\t<displayname>初见</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_U2hKwRv6|v1_fk6ovSIx&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n有3.5在别的团</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754407613</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_idzryo4rneok22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407624, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>91918973f031aed5dad121582acd18c6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_a5FC4Buc|v1_IXIWzWNS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7480877963156360266, 'MsgSeq': 871430281}
2025-08-05 23:26:53 | DEBUG | 从群聊消息中提取发送者: wxid_idzryo4rneok22
2025-08-05 23:26:53 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:26:53 | INFO | 收到引用消息: 消息ID:858443985 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 内容:老板实诚人 引用类型:1
2025-08-05 23:26:53 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:26:53 | INFO | [DouBaoImageToImage] 消息内容: '老板实诚人' from wxid_idzryo4rneok22 in 27852221909@chatroom
2025-08-05 23:26:53 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['老板实诚人']
2025-08-05 23:26:53 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:26:53 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:26:53 | INFO |   - 消息内容: 老板实诚人
2025-08-05 23:26:53 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:26:53 | INFO |   - 发送人: wxid_idzryo4rneok22
2025-08-05 23:26:53 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n有3.5在别的团', 'Msgid': '8398697528579487760', 'NewMsgId': '8398697528579487760', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '初见', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_U2hKwRv6|v1_fk6ovSIx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754407613', 'SenderWxid': 'wxid_idzryo4rneok22'}
2025-08-05 23:26:53 | INFO |   - 引用消息ID: 
2025-08-05 23:26:53 | INFO |   - 引用消息类型: 
2025-08-05 23:26:53 | INFO |   - 引用消息内容: 
有3.5在别的团
2025-08-05 23:26:53 | INFO |   - 引用消息发送人: wxid_idzryo4rneok22
2025-08-05 23:27:02 | DEBUG | 收到消息: {'MsgId': 1915883513, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n不能多了 下个月初还要考试'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407634, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_i+l3D1jB|v1_4gMU5Mct</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7846570259542377160, 'MsgSeq': 871430282}
2025-08-05 23:27:02 | INFO | 收到文本消息: 消息ID:1915883513 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:不能多了 下个月初还要考试
2025-08-05 23:27:02 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不能多了 下个月初还要考试' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:27:02 | DEBUG | [DouBaoImageToImage] 命令解析: ['不能多了', '下个月初还要考试']
2025-08-05 23:27:02 | DEBUG | 处理消息内容: '不能多了 下个月初还要考试'
2025-08-05 23:27:02 | DEBUG | 消息内容 '不能多了 下个月初还要考试' 不匹配任何命令，忽略
2025-08-05 23:27:04 | DEBUG | 收到消息: {'MsgId': 566857094, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n不过，我cp有2w也是我给消费的，嘻嘻'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407635, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_nFitXYT2|v1_vcMfo0aA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8878667302668313877, 'MsgSeq': 871430283}
2025-08-05 23:27:04 | INFO | 收到文本消息: 消息ID:566857094 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:不过，我cp有2w也是我给消费的，嘻嘻
2025-08-05 23:27:04 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不过，我cp有2w也是我给消费的，嘻嘻' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:27:04 | DEBUG | [DouBaoImageToImage] 命令解析: ['不过，我cp有2w也是我给消费的，嘻嘻']
2025-08-05 23:27:04 | DEBUG | 处理消息内容: '不过，我cp有2w也是我给消费的，嘻嘻'
2025-08-05 23:27:04 | DEBUG | 消息内容 '不过，我cp有2w也是我给消费的，嘻嘻' 不匹配任何命令，忽略
2025-08-05 23:27:06 | DEBUG | 收到消息: {'MsgId': 1938492589, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="7a62cdb74d27fa268d10cf6cc7dfcbfc" len = "136172" productid="" androidmd5="7a62cdb74d27fa268d10cf6cc7dfcbfc" androidlen="136172" s60v3md5 = "7a62cdb74d27fa268d10cf6cc7dfcbfc" s60v3len="136172" s60v5md5 = "7a62cdb74d27fa268d10cf6cc7dfcbfc" s60v5len="136172" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=7a62cdb74d27fa268d10cf6cc7dfcbfc&amp;filekey=30440201010430302e02016e040253480420376136326364623734643237666132363864313063663663633764666362666302030213ec040d00000004627466730000000132&amp;hy=SH&amp;storeid=264bcffe40000f01c53cf07e80000006e01004fb153482916ab40b6f0ff45e&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=d9ff6e7ad1ed285ce5ac65028d2be315&amp;filekey=30440201010430302e02016e040253480420643966663665376164316564323835636535616336353032386432626533313502030213f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=264bcffe40001dee853cf07e80000006e02004fb253482916ab40b6f0ff471&amp;ef=2&amp;bizid=1022" aeskey= "89b06b9e0b004bbd913e166c1ec2d3ab" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=30db96aa847472ce8d6b0f4e36279b8d&amp;filekey=3043020101042f302d02016e040253480420333064623936616138343734373263653864366230663465333632373962386402023220040d00000004627466730000000132&amp;hy=SH&amp;storeid=264bcffe40002d04553cf07e80000006e03004fb353482916ab40b6f0ff48a&amp;ef=3&amp;bizid=1022" externmd5 = "8f0c8a8d09eeca9115e4785a6a3804c3" width= "300" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407636, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_aiK1IqCu|v1_lAZf5rwe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6242158242457255826, 'MsgSeq': 871430284}
2025-08-05 23:27:06 | INFO | 收到表情消息: 消息ID:1938492589 来自:27852221909@chatroom 发送人:tianen532965049 MD5:7a62cdb74d27fa268d10cf6cc7dfcbfc 大小:136172
2025-08-05 23:27:06 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6242158242457255826
2025-08-05 23:27:11 | DEBUG | 收到消息: {'MsgId': 297877110, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>消费榜前十都给打了吧</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5701236698365124517</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>tianen532965049</chatusr>\n\t\t\t<displayname>饿飞</displayname>\n\t\t\t<content>因为老板消费了9万</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;766702473&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_13bWdd3l|v1_w7d7DUN/&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754407589</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_snv13qf05qjx11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407643, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>230ba2cac840fb3d5736ece0f2daa547_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_xrdgrYpu|v1_GtBvzRj/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5760075096234701289, 'MsgSeq': 871430285}
2025-08-05 23:27:11 | DEBUG | 从群聊消息中提取发送者: wxid_snv13qf05qjx11
2025-08-05 23:27:11 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:27:11 | INFO | 收到引用消息: 消息ID:297877110 来自:27852221909@chatroom 发送人:wxid_snv13qf05qjx11 内容:消费榜前十都给打了吧 引用类型:1
2025-08-05 23:27:11 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:27:11 | INFO | [DouBaoImageToImage] 消息内容: '消费榜前十都给打了吧' from wxid_snv13qf05qjx11 in 27852221909@chatroom
2025-08-05 23:27:11 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['消费榜前十都给打了吧']
2025-08-05 23:27:11 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:27:11 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:27:11 | INFO |   - 消息内容: 消费榜前十都给打了吧
2025-08-05 23:27:11 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:27:11 | INFO |   - 发送人: wxid_snv13qf05qjx11
2025-08-05 23:27:11 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '因为老板消费了9万', 'Msgid': '5701236698365124517', 'NewMsgId': '5701236698365124517', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '饿飞', 'MsgSource': '<msgsource><sequence_id>766702473</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_13bWdd3l|v1_w7d7DUN/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754407589', 'SenderWxid': 'wxid_snv13qf05qjx11'}
2025-08-05 23:27:11 | INFO |   - 引用消息ID: 
2025-08-05 23:27:11 | INFO |   - 引用消息类型: 
2025-08-05 23:27:11 | INFO |   - 引用消息内容: 因为老板消费了9万
2025-08-05 23:27:11 | INFO |   - 引用消息发送人: wxid_snv13qf05qjx11
2025-08-05 23:27:23 | DEBUG | 收到消息: {'MsgId': 396621614, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '27852221909@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_snv13qf05qjx11</fromusername>\n  <chatusername>27852221909@chatroom</chatusername>\n  <pattedusername>tianen532965049</pattedusername>\n  <patsuffix><![CDATA[随手给我甩了一百亿]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_snv13qf05qjx11}" 拍了拍 "${tianen532965049}" 随手给我甩了一百亿]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407653, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8940776034262172790, 'MsgSeq': 871430286}
2025-08-05 23:27:23 | DEBUG | 系统消息类型: pat
2025-08-05 23:27:23 | INFO | 收到拍一拍消息: 消息ID:396621614 来自:27852221909@chatroom 发送人:27852221909@chatroom 拍者:wxid_snv13qf05qjx11 被拍:tianen532965049 后缀:随手给我甩了一百亿
2025-08-05 23:27:23 | DEBUG | [PatReply] 被拍者 tianen532965049 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-08-05 23:27:26 | DEBUG | 收到消息: {'MsgId': 1226494148, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n9月'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407658, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_/8G9gWhA|v1_JGNKnbz9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4171230599713515001, 'MsgSeq': 871430287}
2025-08-05 23:27:26 | INFO | 收到文本消息: 消息ID:1226494148 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:9月
2025-08-05 23:27:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '9月' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:27:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['9月']
2025-08-05 23:27:26 | DEBUG | 处理消息内容: '9月'
2025-08-05 23:27:26 | DEBUG | 消息内容 '9月' 不匹配任何命令，忽略
2025-08-05 23:27:33 | DEBUG | 收到消息: {'MsgId': 175107249, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n这个月时间不够 '}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407665, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_h7iZkB8e|v1_Gf8e5ta5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1370805495895644400, 'MsgSeq': 871430288}
2025-08-05 23:27:33 | INFO | 收到文本消息: 消息ID:175107249 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:这个月时间不够 
2025-08-05 23:27:33 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这个月时间不够' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:27:33 | DEBUG | [DouBaoImageToImage] 命令解析: ['这个月时间不够']
2025-08-05 23:27:33 | DEBUG | 处理消息内容: '这个月时间不够'
2025-08-05 23:27:33 | DEBUG | 消息内容 '这个月时间不够' 不匹配任何命令，忽略
2025-08-05 23:27:36 | DEBUG | 收到消息: {'MsgId': 667035957, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="7a62cdb74d27fa268d10cf6cc7dfcbfc" len = "136172" productid="" androidmd5="7a62cdb74d27fa268d10cf6cc7dfcbfc" androidlen="136172" s60v3md5 = "7a62cdb74d27fa268d10cf6cc7dfcbfc" s60v3len="136172" s60v5md5 = "7a62cdb74d27fa268d10cf6cc7dfcbfc" s60v5len="136172" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=7a62cdb74d27fa268d10cf6cc7dfcbfc&amp;filekey=30440201010430302e02016e040253480420376136326364623734643237666132363864313063663663633764666362666302030213ec040d00000004627466730000000132&amp;hy=SH&amp;storeid=264bcffe40000f01c53cf07e80000006e01004fb153482916ab40b6f0ff45e&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=d9ff6e7ad1ed285ce5ac65028d2be315&amp;filekey=30440201010430302e02016e040253480420643966663665376164316564323835636535616336353032386432626533313502030213f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=264bcffe40001dee853cf07e80000006e02004fb253482916ab40b6f0ff471&amp;ef=2&amp;bizid=1022" aeskey= "89b06b9e0b004bbd913e166c1ec2d3ab" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=30db96aa847472ce8d6b0f4e36279b8d&amp;filekey=3043020101042f302d02016e040253480420333064623936616138343734373263653864366230663465333632373962386402023220040d00000004627466730000000132&amp;hy=SH&amp;storeid=264bcffe40002d04553cf07e80000006e03004fb353482916ab40b6f0ff48a&amp;ef=3&amp;bizid=1022" externmd5 = "8f0c8a8d09eeca9115e4785a6a3804c3" width= "300" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407668, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_fLKyiCx+|v1_CO6TpGwE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 663384416050837025, 'MsgSeq': 871430289}
2025-08-05 23:27:36 | INFO | 收到表情消息: 消息ID:667035957 来自:27852221909@chatroom 发送人:tianen532965049 MD5:7a62cdb74d27fa268d10cf6cc7dfcbfc 大小:136172
2025-08-05 23:27:36 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 663384416050837025
2025-08-05 23:27:43 | DEBUG | 收到消息: {'MsgId': 154107102, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n看好你哟🫰🏻'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407675, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_eBL5EaGa|v1_cN+Z7Tad</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6673735902397824091, 'MsgSeq': 871430290}
2025-08-05 23:27:43 | INFO | 收到文本消息: 消息ID:154107102 来自:27852221909@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:看好你哟🫰🏻
2025-08-05 23:27:43 | DEBUG | [DouBaoImageToImage] 收到文本消息: '看好你哟🫰🏻' from wxid_snv13qf05qjx11 in 27852221909@chatroom
2025-08-05 23:27:43 | DEBUG | [DouBaoImageToImage] 命令解析: ['看好你哟🫰🏻']
2025-08-05 23:27:43 | DEBUG | 处理消息内容: '看好你哟🫰🏻'
2025-08-05 23:27:43 | DEBUG | 消息内容 '看好你哟🫰🏻' 不匹配任何命令，忽略
2025-08-05 23:27:45 | DEBUG | 收到消息: {'MsgId': 1878942265, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n给打吗，给打我就要甩号了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407677, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_N56Ms0+I|v1_XpwUFU2J</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2268021125115666968, 'MsgSeq': 871430291}
2025-08-05 23:27:45 | INFO | 收到文本消息: 消息ID:1878942265 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:给打吗，给打我就要甩号了
2025-08-05 23:27:45 | DEBUG | [DouBaoImageToImage] 收到文本消息: '给打吗，给打我就要甩号了' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:27:45 | DEBUG | [DouBaoImageToImage] 命令解析: ['给打吗，给打我就要甩号了']
2025-08-05 23:27:45 | DEBUG | 处理消息内容: '给打吗，给打我就要甩号了'
2025-08-05 23:27:45 | DEBUG | 消息内容 '给打吗，给打我就要甩号了' 不匹配任何命令，忽略
2025-08-05 23:27:52 | DEBUG | 收到消息: {'MsgId': 548832226, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n打 老板'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407684, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_mhVu47xP|v1_FtNU8Fp0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2262814375062378958, 'MsgSeq': 871430292}
2025-08-05 23:27:52 | INFO | 收到文本消息: 消息ID:548832226 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:打 老板
2025-08-05 23:27:52 | DEBUG | [DouBaoImageToImage] 收到文本消息: '打 老板' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:27:52 | DEBUG | [DouBaoImageToImage] 命令解析: ['打', '老板']
2025-08-05 23:27:52 | DEBUG | 处理消息内容: '打 老板'
2025-08-05 23:27:52 | DEBUG | 消息内容 '打 老板' 不匹配任何命令，忽略
2025-08-05 23:27:55 | DEBUG | 收到消息: {'MsgId': 700110551, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n我排位挺菜的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407687, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_+ZSAlJCh|v1_bdfsi++K</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1414908542924459649, 'MsgSeq': 871430293}
2025-08-05 23:27:55 | INFO | 收到文本消息: 消息ID:700110551 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:我排位挺菜的
2025-08-05 23:27:55 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我排位挺菜的' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:27:55 | DEBUG | [DouBaoImageToImage] 命令解析: ['我排位挺菜的']
2025-08-05 23:27:55 | DEBUG | 处理消息内容: '我排位挺菜的'
2025-08-05 23:27:55 | DEBUG | 消息内容 '我排位挺菜的' 不匹配任何命令，忽略
2025-08-05 23:28:45 | DEBUG | 收到消息: {'MsgId': 1741192764, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n活动结束消费榜前十的排位有宋公子买单'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407737, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_aU9yGdCD|v1_V7g0u+8X</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4561749402955072967, 'MsgSeq': 871430294}
2025-08-05 23:28:45 | INFO | 收到文本消息: 消息ID:1741192764 来自:27852221909@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:活动结束消费榜前十的排位有宋公子买单
2025-08-05 23:28:45 | DEBUG | [DouBaoImageToImage] 收到文本消息: '活动结束消费榜前十的排位有宋公子买单' from wxid_snv13qf05qjx11 in 27852221909@chatroom
2025-08-05 23:28:45 | DEBUG | [DouBaoImageToImage] 命令解析: ['活动结束消费榜前十的排位有宋公子买单']
2025-08-05 23:28:45 | DEBUG | 处理消息内容: '活动结束消费榜前十的排位有宋公子买单'
2025-08-05 23:28:45 | DEBUG | 消息内容 '活动结束消费榜前十的排位有宋公子买单' 不匹配任何命令，忽略
2025-08-05 23:29:00 | DEBUG | 收到消息: {'MsgId': 497792165, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n活动结束消费榜前十的排位有宋公子买单'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407752, 'MsgSource': '<msgsource>\n\t<eggIncluded>1</eggIncluded>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_MTVm+MNo|v1_1vpM9CgI</signature>\n</msgsource>\n', 'NewMsgId': 8773895460152520185, 'MsgSeq': 871430295}
2025-08-05 23:29:00 | INFO | 收到文本消息: 消息ID:497792165 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:活动结束消费榜前十的排位有宋公子买单
2025-08-05 23:29:00 | DEBUG | [DouBaoImageToImage] 收到文本消息: '活动结束消费榜前十的排位有宋公子买单' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 23:29:00 | DEBUG | [DouBaoImageToImage] 命令解析: ['活动结束消费榜前十的排位有宋公子买单']
2025-08-05 23:29:00 | DEBUG | 处理消息内容: '活动结束消费榜前十的排位有宋公子买单'
2025-08-05 23:29:00 | DEBUG | 消息内容 '活动结束消费榜前十的排位有宋公子买单' 不匹配任何命令，忽略
2025-08-05 23:29:35 | DEBUG | 收到消息: {'MsgId': 1635181041, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_if9bozh3yp522:\n活动结束消费榜前十的排位有宋公子买单'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407787, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>18</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_wwmPkDqY|v1_6OZQfTOS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5138792393511125661, 'MsgSeq': 871430296}
2025-08-05 23:29:35 | INFO | 收到文本消息: 消息ID:1635181041 来自:27852221909@chatroom 发送人:wxid_if9bozh3yp522 @:[] 内容:活动结束消费榜前十的排位有宋公子买单
2025-08-05 23:29:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '活动结束消费榜前十的排位有宋公子买单' from wxid_if9bozh3yp522 in 27852221909@chatroom
2025-08-05 23:29:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['活动结束消费榜前十的排位有宋公子买单']
2025-08-05 23:29:35 | DEBUG | 处理消息内容: '活动结束消费榜前十的排位有宋公子买单'
2025-08-05 23:29:35 | DEBUG | 消息内容 '活动结束消费榜前十的排位有宋公子买单' 不匹配任何命令，忽略
2025-08-05 23:29:44 | DEBUG | 收到消息: {'MsgId': 253156777, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n活动结束消费榜前十的排位有宋公子买单'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407795, 'MsgSource': '<msgsource>\n\t<eggIncluded>1</eggIncluded>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_LZb3UVcC|v1_W44PE+kd</signature>\n</msgsource>\n', 'NewMsgId': 2617743718961474874, 'MsgSeq': 871430297}
2025-08-05 23:29:44 | INFO | 收到文本消息: 消息ID:253156777 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:活动结束消费榜前十的排位有宋公子买单
2025-08-05 23:29:44 | DEBUG | [DouBaoImageToImage] 收到文本消息: '活动结束消费榜前十的排位有宋公子买单' from wxid_ugv5ryus4gz622 in 27852221909@chatroom
2025-08-05 23:29:44 | DEBUG | [DouBaoImageToImage] 命令解析: ['活动结束消费榜前十的排位有宋公子买单']
2025-08-05 23:29:44 | DEBUG | 处理消息内容: '活动结束消费榜前十的排位有宋公子买单'
2025-08-05 23:29:44 | DEBUG | 消息内容 '活动结束消费榜前十的排位有宋公子买单' 不匹配任何命令，忽略
2025-08-05 23:29:58 | DEBUG | 收到消息: {'MsgId': 1518890299, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_if9bozh3yp522:\n虽然我不知道宋公子是谁 但是看你们发 我也发一下吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407809, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_4kTRT9tx|v1_c2erNawa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4257261793593570213, 'MsgSeq': 871430298}
2025-08-05 23:29:58 | INFO | 收到文本消息: 消息ID:1518890299 来自:27852221909@chatroom 发送人:wxid_if9bozh3yp522 @:[] 内容:虽然我不知道宋公子是谁 但是看你们发 我也发一下吧
2025-08-05 23:29:58 | DEBUG | [DouBaoImageToImage] 收到文本消息: '虽然我不知道宋公子是谁 但是看你们发 我也发一下吧' from wxid_if9bozh3yp522 in 27852221909@chatroom
2025-08-05 23:29:58 | DEBUG | [DouBaoImageToImage] 命令解析: ['虽然我不知道宋公子是谁', '但是看你们发', '我也发一下吧']
2025-08-05 23:29:58 | DEBUG | 处理消息内容: '虽然我不知道宋公子是谁 但是看你们发 我也发一下吧'
2025-08-05 23:29:58 | DEBUG | 消息内容 '虽然我不知道宋公子是谁 但是看你们发 我也发一下吧' 不匹配任何命令，忽略
2025-08-05 23:30:00 | DEBUG | 收到消息: {'MsgId': 1383276890, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_if9bozh3yp522:\n哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407811, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_cM7I0C4O|v1_z+rp+UoO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8672993538111655309, 'MsgSeq': 871430299}
2025-08-05 23:30:00 | INFO | 收到文本消息: 消息ID:1383276890 来自:27852221909@chatroom 发送人:wxid_if9bozh3yp522 @:[] 内容:哈哈哈
2025-08-05 23:30:00 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈哈' from wxid_if9bozh3yp522 in 27852221909@chatroom
2025-08-05 23:30:00 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈哈']
2025-08-05 23:30:00 | DEBUG | 处理消息内容: '哈哈哈'
2025-08-05 23:30:00 | DEBUG | 消息内容 '哈哈哈' 不匹配任何命令，忽略
2025-08-05 23:30:31 | DEBUG | 收到消息: {'MsgId': 2130302316, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n活动结束消费榜前十的排位有宋公子买单'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407843, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>18</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_X4+wsl10|v1_FKbSMONy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3579668433613838017, 'MsgSeq': 871430300}
2025-08-05 23:30:31 | INFO | 收到文本消息: 消息ID:2130302316 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:活动结束消费榜前十的排位有宋公子买单
2025-08-05 23:30:31 | DEBUG | [DouBaoImageToImage] 收到文本消息: '活动结束消费榜前十的排位有宋公子买单' from wxid_idzryo4rneok22 in 27852221909@chatroom
2025-08-05 23:30:31 | DEBUG | [DouBaoImageToImage] 命令解析: ['活动结束消费榜前十的排位有宋公子买单']
2025-08-05 23:30:31 | DEBUG | 处理消息内容: '活动结束消费榜前十的排位有宋公子买单'
2025-08-05 23:30:31 | DEBUG | 消息内容 '活动结束消费榜前十的排位有宋公子买单' 不匹配任何命令，忽略
2025-08-05 23:31:51 | DEBUG | [TempFileManager] 已清理文件: C:\XYBotV2\temp\voice_test\1889589502276627264_r93xgvmi.silk
2025-08-05 23:34:44 | DEBUG | 收到消息: {'MsgId': 1388789492, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>复读</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5684799875448054452</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;atuserlist&gt;wxid_ubbh6q832tcs21&lt;/atuserlist&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;3&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_MgAkbpB7|v1_HgyHD9Ii&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n@郭\u2005处理引用语音时出错</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754406961</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754408096, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>04cb9a8e59a0d30c32728d4270936d1b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_Cu7j2BgP|v1_j90GDFXI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 复读', 'NewMsgId': *******************, 'MsgSeq': 871430301}
2025-08-05 23:34:44 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 23:34:44 | DEBUG | 检测到复读命令
2025-08-05 23:34:44 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:34:44 | INFO | 收到引用消息: 消息ID:1388789492 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:复读 引用类型:1
2025-08-05 23:34:44 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:34:44 | INFO | [DouBaoImageToImage] 消息内容: '复读' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 23:34:44 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['复读']
2025-08-05 23:34:44 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:34:44 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:34:44 | INFO |   - 消息内容: 复读
2025-08-05 23:34:44 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 23:34:44 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 23:34:44 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n@郭\u2005处理引用语音时出错', 'Msgid': '5684799875448054452', 'NewMsgId': '5684799875448054452', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ubbh6q832tcs21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<silence>1</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_MgAkbpB7|v1_HgyHD9Ii</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754406961', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 23:34:44 | INFO |   - 引用消息ID: 
2025-08-05 23:34:44 | INFO |   - 引用消息类型: 
2025-08-05 23:34:44 | INFO |   - 引用消息内容: 
@郭 处理引用语音时出错
2025-08-05 23:34:44 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 23:38:27 | DEBUG | 收到消息: {'MsgId': 490879770, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="14143c123a1aa06a2d1ff685a90838a1" len = "51397" productid="" androidmd5="14143c123a1aa06a2d1ff685a90838a1" androidlen="51397" s60v3md5 = "14143c123a1aa06a2d1ff685a90838a1" s60v3len="51397" s60v5md5 = "14143c123a1aa06a2d1ff685a90838a1" s60v5len="51397" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=14143c123a1aa06a2d1ff685a90838a1&amp;filekey=30440201010430302e02016e0402534804203134313433633132336131616130366132643166663638356139303833386131020300c8c5040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313034313331353132313630303061393634633930643331636332343537623435303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=e43e7bfa692a0b8afdb9043739a01820&amp;filekey=30440201010430302e02016e0402534804206534336537626661363932613062386166646239303433373339613031383230020300c8d0040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313034313331353132313630303062633732323930643331636332343537623435303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey= "8e2307cc20734162bea0224d96652fe3" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=b574ffb0f67889f605e6ee571ac088d6&amp;filekey=3043020101042f302d02016e0402534804206235373466666230663637383839663630356536656535373161633038386436020258b0040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032313034313331353132313630303063373638393930643331636332343537623435303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5 = "b568a104965cd07a3b13bd71dcc56f33" width= "600" height= "542" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "CgnmiJHnnaHkuoY=" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754408319, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_roe/oMJZ|v1_QB5JsUjX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1022558250797372449, 'MsgSeq': 871430302}
2025-08-05 23:38:27 | INFO | 收到表情消息: 消息ID:490879770 来自:27852221909@chatroom 发送人:tianen532965049 MD5:14143c123a1aa06a2d1ff685a90838a1 大小:51397
2025-08-05 23:38:27 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1022558250797372449
2025-08-05 23:38:59 | DEBUG | 收到消息: {'MsgId': 1604156608, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n@饿飞\u2005宋公子威武'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754408351, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[tianen532965049]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_fprrz+7/|v1_djxtZdKL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2652888132237470357, 'MsgSeq': 871430303}
2025-08-05 23:38:59 | INFO | 收到文本消息: 消息ID:1604156608 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:['tianen532965049'] 内容:@饿飞 宋公子威武
2025-08-05 23:38:59 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@饿飞 宋公子威武' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:38:59 | DEBUG | [DouBaoImageToImage] 命令解析: ['@饿飞\u2005宋公子威武']
2025-08-05 23:38:59 | DEBUG | 处理消息内容: '@饿飞 宋公子威武'
2025-08-05 23:38:59 | DEBUG | 消息内容 '@饿飞 宋公子威武' 不匹配任何命令，忽略
2025-08-05 23:42:59 | DEBUG | 收到消息: {'MsgId': 1060585119, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="438849a397f5a1ed0288d8177a856857" len = "87404" productid="" androidmd5="438849a397f5a1ed0288d8177a856857" androidlen="87404" s60v3md5 = "438849a397f5a1ed0288d8177a856857" s60v3len="87404" s60v5md5 = "438849a397f5a1ed0288d8177a856857" s60v5len="87404" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=438849a397f5a1ed0288d8177a856857&amp;filekey=30350201010421301f02020106040253480410438849a397f5a1ed0288d8177a856857020301556c040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630cd542000826d5000000000000010600004f5053480e267b40b78d4ce32&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=27b710f75b7d0b53357e29e138211610&amp;filekey=30350201010421301f020201060402535a041027b710f75b7d0b53357e29e1382116100203015570040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2630cd542000de7e5000000000000010600004f50535a01c278809676a5b8d&amp;bizid=1023" aeskey= "82825e0be1ff1c3fa763f9e6f59bce1b" externurl = "" externmd5 = "" width= "640" height= "172" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754408591, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VXGWiMyt|v1_0ZQJim8i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 235738011393380447, 'MsgSeq': 871430304}
2025-08-05 23:42:59 | INFO | 收到表情消息: 消息ID:1060585119 来自:27852221909@chatroom 发送人:tianen532965049 MD5:438849a397f5a1ed0288d8177a856857 大小:87404
2025-08-05 23:42:59 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 235738011393380447
2025-08-05 23:46:46 | DEBUG | 收到消息: {'MsgId': 1529339947, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n雄起'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754408818, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Cm2Zl3Ga|v1_HgVijD8s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7003479542283834202, 'MsgSeq': 871430305}
2025-08-05 23:46:46 | INFO | 收到文本消息: 消息ID:1529339947 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:雄起
2025-08-05 23:46:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '雄起' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:46:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['雄起']
2025-08-05 23:46:46 | DEBUG | 处理消息内容: '雄起'
2025-08-05 23:46:46 | DEBUG | 消息内容 '雄起' 不匹配任何命令，忽略
2025-08-05 23:47:05 | DEBUG | 收到消息: {'MsgId': 497595947, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>734851319</msgid><newmsgid>7003479542283834202</newmsgid><replacemsg><![CDATA["XvemiZ¹⁴" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754408831, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6860271850740549474, 'MsgSeq': 871430306}
2025-08-05 23:47:05 | DEBUG | 系统消息类型: revokemsg
2025-08-05 23:47:05 | INFO | 未知的系统消息类型: {'MsgId': 497595947, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>734851319</msgid><newmsgid>7003479542283834202</newmsgid><replacemsg><![CDATA["XvemiZ¹⁴" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754408831, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6860271850740549474, 'MsgSeq': 871430306, 'FromWxid': '27852221909@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-05 23:47:08 | DEBUG | 收到消息: {'MsgId': 81561, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n霸气  威武'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754408840, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_nvguidSL|v1_HopFx7Jb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6180248951970566142, 'MsgSeq': 871430307}
2025-08-05 23:47:08 | INFO | 收到文本消息: 消息ID:81561 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:霸气  威武
2025-08-05 23:47:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '霸气  威武' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:47:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['霸气', '', '威武']
2025-08-05 23:47:08 | DEBUG | 处理消息内容: '霸气  威武'
2025-08-05 23:47:08 | DEBUG | 消息内容 '霸气  威武' 不匹配任何命令，忽略
2025-08-05 23:53:39 | DEBUG | 收到消息: {'MsgId': 1262716983, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音 拜拜'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754409231, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_zStY6WN7|v1_+zGsTFX6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音 拜拜', 'NewMsgId': 5857148206955795621, 'MsgSeq': 871430308}
2025-08-05 23:53:39 | INFO | 收到文本消息: 消息ID:1262716983 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音 拜拜
2025-08-05 23:53:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音 拜拜' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 23:53:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音', '拜拜']
2025-08-05 23:53:39 | DEBUG | 处理消息内容: '语音 拜拜'
2025-08-05 23:53:39 | DEBUG | 消息内容 '语音 拜拜' 不匹配任何命令，忽略
2025-08-05 23:53:40 | DEBUG | 收到消息: {'MsgId': 855816210, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xun900112:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>国产AI的一次“代际跃迁”：读懂L4智能体蜂群，你就读懂了未来十年</title>\n\t\t<des>加我进AI讨论学习群，公众号右下角“联系方式”关注发送“知识精华”，可获得免费的知识链接地址</des>\n\t\t<action />\n\t\t<type>5</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url>http://mp.weixin.qq.com/s?__biz=MzI0NzU2MDgyNA==&amp;mid=2247488435&amp;idx=1&amp;sn=0b74aab2479cb949c010973c6cbada5d&amp;chksm=e8c4eb59dad8604d1ee57a654af49687a3409539f6bf66253d99f883ddd8e6dda3ec6327bd6a&amp;scene=126&amp;sessionid=1754409141#rd</url>\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername>gh_0343fa98274f</sourceusername>\n\t\t<sourcedisplayname>老金带你玩AI</sourcedisplayname>\n\t\t<thumburl>https://mmbiz.qpic.cn/sz_mmbiz_jpg/4t4XAdD3valJchgE7h5tTZ0nOVy54TbVRp0jRrzicbRK8dx9JoSjicHycgLD8KUGibfRtLJandjF4IprFEUGJeZnQ/300?wxtype=jpeg&amp;wxfrom=401</thumburl>\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<mmreadershare>\n\t\t\t<itemshowtype>0</itemshowtype>\n\t\t\t<ispaysubscribe>false</ispaysubscribe>\n\t\t\t<pubtime>0</pubtime>\n\t\t\t<duration>0</duration>\n\t\t\t<width>0</width>\n\t\t\t<height>0</height>\n\t\t\t<vid>0</vid>\n\t\t\t<showsourceinfo>0</showsourceinfo>\n\t\t\t<coverpicimageurl />\n\t\t\t<piccount>0</piccount>\n\t\t\t<coverpicwidth>0</coverpicwidth>\n\t\t\t<coverpicheight>0</coverpicheight>\n\t\t</mmreadershare>\n\t\t<webviewshared />\n\t</appmsg>\n\t<fromusername>xun900112</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754409232, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>c5ed341965ff4380aa72823bd32d23e2_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_SpbBQF+X|v1_NzofOyIm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '金永勋 : [链接]国产AI的一次“代际跃迁”：读懂L4智能体蜂群，你就读懂了未...', 'NewMsgId': 9180865774267810007, 'MsgSeq': 871430309}
2025-08-05 23:53:40 | DEBUG | 从群聊消息中提取发送者: xun900112
2025-08-05 23:53:40 | INFO | 收到公众号文章消息: 消息ID:855816210 来自:47325400669@chatroom
2025-08-05 23:53:40 | ERROR | 解析XML失败: mismatched tag: line 1, column 248
2025-08-05 23:53:40 | DEBUG | [ArticleForwarder] 检测到XML开头被截断，尝试修复
2025-08-05 23:53:40 | DEBUG | [ArticleForwarder] 已修复XML开头截断问题
2025-08-05 23:53:40 | DEBUG | 尝试使用修复后的XML重新解析
2025-08-05 23:53:40 | DEBUG | 从sourcedisplayname提取到公众号: 老金带你玩AI
2025-08-05 23:53:40 | DEBUG | 公众号「老金带你玩AI」不在监控列表中，跳过处理
2025-08-05 23:53:41 | DEBUG | 收到消息: {'MsgId': 300105944, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n🎉正在为您生成总结，请稍候...'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754409233, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_7d6xzjBi|v1_JDK95/4e</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : \ue312正在为您生成总结，请稍候...', 'NewMsgId': 2896161973732546327, 'MsgSeq': 871430310}
2025-08-05 23:53:41 | INFO | 收到文本消息: 消息ID:300105944 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:🎉正在为您生成总结，请稍候...
2025-08-05 23:53:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '🎉正在为您生成总结，请稍候...' from wxid_fh84okl6f5wp22 in 47325400669@chatroom
2025-08-05 23:53:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['🎉正在为您生成总结，请稍候...']
2025-08-05 23:53:41 | DEBUG | 处理消息内容: '🎉正在为您生成总结，请稍候...'
2025-08-05 23:53:41 | DEBUG | 消息内容 '🎉正在为您生成总结，请稍候...' 不匹配任何命令，忽略
2025-08-05 23:53:44 | DEBUG | 收到消息: {'MsgId': 294757325, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_z3wc4zex3vr822:\n🎉正在为您生成总结，请稍候...'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754409233, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_pLLh4Kbp|v1_/Y0WV5tw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小艾上午 : \ue312正在为您生成总结，请稍候...', 'NewMsgId': 5769488073080447887, 'MsgSeq': 871430311}
2025-08-05 23:53:44 | INFO | 收到文本消息: 消息ID:294757325 来自:47325400669@chatroom 发送人:wxid_z3wc4zex3vr822 @:[] 内容:🎉正在为您生成总结，请稍候...
2025-08-05 23:53:44 | DEBUG | [DouBaoImageToImage] 收到文本消息: '🎉正在为您生成总结，请稍候...' from wxid_z3wc4zex3vr822 in 47325400669@chatroom
2025-08-05 23:53:44 | DEBUG | [DouBaoImageToImage] 命令解析: ['🎉正在为您生成总结，请稍候...']
2025-08-05 23:53:44 | DEBUG | 处理消息内容: '🎉正在为您生成总结，请稍候...'
2025-08-05 23:53:44 | DEBUG | 消息内容 '🎉正在为您生成总结，请稍候...' 不匹配任何命令，忽略
2025-08-05 23:53:46 | DEBUG | 收到消息: {'MsgId': 193715503, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n📖 国产AI的一次“代际跃迁”：读懂L4智能体蜂群，你就读懂了未来十年\n\n📝 简要总结  \n这篇文章介绍了L4多智能体蜂群的突破，标志着AI从“个体”向“组织”转变，未来的AI竞争将由“模型大小”转向“组织结构”，并开启了AI团队管理的新篇章。\n\n🔅 关键要点  \n1️⃣ L4智能体蜂群将多个AI智能体合作协作，提升任务完成效率。  \n2️⃣ 竞争焦点从“模型大小”转向“管理能力”和“组织结构”。  \n3️⃣ 普通人将不再只是“使用者”，而是能够管理AI团队的“领导者”。  \n4️⃣ 纳米AI实现了AI团队的动态协作，解决了复杂任务透明化与可优化的问题。  \n\n🔖标签: #AI技术 #多智能体 #未来十年 #管理能力 #L4智能体'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754409237, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_Xr8MHPV2|v1_d8D4a9ZR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : \ue148 国产AI的一次“代际跃迁”：读懂L4智能体蜂群，你就读懂了未来...', 'NewMsgId': 1369618185854243200, 'MsgSeq': 871430312}
2025-08-05 23:53:46 | INFO | 收到文本消息: 消息ID:193715503 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:📖 国产AI的一次“代际跃迁”：读懂L4智能体蜂群，你就读懂了未来十年

📝 简要总结  
这篇文章介绍了L4多智能体蜂群的突破，标志着AI从“个体”向“组织”转变，未来的AI竞争将由“模型大小”转向“组织结构”，并开启了AI团队管理的新篇章。

🔅 关键要点  
1️⃣ L4智能体蜂群将多个AI智能体合作协作，提升任务完成效率。  
2️⃣ 竞争焦点从“模型大小”转向“管理能力”和“组织结构”。  
3️⃣ 普通人将不再只是“使用者”，而是能够管理AI团队的“领导者”。  
4️⃣ 纳米AI实现了AI团队的动态协作，解决了复杂任务透明化与可优化的问题。  

🔖标签: #AI技术 #多智能体 #未来十年 #管理能力 #L4智能体
2025-08-05 23:53:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '📖 国产AI的一次“代际跃迁”：读懂L4智能体蜂群，你就读懂了未来十年

📝 简要总结  
这篇文章介绍了L4多智能体蜂群的突破，标志着AI从“个体”向“组织”转变，未来的AI竞争将由“模型大小”转向“组织结构”，并开启了AI团队管理的新篇章。

🔅 关键要点  
1️⃣ L4智能体蜂群将多个AI智能体合作协作，提升任务完成效率。  
2️⃣ 竞争焦点从“模型大小”转向“管理能力”和“组织结构”。  
3️⃣ 普通人将不再只是“使用者”，而是能够管理AI团队的“领导者”。  
4️⃣ 纳米AI实现了AI团队的动态协作，解决了复杂任务透明化与可优化的问题。  

🔖标签: #AI技术 #多智能体 #未来十年 #管理能力 #L4智能体' from wxid_fh84okl6f5wp22 in 47325400669@chatroom
2025-08-05 23:53:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['📖', '国产AI的一次“代际跃迁”：读懂L4智能体蜂群，你就读懂了未来十年\n\n📝', '简要总结  \n这篇文章介绍了L4多智能体蜂群的突破，标志着AI从“个体”向“组织”转变，未来的AI竞争将由“模型大小”转向“组织结构”，并开启了AI团队管理的新篇章。\n\n🔅 关键要点  \n1️⃣ L4智能体蜂群将多个AI智能体合作协作，提升任务完成效率。  \n2️⃣ 竞争焦点从“模型大小”转向“管理能力”和“组织结构”。  \n3️⃣ 普通人将不再只是“使用者”，而是能够管理AI团队的“领导者”。  \n4️⃣ 纳米AI实现了AI团队的动态协作，解决了复杂任务透明化与可优化的问题。  \n\n🔖标签: #AI技术 #多智能体 #未来十年 #管理能力 #L4智能体']
2025-08-05 23:53:46 | DEBUG | 处理消息内容: '📖 国产AI的一次“代际跃迁”：读懂L4智能体蜂群，你就读懂了未来十年

📝 简要总结  
这篇文章介绍了L4多智能体蜂群的突破，标志着AI从“个体”向“组织”转变，未来的AI竞争将由“模型大小”转向“组织结构”，并开启了AI团队管理的新篇章。

🔅 关键要点  
1️⃣ L4智能体蜂群将多个AI智能体合作协作，提升任务完成效率。  
2️⃣ 竞争焦点从“模型大小”转向“管理能力”和“组织结构”。  
3️⃣ 普通人将不再只是“使用者”，而是能够管理AI团队的“领导者”。  
4️⃣ 纳米AI实现了AI团队的动态协作，解决了复杂任务透明化与可优化的问题。  

🔖标签: #AI技术 #多智能体 #未来十年 #管理能力 #L4智能体'
2025-08-05 23:53:46 | DEBUG | 消息内容 '📖 国产AI的一次“代际跃迁”：读懂L4智能体蜂群，你就读懂了未来十年

📝 简要总结  
这篇文章介绍了L4多智能体蜂群的突破，标志着AI从“个体”向“组织”转变，未来的AI竞争将由“模型大小”转向“组织结构”，并开启了AI团队管理的新篇章。

🔅 关键要点  
1️⃣ L4智能体蜂群将多个AI智能体合作协作，提升任务完成效率。  
2️⃣ 竞争焦点从“模型大小”转向“管理能力”和“组织结构”。  
3️⃣ 普通人将不再只是“使用者”，而是能够管理AI团队的“领导者”。  
4️⃣ 纳米AI实现了AI团队的动态协作，解决了复杂任务透明化与可优化的问题。  

🔖标签: #AI技术 #多智能体 #未来十年 #管理能力 #L4智能体' 不匹配任何命令，忽略
2025-08-05 23:53:56 | DEBUG | 收到消息: {'MsgId': 1862902286, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_z3wc4zex3vr822:\n📖 一句话总结  \n国产AI正经历从“个体智能”到“组织智能”的代际跃迁，特别是L4多智能体蜂群的出现标志着协作能力的重大突破。\n\n🔑 关键要点  \n1. L4多智能体蜂群通过团队协作，将多个AI智能体在统一框架下进行协调，展示出比以往更强大的工作效率和协同能力。  \n2. AI竞争的焦点从“模型大小”转向“组织结构”，未来的成功取决于管理能力和智能体之间的配合。  \n3. 普通用户将从“使用者”转变为“领导者”，拥有管理AI团队和设定任务的能力，大幅提升个人工作效率。  \n4. 纳米AI的“蜂群协作框架”突破传统调度方式，使复杂任务执行过程更加透明、可控和可优化。  \n5. 该技术的商业化应用提醒我们要重视AI行业的潜力，不再将其视为简单的“玩具”。\n\n🏷 标签: #国产AI #多智能体 #组织智能 #团队协作 #技术创新'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754409247, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_Jru9Pb8u|v1_yaiaGqpb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小艾上午 : \ue148 一句话总结  \n国产AI正经历从“个体智能”到“组织智能”的代...', 'NewMsgId': 8187845315188700989, 'MsgSeq': 871430313}
2025-08-05 23:53:56 | INFO | 收到文本消息: 消息ID:1862902286 来自:47325400669@chatroom 发送人:wxid_z3wc4zex3vr822 @:[] 内容:📖 一句话总结  
国产AI正经历从“个体智能”到“组织智能”的代际跃迁，特别是L4多智能体蜂群的出现标志着协作能力的重大突破。

🔑 关键要点  
1. L4多智能体蜂群通过团队协作，将多个AI智能体在统一框架下进行协调，展示出比以往更强大的工作效率和协同能力。  
2. AI竞争的焦点从“模型大小”转向“组织结构”，未来的成功取决于管理能力和智能体之间的配合。  
3. 普通用户将从“使用者”转变为“领导者”，拥有管理AI团队和设定任务的能力，大幅提升个人工作效率。  
4. 纳米AI的“蜂群协作框架”突破传统调度方式，使复杂任务执行过程更加透明、可控和可优化。  
5. 该技术的商业化应用提醒我们要重视AI行业的潜力，不再将其视为简单的“玩具”。

🏷 标签: #国产AI #多智能体 #组织智能 #团队协作 #技术创新
2025-08-05 23:53:56 | DEBUG | [DouBaoImageToImage] 收到文本消息: '📖 一句话总结  
国产AI正经历从“个体智能”到“组织智能”的代际跃迁，特别是L4多智能体蜂群的出现标志着协作能力的重大突破。

🔑 关键要点  
1. L4多智能体蜂群通过团队协作，将多个AI智能体在统一框架下进行协调，展示出比以往更强大的工作效率和协同能力。  
2. AI竞争的焦点从“模型大小”转向“组织结构”，未来的成功取决于管理能力和智能体之间的配合。  
3. 普通用户将从“使用者”转变为“领导者”，拥有管理AI团队和设定任务的能力，大幅提升个人工作效率。  
4. 纳米AI的“蜂群协作框架”突破传统调度方式，使复杂任务执行过程更加透明、可控和可优化。  
5. 该技术的商业化应用提醒我们要重视AI行业的潜力，不再将其视为简单的“玩具”。

🏷 标签: #国产AI #多智能体 #组织智能 #团队协作 #技术创新' from wxid_z3wc4zex3vr822 in 47325400669@chatroom
2025-08-05 23:53:56 | DEBUG | [DouBaoImageToImage] 命令解析: ['📖', '一句话总结', ' \n国产AI正经历从“个体智能”到“组织智能”的代际跃迁，特别是L4多智能体蜂群的出现标志着协作能力的重大突破。\n\n🔑 关键要点  \n1. L4多智能体蜂群通过团队协作，将多个AI智能体在统一框架下进行协调，展示出比以往更强大的工作效率和协同能力。  \n2. AI竞争的焦点从“模型大小”转向“组织结构”，未来的成功取决于管理能力和智能体之间的配合。  \n3. 普通用户将从“使用者”转变为“领导者”，拥有管理AI团队和设定任务的能力，大幅提升个人工作效率。  \n4. 纳米AI的“蜂群协作框架”突破传统调度方式，使复杂任务执行过程更加透明、可控和可优化。  \n5. 该技术的商业化应用提醒我们要重视AI行业的潜力，不再将其视为简单的“玩具”。\n\n🏷 标签: #国产AI #多智能体 #组织智能 #团队协作 #技术创新']
2025-08-05 23:53:56 | DEBUG | 处理消息内容: '📖 一句话总结  
国产AI正经历从“个体智能”到“组织智能”的代际跃迁，特别是L4多智能体蜂群的出现标志着协作能力的重大突破。

🔑 关键要点  
1. L4多智能体蜂群通过团队协作，将多个AI智能体在统一框架下进行协调，展示出比以往更强大的工作效率和协同能力。  
2. AI竞争的焦点从“模型大小”转向“组织结构”，未来的成功取决于管理能力和智能体之间的配合。  
3. 普通用户将从“使用者”转变为“领导者”，拥有管理AI团队和设定任务的能力，大幅提升个人工作效率。  
4. 纳米AI的“蜂群协作框架”突破传统调度方式，使复杂任务执行过程更加透明、可控和可优化。  
5. 该技术的商业化应用提醒我们要重视AI行业的潜力，不再将其视为简单的“玩具”。

🏷 标签: #国产AI #多智能体 #组织智能 #团队协作 #技术创新'
2025-08-05 23:53:56 | DEBUG | 消息内容 '📖 一句话总结  
国产AI正经历从“个体智能”到“组织智能”的代际跃迁，特别是L4多智能体蜂群的出现标志着协作能力的重大突破。

🔑 关键要点  
1. L4多智能体蜂群通过团队协作，将多个AI智能体在统一框架下进行协调，展示出比以往更强大的工作效率和协同能力。  
2. AI竞争的焦点从“模型大小”转向“组织结构”，未来的成功取决于管理能力和智能体之间的配合。  
3. 普通用户将从“使用者”转变为“领导者”，拥有管理AI团队和设定任务的能力，大幅提升个人工作效率。  
4. 纳米AI的“蜂群协作框架”突破传统调度方式，使复杂任务执行过程更加透明、可控和可优化。  
5. 该技术的商业化应用提醒我们要重视AI行业的潜力，不再将其视为简单的“玩具”。

🏷 标签: #国产AI #多智能体 #组织智能 #团队协作 #技术创新' 不匹配任何命令，忽略
2025-08-05 23:55:44 | INFO | [TempFileManager] 开始清理临时文件...
