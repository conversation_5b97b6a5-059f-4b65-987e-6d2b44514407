import os, json, asyncio, time, hashlib, uuid, hmac, random, base64, xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from pathlib import Path
try: import tomllib
except: import tomli as tomllib
import httpx
from loguru import logger
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_quote_message
from utils.plugin_base import PluginBase


class RoboNeo(PluginBase):
    description, author, version, plugin_name = "美图RoboNeo AI图像处理功能，支持换装、风格转换等", "XYBot", "1.0.0", "RoboNeo"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("temp/roboneo")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request, self.user_request_count, self.user_sessions = {}, {}, {}
        self.quote_command = ["美图"]

        # 用于管理后台任务
        self.background_tasks = set()
        # AI询问状态管理
        self._ai_asking = {}  # 记录AI是否在询问用户
        self._ai_questions = {}  # 记录AI的问题
        self._pending_requests = {}  # 记录待处理的请求信息

    def _load_config(self):
        try:
            config = tomllib.load(open(f"plugins/{self.plugin_name}/config.toml", "rb")).get(self.plugin_name, {}) if os.path.exists(f"plugins/{self.plugin_name}/config.toml") else {}
        except:
            config = {}
        
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["美图"])
        self.command_format = config.get("command-format", "美图 [提示词] [图片路径]")
        
        quote_config = config.get("quote", {})
        self.quote_command = quote_config.get("command", ["美图"])
        self.quote_command_format = quote_config.get("command-format", "引用图片并发送: 美图+提示词")
        
        api_config = config.get("api", {})
        self.policy_url = api_config.get("policy_url", "https://strategy.app.meitudata.com/upload/policy")
        self.generation_url = api_config.get("generation_url", "https://ai-engine-gateway-roboneo.meitu.com/roboneo/sync/request/stream")
        self.history_url = api_config.get("history_url", "https://ai-engine-gateway-roboneo.meitu.com/roboneo/sync/request")
        self.access_token = api_config.get("access_token", "")
        self.client_id = api_config.get("client_id", "1189857651")
        self.uid = api_config.get("uid", "1697403212")
        self.gnum = api_config.get("gnum", "2886031057")
        
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 60)
        self.default_prompt = config.get("default_prompt", "换成泳衣")
        self.max_wait_time = config.get("max_wait_time", 180)

    async def cleanup(self):
        """清理后台任务和AI询问状态"""
        if hasattr(self, 'background_tasks'):
            # 等待所有后台任务完成或取消它们
            for task in list(self.background_tasks):
                if not task.done():
                    task.cancel()
            # 等待所有任务完成
            if self.background_tasks:
                await asyncio.gather(*self.background_tasks, return_exceptions=True)
            self.background_tasks.clear()
            logger.info(f"[{self.plugin_name}] 已清理所有后台任务")

        # 清理AI询问状态
        if hasattr(self, '_ai_asking'):
            self._ai_asking.clear()
        if hasattr(self, '_ai_questions'):
            self._ai_questions.clear()
        if hasattr(self, '_pending_requests'):
            self._pending_requests.clear()
        logger.info(f"[{self.plugin_name}] 已清理AI询问状态")





    @on_text_message
    async def handle_text(self, bot, message):
        if not self.enable:
            return

        content, wxid, user_wxid = str(message["Content"]).strip(), message["FromWxid"], message["SenderWxid"]

        # 检查是否是AI询问状态下的回答
        if self._ai_asking.get(wxid, False) and self._ai_asking[wxid] == user_wxid:
            await self._handle_ai_question_response(bot, wxid, user_wxid, content)
            return

        if content in ["美图帮助", "美图说明", "美图指令"]:
            await self._send_usage_instructions(bot, wxid, user_wxid)
            return

        command_parts = content.split(" ", 2)
        if command_parts[0] in self.command:
            if len(command_parts) < 3:
                await bot.send_at_message(wxid, f"❌ 命令格式错误，正确格式: {self.command_format}", [user_wxid])
                await self._send_usage_instructions(bot, wxid, user_wxid)
                return

            if self._check_rate_limit(user_wxid):
                await bot.send_at_message(wxid, "⏳ 请求太频繁，请稍后再试", [user_wxid])
                return

            prompt, image_path = command_parts[1].strip(), command_parts[2].strip()
            if not prompt:
                prompt = self.default_prompt

            if not os.path.exists(image_path):
                await bot.send_at_message(wxid, f"❌ 图片不存在: {image_path}", [user_wxid])
                await self._send_usage_instructions(bot, wxid, user_wxid)
                return

            # 创建异步后台任务，避免阻塞消息处理
            async def safe_process():
                try:
                    await self._process_image_generation(bot, wxid, user_wxid, prompt, image_path)
                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 后台任务异常: {str(e)}")
                    await bot.send_at_message(wxid, f"❌ 处理失败: {str(e)}", [user_wxid])

            task = asyncio.create_task(safe_process())
            self.background_tasks.add(task)
            task.add_done_callback(self.background_tasks.discard)

    @on_quote_message
    async def handle_quote(self, bot, message):
        if not self.enable:
            return
        
        content, wxid, user_wxid = str(message.get("Content", "")).strip(), message.get("FromWxid", ""), message.get("SenderWxid", "")
        command_parts = content.split(maxsplit=1)
        
        if not command_parts or command_parts[0] not in self.quote_command:
            return
        
        if self._check_rate_limit(user_wxid):
            await bot.send_at_message(wxid, "请求太频繁，等会再试试", [user_wxid])
            return
        
        # 检查是否是AI询问状态下的回答
        if self._ai_asking.get(wxid, False) and self._ai_asking[wxid] == user_wxid:
            await self._handle_ai_question_response(bot, wxid, user_wxid, content)
            return

        # 解析提示词
        prompt = self.default_prompt
        if len(command_parts) > 1:
            full_command = command_parts[1].strip()
            prompt = full_command[1:].strip() if full_command.startswith("+") else full_command

        if not prompt:
            prompt = self.default_prompt
        
        quote_info = message.get("Quote", {})
        quoted_msg_id = quote_info.get("Msgid") or quote_info.get("NewMsgId")
        quoted_sender = quote_info.get("SenderWxid") or quote_info.get("FromWxid")
        bot_wxid = getattr(bot, 'wxid', None)
        
        if quote_info.get("MsgType") != 3:
            await bot.send_at_message(wxid, "❌ 请引用图片消息", [user_wxid])
            return
        
        await self._simple_confirm(bot, wxid)
        
        # 处理机器人发送的图片
        if quoted_sender == bot_wxid:
            try:
                from plugins.RevokePlugin.main import RevokePlugin
                if RevokePlugin._instance:
                    msg_info = RevokePlugin._instance.get_message_by_id(quoted_msg_id)
                    if msg_info and 'local_image_path' in msg_info and os.path.exists(msg_info['local_image_path']):
                        await self._process_image_generation_without_notification(bot, wxid, user_wxid, prompt, msg_info['local_image_path'])
                        return
            except:
                pass
            await bot.send_at_message(wxid, "❌ 暂不支持处理机器人发送的图片\n请引用其他用户发送的图片", [user_wxid])
            return
        
        try:
            quote_content = quote_info.get("Content", "")
            if not quote_content:
                await bot.send_at_message(wxid, "❌ 无法获取引用的图片内容", [user_wxid])
                return
            
            # 解析XML获取图片信息
            try:
                root = ET.fromstring(quote_content)
                img_node = root.find('.//img')
                if img_node is None:
                    refermsg = root.find('.//refermsg')
                    if refermsg is not None and refermsg.find('content') is not None:
                        content_text = refermsg.find('content').text
                        if content_text:
                            content_text = content_text.replace('&lt;', '<').replace('&gt;', '>')
                            try:
                                img_node = ET.fromstring(content_text).find('img')
                            except:
                                pass
                
                if img_node is None:
                    if "aeskey=" in quote_content and "cdnmidimgurl=" in quote_content:
                        aeskey = quote_content[quote_content.find('aeskey="')+8:quote_content.find('"', quote_content.find('aeskey="')+8)]
                        cdnmidimgurl = quote_content[quote_content.find('cdnmidimgurl="')+14:quote_content.find('"', quote_content.find('cdnmidimgurl="')+14)]
                    elif "cdnthumbaeskey=" in quote_content and "cdnthumburl=" in quote_content:
                        aeskey = quote_content[quote_content.find('cdnthumbaeskey="')+16:quote_content.find('"', quote_content.find('cdnthumbaeskey="')+16)]
                        cdnmidimgurl = quote_content[quote_content.find('cdnthumburl="')+13:quote_content.find('"', quote_content.find('cdnthumburl="')+13)]
                    else:
                        await bot.send_at_message(wxid, "❌ 无法从引用消息中提取图片信息", [user_wxid])
                        return
                else:
                    aeskey, cdnmidimgurl = img_node.get('aeskey'), img_node.get('cdnmidimgurl')
                    if not aeskey or not cdnmidimgurl:
                        aeskey, cdnmidimgurl = img_node.get('cdnthumbaeskey'), img_node.get('cdnthumburl')
                
                if not aeskey or not cdnmidimgurl:
                    await bot.send_at_message(wxid, "❌ 无法提取图片下载参数", [user_wxid])
                    return
                
                image_base64 = await bot.download_image(aeskey, cdnmidimgurl)
                if not image_base64:
                    await bot.send_at_message(wxid, "❌ 下载图片失败", [user_wxid])
                    return
                
                temp_file = self.temp_dir / f"quoted_image_{int(time.time())}.jpg"
                with open(temp_file, "wb") as f:
                    f.write(base64.b64decode(image_base64))
                
                # 创建异步后台任务，避免阻塞消息处理
                async def safe_process():
                    try:
                        await self._process_image_generation_without_notification(bot, wxid, user_wxid, prompt, str(temp_file))
                    except Exception as e:
                        logger.error(f"[{self.plugin_name}] 后台任务异常: {str(e)}")
                        await bot.send_at_message(wxid, f"❌ 处理失败: {str(e)}", [user_wxid])

                task = asyncio.create_task(safe_process())
                self.background_tasks.add(task)
                task.add_done_callback(self.background_tasks.discard)
                
            except:
                await bot.send_at_message(wxid, "❌ 解析图片信息失败", [user_wxid])
        except Exception as e:
            await bot.send_at_message(wxid, f"❌ 处理过程中出现错误: {str(e)}", [user_wxid])

    def _check_rate_limit(self, user_wxid):
        current_time = time.time()
        if user_wxid not in self.user_last_request or (current_time - self.user_last_request.get(user_wxid, 0)) > self.cooldown:
            self.user_last_request[user_wxid], self.user_request_count[user_wxid] = current_time, 1
            return False
        self.user_last_request[user_wxid] = current_time
        self.user_request_count[user_wxid] = self.user_request_count.get(user_wxid, 0) + 1
        return True

    async def _simple_confirm(self, bot, wxid, custom_message=None):
        """简单确认回复"""
        if custom_message:
            await bot.send_text_message(wxid, custom_message)
        else:
            await bot.send_text_message(wxid, "正在处理中，请稍候...")

    async def _handle_ai_question_response(self, bot, wxid, user_wxid, response):
        """处理AI询问的用户回答"""
        try:
            # 获取待处理的请求信息
            pending_request = self._pending_requests.get(wxid)
            if not pending_request:
                await bot.send_at_message(wxid, "❌ 未找到待处理的请求", [user_wxid])
                self._ai_asking.pop(wxid, None)
                self._ai_questions.pop(wxid, None)
                return

            # 清除询问状态
            self._ai_asking.pop(wxid, None)
            self._ai_questions.pop(wxid, None)

            # 将用户回答作为补充提示词，重新处理请求
            original_prompt = pending_request['prompt']
            enhanced_prompt = f"{original_prompt} {response}".strip()
            image_path = pending_request['image_path']

            logger.info(f"[{self.plugin_name}] AI询问回答处理: 原提示词='{original_prompt}', 用户回答='{response}', 增强提示词='{enhanced_prompt}'")

            # 重新处理图像生成
            await self._process_image_generation_without_notification(bot, wxid, user_wxid, enhanced_prompt, image_path)

            # 清除待处理请求
            self._pending_requests.pop(wxid, None)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理AI询问回答异常: {str(e)}")
            await bot.send_at_message(wxid, f"❌ 处理回答失败: {str(e)}", [user_wxid])
            # 清理状态
            self._ai_asking.pop(wxid, None)
            self._ai_questions.pop(wxid, None)
            self._pending_requests.pop(wxid, None)

    async def _send_usage_instructions(self, bot, wxid, user_wxid):
        await bot.send_at_message(wxid, f"🎨 美图RoboNeo插件使用说明\n📝 命令格式:\n• {self.command_format}\n• {self.quote_command_format}\n💡 示例: 美图换成泳衣\n⏱️ 处理时间: 约30-180秒\n🤖 AI可能会询问更多细节，请直接回答", [user_wxid])

    async def _process_image_generation(self, bot, wxid, user_wxid, prompt, image_path):
        """处理图像生成（带确认消息）"""
        try:
            # 立即发送确认消息
            await self._simple_confirm(bot, wxid)
            # 然后在后台处理图像生成
            await self._process_image_generation_without_notification(bot, wxid, user_wxid, prompt, image_path)
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 图像生成失败: {str(e)}")
            await bot.send_at_message(wxid, f"❌ 处理失败: {str(e)}", [user_wxid])

    async def _process_image_generation_without_notification(self, bot, wxid, user_wxid, prompt, image_path):
        """处理图像生成（无确认消息）"""
        try:
            logger.info(f"[{self.plugin_name}] 开始处理图像生成: 用户={user_wxid}, 提示词={prompt}, 图片={image_path}")

            # 步骤1: 上传图片
            image_url = await self._upload_image(image_path)
            if not image_url:
                await bot.send_at_message(wxid, "❌ 图片上传失败", [user_wxid])
                return

            logger.info(f"[{self.plugin_name}] 图片上传成功: {image_url}")

            # 步骤2: 提交生成任务
            result = await self._submit_generation_task(image_url, prompt)

            if isinstance(result, str):
                # 直接获得了生成图片URL（旧版本兼容）
                await self._send_result_image(bot, wxid, user_wxid, result, prompt)
            elif isinstance(result, dict):
                if result.get('ai_question'):
                    # AI询问更多细节
                    question = result['ai_question']
                    ai_response = result.get('text_response', {})

                    # 设置询问状态
                    self._ai_asking[wxid] = user_wxid
                    self._ai_questions[wxid] = question
                    self._pending_requests[wxid] = {
                        'prompt': prompt,
                        'image_path': image_path
                    }

                    # 先发送AI回复内容（如果有）
                    if ai_response:
                        ai_text_parts = []
                        for _, text in ai_response.items():
                            if text.strip():
                                ai_text_parts.append(text.strip())
                        if ai_text_parts:
                            ai_message = " ".join(ai_text_parts)
                            await bot.send_text_message(wxid, ai_message)

                    # 发送AI询问
                    await bot.send_at_message(wxid, f"🤖 AI询问: {question}\n💡 请直接回答，我会根据您的回答继续处理", [user_wxid])
                    return

                elif result.get('direct_url'):
                    # 直接获得了生成图片URL和AI回复内容
                    direct_url = result['direct_url']
                    ai_response = result.get('text_response', {})

                    # 先发送AI回复内容
                    if ai_response:
                        # 合并所有AI回复文本
                        ai_text_parts = []
                        for _, text in ai_response.items():
                            if text.strip():
                                ai_text_parts.append(text.strip())

                        if ai_text_parts:
                            ai_message = " ".join(ai_text_parts)
                            await bot.send_text_message(wxid, ai_message)

                    # 然后发送生成的图片
                    await self._send_result_image(bot, wxid, user_wxid, direct_url, prompt)

                elif result.get('task_id'):
                    # 获得了task_id，需要等待结果
                    logger.info(f"[{self.plugin_name}] 任务已提交: {result['task_id']}")

                    # 发送AI回复内容作为确认消息
                    ai_response = result.get('text_response', {})
                    if ai_response:
                        # 合并所有AI回复文本
                        ai_text_parts = []
                        for _, text in ai_response.items():
                            if text.strip():
                                ai_text_parts.append(text.strip())

                        if ai_text_parts:
                            ai_message = " ".join(ai_text_parts)
                            await bot.send_text_message(wxid, ai_message)
                        else:
                            # 如果没有AI回复内容，使用默认处理消息
                            await bot.send_text_message(wxid, "正在处理中，请稍候...")
                    else:
                        # 如果没有AI回复内容，使用默认处理消息
                        await bot.send_text_message(wxid, "正在处理中，请稍候...")

                    result_url = await self._wait_for_result(result, self.max_wait_time)

                    if result_url:
                        await self._send_result_image(bot, wxid, user_wxid, result_url, prompt)
                    else:
                        await bot.send_at_message(wxid, "❌ 生成超时或失败，请稍后重试", [user_wxid])
                else:
                    await bot.send_at_message(wxid, "❌ 生成任务提交失败", [user_wxid])
            else:
                await bot.send_at_message(wxid, "❌ 生成任务提交失败", [user_wxid])

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 图像生成异常: {str(e)}")
            await bot.send_at_message(wxid, f"❌ 处理失败: {str(e)}", [user_wxid])

    async def _send_result_image(self, bot, wxid, user_wxid, image_url, _prompt):
        """发送生成的图片结果"""
        try:
            async with httpx.AsyncClient(timeout=30) as client:
                response = await client.get(image_url)
                response.raise_for_status()
                image_data = response.content

                if len(image_data) < 1024:
                    raise ValueError(f"下载的图片数据过小: {len(image_data)}字节")

                result = await bot.send_image_message(wxid, image_data)
                if result and result[2] != 0:
                    logger.info(f"[{self.plugin_name}] 图片发送成功")
                else:
                    # 尝试发送文件
                    temp_file = self.temp_dir / f"result_{int(time.time())}.jpg"
                    with open(temp_file, "wb") as f:
                        f.write(image_data)
                    file_result = await bot.send_file_message(wxid, str(temp_file))
                    if file_result and file_result[2] != 0:
                        logger.info(f"[{self.plugin_name}] 文件发送成功")
                    else:
                        await bot.send_at_message(wxid, "❌ 图片发送失败", [user_wxid])

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送结果图片失败: {str(e)}")
            await bot.send_at_message(wxid, f"❌ 发送结果失败: {str(e)}", [user_wxid])

    async def _upload_image(self, file_path):
        """上传图片到服务器"""
        try:
            # 获取上传策略
            policy_data = await self._get_upload_policy()
            if not policy_data:
                return None

            # 使用第一个策略上传
            hw_s3_config = policy_data[0]['hw-s3']
            credentials = hw_s3_config['credentials']

            access_key = credentials['access_key']
            secret_key = credentials['secret_key']
            session_token = credentials['session_token']
            bucket = hw_s3_config['bucket']
            key = hw_s3_config['key']
            upload_url = hw_s3_config['url']

            # 创建上传表单
            current_date = time.strftime('%Y%m%dT%H%M%SZ', time.gmtime())
            # 使用UTC时间，兼容所有Python版本
            expiration_time = datetime.utcfromtimestamp(time.time()) + timedelta(hours=1)
            expiration = expiration_time.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'

            policy = {
                "expiration": expiration,
                "conditions": [
                    {"bucket": bucket},
                    ["starts-with", "$key", "roboneo/"],
                    ["starts-with", "$Content-Type", "image/"],
                    {"success_action_status": "200"},
                    {"X-Amz-Credential": f"{access_key}/{current_date[:8]}/cn-north-4/s3/aws4_request"},
                    {"X-Amz-Algorithm": "AWS4-HMAC-SHA256"},
                    {"X-Amz-Security-Token": session_token},
                    {"X-Amz-Date": current_date}
                ]
            }

            policy_json = json.dumps(policy, separators=(',', ':'))
            policy_b64 = base64.b64encode(policy_json.encode()).decode()
            signature = self._calculate_aws4_signature(secret_key, policy_b64, current_date)

            # 上传文件
            form_data = {
                'key': key,
                'Content-Type': 'image/jpeg',
                'success_action_status': '200',
                'X-Amz-Credential': f"{access_key}/{current_date[:8]}/cn-north-4/s3/aws4_request",
                'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
                'X-Amz-Security-Token': session_token,
                'X-Amz-Date': current_date,
                'Policy': policy_b64,
                'X-Amz-Signature': signature
            }

            body, content_length = self._create_multipart_data(form_data, file_path)

            upload_headers = {
                'Content-Type': f'multipart/form-data; boundary=----WebKitFormBoundaryAppUpload123456',
                'Content-Length': str(content_length),
                'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 10; V2002A Build/QP1A.190711.020)',
                'Accept-Encoding': 'gzip',
                'Connection': 'Keep-Alive'
            }

            async with httpx.AsyncClient(timeout=60) as client:
                response = await client.post(f"{upload_url}/{bucket}", content=body, headers=upload_headers)

                if response.status_code in [200, 204]:
                    return hw_s3_config.get('access_url', '')
                else:
                    return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 上传异常: {e}")
            return None

    async def _submit_generation_task(self, image_url, prompt):
        """提交生成任务"""
        try:
            room_id = str(uuid.uuid4())
            trace_id = str(uuid.uuid4())
            trace_id_high = self.gnum
            trace_id_low = str(int(time.time() * 1000000))

            request_data = {
                "parameter": {
                    "path_scene": "roboneo",
                    "body": "",
                    "features": "",
                    "image_urls": [image_url],
                    "video_urls": [],
                    "later_face": 0,
                    "message": prompt,
                    "room_id": room_id,
                    "id": "",
                    "content": "",
                    "type": "",
                    "task_id": "",
                    "req_mode": "",
                    "time_zone": "Asia/Shanghai",
                    "platform": "android",
                    "trace_id": trace_id,
                    "client_id": self.client_id,
                    "client_os": "10",
                    "client_channel_id": "And_NeoHomeMobile",
                    "lang": "zh-Hans",
                    "area_code": "CN",
                    "version": "1.3.0",
                    "uid": self.uid,
                    "gid": self.gnum,
                    "theme": 2,
                    "app_scene": "roboneo",
                    "token": "7E14E5FF92D54108A074A34D2083C93F"
                }
            }

            headers = {
                'Access-Token': self.access_token,
                'Content-Type': 'application/json; charset=utf-8',
                'Host': 'ai-engine-gateway-roboneo.meitu.com',
                'traceIdHigh': trace_id_high,
                'traceIdLow': trace_id_low
            }

            async with httpx.AsyncClient(timeout=120) as client:
                async with client.stream('POST', self.generation_url, json=request_data, headers=headers) as response:
                    if response.status_code == 200:
                        task_id = None
                        generated_url = None
                        text_blocks = {}  # 存储不同block的文字内容

                        async for line in response.aiter_lines():
                            if line and line.startswith('data: '):
                                try:
                                    data = json.loads(line[6:])
                                    message_type = data.get('type', '')

                                    if message_type == 'resp':
                                        task_id = data.get('task_id')
                                        logger.info(f"[{self.plugin_name}] 获得Task ID: {task_id}")

                                    elif message_type == 'request':
                                        # AI询问更多细节
                                        question = data.get('question', '')
                                        if question:
                                            logger.info(f"[{self.plugin_name}] AI询问: {question}")
                                            return {'ai_question': question, 'text_response': text_blocks}

                                    elif message_type == 'text':
                                        # 提取文字回复内容
                                        block_id = data.get('id', '')
                                        content = data.get('content', '')

                                        if block_id:
                                            if block_id not in text_blocks:
                                                text_blocks[block_id] = ""
                                            text_blocks[block_id] += content

                                    elif message_type == 'media':
                                        # 找到生成的图片！
                                        media_item = data.get('media_item', {})
                                        if media_item:
                                            generated_url = media_item.get('media_url', '')
                                            if generated_url:
                                                logger.info(f"[{self.plugin_name}] 找到生成图片: {generated_url}")

                                                # 输出完整的文字回复
                                                if text_blocks:
                                                    logger.info(f"[{self.plugin_name}] AI回复内容:")
                                                    for block_id, text in text_blocks.items():
                                                        if text.strip():  # 只输出非空内容
                                                            logger.info(f"  {text.strip()}")

                                                # 返回URL和文本回复内容
                                                return {'direct_url': generated_url, 'text_response': text_blocks}

                                    elif message_type == 'render_percent':
                                        percent = data.get('percent', [0])
                                        if percent and len(percent) > 0:
                                            progress = percent[0]
                                            logger.info(f"[{self.plugin_name}] 生成进度: {progress}%")
                                except:
                                    continue

                        if task_id:
                            # 如果有文字内容但没有直接获得图片，也输出文字回复
                            if text_blocks:
                                logger.info(f"[{self.plugin_name}] AI回复内容:")
                                for block_id, text in text_blocks.items():
                                    if text.strip():
                                        logger.info(f"  {text.strip()}")

                            return {'task_id': task_id, 'room_id': room_id, 'text_response': text_blocks}

            return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 提交任务异常: {e}")
            return None

    async def _wait_for_result(self, task_info, max_wait_time=180):
        """等待生成结果"""
        start_time = time.time()
        room_id = task_info.get('room_id')
        check_interval = 5  # 初始检查间隔5秒
        max_interval = 15   # 最大检查间隔15秒

        while time.time() - start_time < max_wait_time:
            elapsed = int(time.time() - start_time)

            # 动态调整检查间隔，开始频繁检查，后面逐渐减少
            if elapsed < 30:
                check_interval = 5
            elif elapsed < 60:
                check_interval = 8
            else:
                check_interval = min(max_interval, check_interval + 1)

            if elapsed % 30 == 0 and elapsed > 0:  # 每30秒记录一次
                logger.info(f"[{self.plugin_name}] 等待中... ({elapsed}s/{max_wait_time}s)")

            await asyncio.sleep(check_interval)

            try:
                # 获取历史记录
                history_data = await self._get_history_list()
                if history_data:
                    # 查找我们的任务结果
                    result_url = self._find_task_result(history_data, room_id)
                    if result_url:
                        logger.info(f"[{self.plugin_name}] 任务完成，总耗时: {elapsed}秒")
                        return result_url
            except Exception as e:
                logger.warning(f"[{self.plugin_name}] 检查结果时出错: {e}")
                # 出错时稍微等待一下再继续
                await asyncio.sleep(2)

        logger.warning(f"[{self.plugin_name}] 等待超时: {max_wait_time}秒")
        return None

    def _find_task_result(self, history_data, room_id):
        """在历史记录中查找任务结果"""
        try:
            parameter = history_data.get('parameter', {})
            history_list = parameter.get('list', [])

            for group in history_list:
                items = group.get('items', [])
                for item in items:
                    if item.get('room_id') == room_id:
                        title_image = item.get('title_image', '')
                        if title_image:
                            # 检查是否是convert_save路径（生成结果）
                            if 'convert_save' in title_image:
                                # 转换为原图URL（去掉缩略图参数）
                                original_url = title_image.replace('!thumb-w100-webp', '')
                                return original_url
                            else:
                                # 如果不是convert_save，可能生成还在进行中
                                continue

            return None
        except:
            return None

    # 辅助方法
    async def _get_upload_policy(self):
        """获取上传策略"""
        current_time = str(int(time.time() * 1000))
        params = {
            'app': 'RoboNeo',
            'type': 'roboneo_private',
            'count': '1',
            'version': '1.1.10.6',
            'app_version': '1.3.0',
            'support_s3_upload': 'true',
            'suffix': 'jpg',
            'platform': 'android',
            'sigTime': current_time,
            'sigVersion': '1.2'
        }

        sorted_params = dict(sorted(params.items()))
        param_string = '&'.join([f"{k}={v}" for k, v in sorted_params.items()])
        signature = hashlib.md5(param_string.encode()).hexdigest()
        params['sig'] = signature

        headers = {
            'Access-Token': self.access_token,
            'Host': 'strategy.app.meitudata.com'
        }

        try:
            async with httpx.AsyncClient(timeout=30) as client:
                response = await client.get(self.policy_url, params=params, headers=headers)

                if response.status_code == 200:
                    return response.json()
                return None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 获取上传策略失败: {e}")
            return None

    async def _get_history_list(self):
        """获取历史记录"""
        trace_id = str(uuid.uuid4())
        request_data = {
            "parameter": {
                "path_scene": "historylist",
                "body": "", "features": "", "image_urls": [], "video_urls": [],
                "later_face": 0, "message": "", "room_id": "", "id": "", "content": "",
                "type": "", "task_id": "", "req_mode": "", "time_zone": "Asia/Shanghai",
                "platform": "android", "trace_id": trace_id, "client_id": self.client_id,
                "client_os": "10", "client_channel_id": "And_NeoHomeMobile",
                "lang": "zh-Hans", "area_code": "CN", "version": "1.3.0",
                "uid": self.uid, "gid": self.gnum, "theme": 2,
                "app_scene": "roboneo", "token": "7E14E5FF92D54108A074A34D2083C93F"
            }
        }

        headers = {
            'Access-Token': self.access_token,
            'Content-Type': 'application/json; charset=UTF-8',
            'Host': 'ai-engine-gateway-roboneo.meitu.com'
        }

        try:
            async with httpx.AsyncClient(timeout=30) as client:
                response = await client.post(self.history_url, json=request_data, headers=headers)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('error_code') == 0:
                        return result
                return None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 获取历史记录失败: {e}")
            return None

    def _calculate_aws4_signature(self, secret_key, policy_b64, date_str):
        """计算AWS4签名"""
        def sign(key, msg):
            return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

        def get_signature_key(key, date_stamp, region_name, service_name):
            k_date = sign(('AWS4' + key).encode('utf-8'), date_stamp)
            k_region = sign(k_date, region_name)
            k_service = sign(k_region, service_name)
            k_signing = sign(k_service, 'aws4_request')
            return k_signing

        date_stamp = date_str[:8]
        signing_key = get_signature_key(secret_key, date_stamp, 'cn-north-4', 's3')
        signature = hmac.new(signing_key, policy_b64.encode('utf-8'), hashlib.sha256).hexdigest()
        return signature

    def _create_multipart_data(self, form_data, file_path):
        """创建multipart数据"""
        _boundary = '----WebKitFormBoundaryAppUpload123456'

        with open(file_path, 'rb') as f:
            file_content = f.read()

        filename = os.path.basename(file_path)
        body_parts = []

        fields = [
            ('key', form_data['key']), ('Content-Type', form_data['Content-Type']),
            ('success_action_status', form_data['success_action_status']),
            ('X-Amz-Credential', form_data['X-Amz-Credential']),
            ('X-Amz-Algorithm', form_data['X-Amz-Algorithm']),
            ('X-Amz-Security-Token', form_data['X-Amz-Security-Token']),
            ('X-Amz-Date', form_data['X-Amz-Date']),
            ('Policy', form_data['Policy']), ('X-Amz-Signature', form_data['X-Amz-Signature'])
        ]

        for field_name, field_value in fields:
            part = f'------WebKitFormBoundaryAppUpload123456\r\n'
            part += f'Content-Disposition: form-data; name="{field_name}"\r\n\r\n'
            part += f'{field_value}\r\n'
            body_parts.append(part.encode('utf-8'))

        file_part = f'------WebKitFormBoundaryAppUpload123456\r\n'
        file_part += f'Content-Disposition: form-data; name="file"; filename="{filename}"\r\n'
        file_part += f'Content-Type: image/jpeg\r\n\r\n'
        body_parts.append(file_part.encode('utf-8'))
        body_parts.append(file_content)

        end_part = f'\r\n------WebKitFormBoundaryAppUpload123456--\r\n'
        body_parts.append(end_part.encode('utf-8'))

        body = b''.join(body_parts)
        return body, len(body)
