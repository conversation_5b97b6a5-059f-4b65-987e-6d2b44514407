2025-08-05 22:29:21 | SUCCESS | 读取主设置成功
2025-08-05 22:29:21 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 22:29:21 | INFO | 2025/08/05 22:29:21 GetRedisAddr: 127.0.0.1:6379
2025-08-05 22:29:21 | INFO | 2025/08/05 22:29:21 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 22:29:21 | INFO | 2025/08/05 22:29:21 Server start at :9000
2025-08-05 22:29:21 | SUCCESS | WechatAPI服务已启动
2025-08-05 22:29:22 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 22:29:22 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 22:29:22 | SUCCESS | 登录成功
2025-08-05 22:29:22 | SUCCESS | 已开启自动心跳
2025-08-05 22:29:22 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 22:29:22 | SUCCESS | 数据库初始化成功
2025-08-05 22:29:22 | SUCCESS | 定时任务已启动
2025-08-05 22:29:22 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 22:29:22 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 22:29:23 | INFO | 播客API初始化成功
2025-08-05 22:29:23 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 22:29:23 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 22:29:23 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 22:29:23 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 22:29:23 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 22:29:23 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 22:29:23 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 22:29:23 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 22:29:23 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 22:29:24 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 22:29:24 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 22:29:24 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 22:29:24 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 22:29:24 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 22:29:24 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 22:29:24 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 22:29:24 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 22:29:24 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 22:29:24 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 22:29:24 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 22:29:24 | DEBUG |   - 启用状态: True
2025-08-05 22:29:24 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 22:29:24 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 22:29:24 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 22:29:24 | DEBUG |   - Cookies配置: 已配置
2025-08-05 22:29:24 | DEBUG |   - 限制机制: 已禁用
2025-08-05 22:29:24 | DEBUG |   - 数字选择超时: 120秒
2025-08-05 22:29:24 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 22:29:24 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-05 22:29:24 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 22:29:24 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 22:29:24 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 22:29:24 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 22:29:24 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 22:29:24 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 22:29:24 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 22:29:24 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 22:29:24 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 22:29:24 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 22:29:24 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 22:29:24 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 22:29:24 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 22:29:24 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 22:29:25 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-05 22:29:25 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 22:29:25 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 22:29:26 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 22:29:26 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 22:29:26 | INFO | [yuanbao] 插件初始化完成
2025-08-05 22:29:26 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 22:29:26 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 22:29:26 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 22:29:26 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 22:29:26 | INFO | 处理堆积消息中
2025-08-05 22:29:26 | DEBUG | 接受到 3 条消息
2025-08-05 22:29:27 | SUCCESS | 处理堆积消息完毕
2025-08-05 22:29:27 | SUCCESS | 开始处理消息
2025-08-05 22:29:32 | DEBUG | 收到消息: {'MsgId': 1088918019, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n肘了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404183, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_rzsVPoqW|v1_mlza8rML</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5280780613035718935, 'MsgSeq': 871429992}
2025-08-05 22:29:32 | INFO | 收到文本消息: 消息ID:1088918019 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:肘了
2025-08-05 22:29:32 | DEBUG | [DouBaoImageToImage] 收到文本消息: '肘了' from wxid_ugv5ryus4gz622 in 27852221909@chatroom
2025-08-05 22:29:32 | DEBUG | [DouBaoImageToImage] 命令解析: ['肘了']
2025-08-05 22:29:32 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 22:29:32 | DEBUG | 处理消息内容: '肘了'
2025-08-05 22:29:32 | DEBUG | 消息内容 '肘了' 不匹配任何命令，忽略
2025-08-05 22:29:35 | DEBUG | 收到消息: {'MsgId': 159765003, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_g173eyu8nbm522:\n<msg><emoji fromusername="wxid_g173eyu8nbm522" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="810b45c1776888a503a3f16e363353f6" len="7734" productid="" androidmd5="810b45c1776888a503a3f16e363353f6" androidlen="7734" s60v3md5="810b45c1776888a503a3f16e363353f6" s60v3len="7734" s60v5md5="810b45c1776888a503a3f16e363353f6" s60v5len="7734" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=810b45c1776888a503a3f16e363353f6&amp;filekey=3043020101042f302d02016e040253480420383130623435633137373638383861353033613366313665333633333533663602021e36040d00000004627466730000000132&amp;hy=SH&amp;storeid=266bf6ca100089ba0bd22e7ad0000006e01004fb1534827b7d0d1572866115&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=78dff7cf3128050ebc78677ba166231a&amp;filekey=3043020101042f302d02016e040253480420373864666637636633313238303530656263373836373762613136363233316102021e40040d00000004627466730000000132&amp;hy=SH&amp;storeid=266bf6ca100090e02bd22e7ad0000006e02004fb2534827b7d0d1572866120&amp;ef=2&amp;bizid=1022" aeskey="104857d9962f4600a8419bd067f06b31" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=ee3cfa8bdcd0bd5c1ca914a625097ce9&amp;filekey=3043020101042f302d02016e0402534804206565336366613862646364306264356331636139313461363235303937636539020207b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=266bf6ca10009a9d2bd22e7ad0000006e03004fb3534827b7d0d1572866131&amp;ef=3&amp;bizid=1022" externmd5="37e5a89d781ce059af9ec839c6f2670e" width="144" height="149" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404186, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_hrBSWWuj|v1_3+LqNGMR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7193979724467537722, 'MsgSeq': 871429993}
2025-08-05 22:29:35 | INFO | 收到表情消息: 消息ID:159765003 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 MD5:810b45c1776888a503a3f16e363353f6 大小:7734
2025-08-05 22:29:35 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7193979724467537722
2025-08-05 22:29:35 | DEBUG | 收到消息: {'MsgId': 518277079, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>吨吨吨</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2934324322791781116</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ugv5ryus4gz622</chatusr>\n\t\t\t<displayname>悦菟ིྀ</displayname>\n\t\t\t<content>冒个泡 咕嘟咕嘟</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;831173993&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_FaQbAk54|v1_IRy+7HJ3&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754404176</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_1ul5r40nibpn12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404187, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>70545931e63a850a7cc0bb4169dde5f6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_wzwYQe3D|v1_is7srn0w</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7249824173550825523, 'MsgSeq': 871429994}
2025-08-05 22:29:35 | DEBUG | 从群聊消息中提取发送者: wxid_1ul5r40nibpn12
2025-08-05 22:29:35 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:29:35 | INFO | 收到引用消息: 消息ID:518277079 来自:27852221909@chatroom 发送人:wxid_1ul5r40nibpn12 内容:吨吨吨 引用类型:1
2025-08-05 22:29:35 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:29:35 | INFO | [DouBaoImageToImage] 消息内容: '吨吨吨' from wxid_1ul5r40nibpn12 in 27852221909@chatroom
2025-08-05 22:29:35 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['吨吨吨']
2025-08-05 22:29:35 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:29:35 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:29:35 | INFO |   - 消息内容: 吨吨吨
2025-08-05 22:29:35 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 22:29:35 | INFO |   - 发送人: wxid_1ul5r40nibpn12
2025-08-05 22:29:35 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '冒个泡 咕嘟咕嘟', 'Msgid': '2934324322791781116', 'NewMsgId': '2934324322791781116', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '悦菟ིྀ', 'MsgSource': '<msgsource><sequence_id>831173993</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_FaQbAk54|v1_IRy+7HJ3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754404176', 'SenderWxid': 'wxid_1ul5r40nibpn12'}
2025-08-05 22:29:35 | INFO |   - 引用消息ID: 
2025-08-05 22:29:35 | INFO |   - 引用消息类型: 
2025-08-05 22:29:35 | INFO |   - 引用消息内容: 冒个泡 咕嘟咕嘟
2025-08-05 22:29:35 | INFO |   - 引用消息发送人: wxid_1ul5r40nibpn12
2025-08-05 22:29:37 | DEBUG | 收到消息: {'MsgId': 2009701248, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n肘了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404189, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_KNyB1929|v1_2G4Pz+cj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8797471364993212520, 'MsgSeq': 871429995}
2025-08-05 22:29:37 | INFO | 收到文本消息: 消息ID:2009701248 来自:27852221909@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:肘了
2025-08-05 22:29:37 | DEBUG | [DouBaoImageToImage] 收到文本消息: '肘了' from wxid_1ul5r40nibpn12 in 27852221909@chatroom
2025-08-05 22:29:37 | DEBUG | [DouBaoImageToImage] 命令解析: ['肘了']
2025-08-05 22:29:37 | DEBUG | 处理消息内容: '肘了'
2025-08-05 22:29:37 | DEBUG | 消息内容 '肘了' 不匹配任何命令，忽略
2025-08-05 22:29:39 | DEBUG | 收到消息: {'MsgId': 912931095, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>2680457720533432645</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:2274:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754403654</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404190, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>bf0fa3a74c592106954372a548375d08_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_yUdCK4fE|v1_r5+UbY4s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': 3555807042852962726, 'MsgSeq': 871429996}
2025-08-05 22:29:39 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 22:29:39 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:29:39 | INFO | 收到引用消息: 消息ID:912931095 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 22:29:39 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:29:39 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 22:29:39 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 22:29:39 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:29:39 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:29:39 | INFO |   - 消息内容: 跟着唱
2025-08-05 22:29:39 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 22:29:39 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 22:29:39 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:2274:0\n', 'Msgid': '2680457720533432645', 'NewMsgId': '2680457720533432645', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754403654', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 22:29:39 | INFO |   - 引用消息ID: 
2025-08-05 22:29:39 | INFO |   - 引用消息类型: 
2025-08-05 22:29:39 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:2274:0

2025-08-05 22:29:39 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 22:29:40 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 引用的语音消息已过期或未找到，请重新发送语音
2025-08-05 22:29:48 | DEBUG | 收到消息: {'MsgId': 1674016777, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="2909" length="4267" bufid="0" aeskey="a7b35bd40adc4a01399c958bd85613ab" voiceurl="3052020100044b30490201000204a95c809d02032df9270204d9089324020468921568042463323866316464312d373762612d343934612d623835642d36366236633136346236613802040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600562229080525e376c310ccf103" fromusername="wxid_ubbh6q832tcs21" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404200, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_fPdSW6oE|v1_BEcNvdr9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段语音', 'NewMsgId': 677282590330098313, 'MsgSeq': 871429999}
2025-08-05 22:29:48 | INFO | 收到语音消息: 消息ID:1674016777 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="2909" length="4267" bufid="0" aeskey="a7b35bd40adc4a01399c958bd85613ab" voiceurl="3052020100044b30490201000204a95c809d02032df9270204d9089324020468921568042463323866316464312d373762612d343934612d623835642d36366236633136346236613802040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600562229080525e376c310ccf103" fromusername="wxid_ubbh6q832tcs21" /></msg>
2025-08-05 22:30:04 | DEBUG | 收到消息: {'MsgId': 162971933, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '27852221909@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_idzryo4rneok22</fromusername>\n  <chatusername>27852221909@chatroom</chatusername>\n  <pattedusername>wxid_g173eyu8nbm522</pattedusername>\n  <patsuffix><![CDATA[]]></patsuffix>\n  <patsuffixversion>0</patsuffixversion>\n\n\n\n\n  <template><![CDATA["${wxid_idzryo4rneok22}" 拍了拍 "${wxid_g173eyu8nbm522}"]]></template>\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404213, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4151675232676411251, 'MsgSeq': 871430000}
2025-08-05 22:30:04 | DEBUG | 系统消息类型: pat
2025-08-05 22:30:04 | INFO | 收到拍一拍消息: 消息ID:162971933 来自:27852221909@chatroom 发送人:27852221909@chatroom 拍者:wxid_idzryo4rneok22 被拍:wxid_g173eyu8nbm522 后缀:None
2025-08-05 22:30:04 | DEBUG | [PatReply] 被拍者 wxid_g173eyu8nbm522 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-08-05 22:30:19 | DEBUG | 收到消息: {'MsgId': 1724942558, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>677282590330098313</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:2909:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754404200</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404231, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>5ee8615a4048f34d4d02d5ff05231a99_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_fTMxACQe|v1_iANmKu1c</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': 9170032378366573125, 'MsgSeq': 871430001}
2025-08-05 22:30:19 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 22:30:19 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:30:19 | INFO | 收到引用消息: 消息ID:1724942558 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 22:30:19 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:30:19 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 22:30:19 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 22:30:19 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:30:19 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:30:19 | INFO |   - 消息内容: 跟着唱
2025-08-05 22:30:19 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 22:30:19 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 22:30:19 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:2909:0\n', 'Msgid': '677282590330098313', 'NewMsgId': '677282590330098313', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754404200', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 22:30:19 | INFO |   - 引用消息ID: 
2025-08-05 22:30:19 | INFO |   - 引用消息类型: 
2025-08-05 22:30:19 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:2909:0

2025-08-05 22:30:19 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 22:30:20 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 引用的语音消息已过期或未找到，请重新发送语音
2025-08-05 22:30:40 | DEBUG | 收到消息: {'MsgId': 1934591716, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1099" length="1237" bufid="0" aeskey="ef299a94da825e65374e1e2ae18b8dac" voiceurl="3052020100044b30490201000204a95c809d02032df927020429089324020468921594042466343464396233382d396665322d343963302d623438662d31326332326237336264343202040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600422230080525e376c31bef8100" fromusername="wxid_ubbh6q832tcs21" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404244, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_8RaEdfpA|v1_ZgwTCNSc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段语音', 'NewMsgId': 3860911888099846358, 'MsgSeq': 871430004}
2025-08-05 22:30:40 | INFO | 收到语音消息: 消息ID:1934591716 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1099" length="1237" bufid="0" aeskey="ef299a94da825e65374e1e2ae18b8dac" voiceurl="3052020100044b30490201000204a95c809d02032df927020429089324020468921594042466343464396233382d396665322d343963302d623438662d31326332326237336264343202040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600422230080525e376c31bef8100" fromusername="wxid_ubbh6q832tcs21" /></msg>
2025-08-05 22:30:46 | DEBUG | 收到消息: {'MsgId': 30698295, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>3860911888099846358</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:1099:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754404243</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404257, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>9fcc2adcec41963613aa036a063cc75b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_lcLweGJi|v1_vppi7xFY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': 9030429062201040974, 'MsgSeq': 871430005}
2025-08-05 22:30:46 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 22:30:46 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:30:46 | INFO | 收到引用消息: 消息ID:30698295 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 22:30:47 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:30:47 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 22:30:47 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 22:30:47 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:30:47 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:30:47 | INFO |   - 消息内容: 跟着唱
2025-08-05 22:30:47 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 22:30:47 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 22:30:47 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:1099:0\n', 'Msgid': '3860911888099846358', 'NewMsgId': '3860911888099846358', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754404243', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 22:30:47 | INFO |   - 引用消息ID: 
2025-08-05 22:30:47 | INFO |   - 引用消息类型: 
2025-08-05 22:30:47 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:1099:0

2025-08-05 22:30:47 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 22:30:49 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 引用的语音消息已过期或未找到，请重新发送语音
2025-08-05 22:30:49 | DEBUG | 收到消息: {'MsgId': 1499472610, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n卧槽'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404260, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_5dE7hD00|v1_PG/uJv4T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 卧槽', 'NewMsgId': 7898306888176666352, 'MsgSeq': 871430006}
2025-08-05 22:30:49 | INFO | 收到文本消息: 消息ID:1499472610 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:卧槽
2025-08-05 22:30:50 | DEBUG | [DouBaoImageToImage] 收到文本消息: '卧槽' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-05 22:30:50 | DEBUG | [DouBaoImageToImage] 命令解析: ['卧槽']
2025-08-05 22:30:50 | DEBUG | 处理消息内容: '卧槽'
2025-08-05 22:30:50 | DEBUG | 消息内容 '卧槽' 不匹配任何命令，忽略
2025-08-05 22:30:55 | DEBUG | 收到消息: {'MsgId': 1568697799, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n我楼上好像在吃榴莲'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404266, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_ZZaN+klF|v1_eKeLv6PW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 我楼上好像在吃榴莲', 'NewMsgId': 4113441218459425169, 'MsgSeq': 871430009}
2025-08-05 22:30:55 | INFO | 收到文本消息: 消息ID:1568697799 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:我楼上好像在吃榴莲
2025-08-05 22:30:56 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我楼上好像在吃榴莲' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-05 22:30:56 | DEBUG | [DouBaoImageToImage] 命令解析: ['我楼上好像在吃榴莲']
2025-08-05 22:30:56 | DEBUG | 处理消息内容: '我楼上好像在吃榴莲'
2025-08-05 22:30:56 | DEBUG | 消息内容 '我楼上好像在吃榴莲' 不匹配任何命令，忽略
2025-08-05 22:31:05 | DEBUG | 收到消息: {'MsgId': 7519456, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n怎么突然一股榴莲的臭味'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404277, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_X1joOqE1|v1_wKWu8j6I</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 怎么突然一股榴莲的臭味', 'NewMsgId': 2844582597854239435, 'MsgSeq': 871430010}
2025-08-05 22:31:05 | INFO | 收到文本消息: 消息ID:7519456 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:怎么突然一股榴莲的臭味
2025-08-05 22:31:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '怎么突然一股榴莲的臭味' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-05 22:31:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['怎么突然一股榴莲的臭味']
2025-08-05 22:31:05 | DEBUG | 处理消息内容: '怎么突然一股榴莲的臭味'
2025-08-05 22:31:05 | DEBUG | 消息内容 '怎么突然一股榴莲的臭味' 不匹配任何命令，忽略
2025-08-05 22:31:29 | DEBUG | 收到消息: {'MsgId': 136498354, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n还是那种臭腐乳啊🫢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404300, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_K9zsimr9|v1_R0mliWr2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 还是那种臭腐乳啊🫢', 'NewMsgId': 813770016266912465, 'MsgSeq': 871430011}
2025-08-05 22:31:29 | INFO | 收到文本消息: 消息ID:136498354 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:还是那种臭腐乳啊🫢
2025-08-05 22:31:29 | DEBUG | [DouBaoImageToImage] 收到文本消息: '还是那种臭腐乳啊🫢' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-05 22:31:29 | DEBUG | [DouBaoImageToImage] 命令解析: ['还是那种臭腐乳啊🫢']
2025-08-05 22:31:29 | DEBUG | 处理消息内容: '还是那种臭腐乳啊🫢'
2025-08-05 22:31:29 | DEBUG | 消息内容 '还是那种臭腐乳啊🫢' 不匹配任何命令，忽略
2025-08-05 22:31:36 | DEBUG | 收到消息: {'MsgId': 113178305, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n我靠'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404307, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_beutQ3DZ|v1_12/5N/kb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 我靠', 'NewMsgId': 6902179441614662685, 'MsgSeq': 871430012}
2025-08-05 22:31:36 | INFO | 收到文本消息: 消息ID:113178305 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:我靠
2025-08-05 22:31:36 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我靠' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-05 22:31:36 | DEBUG | [DouBaoImageToImage] 命令解析: ['我靠']
2025-08-05 22:31:36 | DEBUG | 处理消息内容: '我靠'
2025-08-05 22:31:36 | DEBUG | 消息内容 '我靠' 不匹配任何命令，忽略
2025-08-05 22:31:40 | DEBUG | 收到消息: {'MsgId': 1905542004, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n大晚上的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404312, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_wuUM35nq|v1_b8Fvtjbf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 大晚上的', 'NewMsgId': 3616861783501794765, 'MsgSeq': 871430013}
2025-08-05 22:31:40 | INFO | 收到文本消息: 消息ID:1905542004 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:大晚上的
2025-08-05 22:31:40 | DEBUG | [DouBaoImageToImage] 收到文本消息: '大晚上的' from wxid_wlnzvr8ivgd422 in 48097389945@chatroom
2025-08-05 22:31:40 | DEBUG | [DouBaoImageToImage] 命令解析: ['大晚上的']
2025-08-05 22:31:40 | DEBUG | 处理消息内容: '大晚上的'
2025-08-05 22:31:40 | DEBUG | 消息内容 '大晚上的' 不匹配任何命令，忽略
2025-08-05 22:31:49 | DEBUG | 收到消息: {'MsgId': 1932908723, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_71638u5vutp912:\n别乱想了，会不会是在处理尸体'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404320, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_n+ubY1Vc|v1_Nx/aCvvy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '老甜甜 : 别乱想了，会不会是在处理尸体', 'NewMsgId': 4418601542056365544, 'MsgSeq': 871430014}
2025-08-05 22:31:49 | INFO | 收到文本消息: 消息ID:1932908723 来自:48097389945@chatroom 发送人:wxid_71638u5vutp912 @:[] 内容:别乱想了，会不会是在处理尸体
2025-08-05 22:31:49 | DEBUG | [DouBaoImageToImage] 收到文本消息: '别乱想了，会不会是在处理尸体' from wxid_71638u5vutp912 in 48097389945@chatroom
2025-08-05 22:31:49 | DEBUG | [DouBaoImageToImage] 命令解析: ['别乱想了，会不会是在处理尸体']
2025-08-05 22:31:49 | DEBUG | 处理消息内容: '别乱想了，会不会是在处理尸体'
2025-08-05 22:31:49 | DEBUG | 消息内容 '别乱想了，会不会是在处理尸体' 不匹配任何命令，忽略
2025-08-05 22:35:07 | DEBUG | 收到消息: {'MsgId': 527391382, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '27852221909@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_ubbh6q832tcs21</fromusername>\n  <chatusername>27852221909@chatroom</chatusername>\n  <pattedusername>wxid_idzryo4rneok22</pattedusername>\n  <patsuffix><![CDATA[表示已读]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_ubbh6q832tcs21}" 拍了拍 "${wxid_idzryo4rneok22}" 表示已读]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404517, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8037222781644506927, 'MsgSeq': 871430015}
2025-08-05 22:35:07 | DEBUG | 系统消息类型: pat
2025-08-05 22:35:07 | INFO | 收到拍一拍消息: 消息ID:527391382 来自:27852221909@chatroom 发送人:27852221909@chatroom 拍者:wxid_ubbh6q832tcs21 被拍:wxid_idzryo4rneok22 后缀:表示已读
2025-08-05 22:35:07 | DEBUG | [PatReply] 被拍者 wxid_idzryo4rneok22 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-08-05 22:36:13 | DEBUG | 收到消息: {'MsgId': 1382935154, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<msg><emoji fromusername="wxid_c3jkq1ylevnb12" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="771055217bf0c0d8c5b1831074588f2e" len="2756441" productid="" androidmd5="771055217bf0c0d8c5b1831074588f2e" androidlen="2756441" s60v3md5="771055217bf0c0d8c5b1831074588f2e" s60v3len="2756441" s60v5md5="771055217bf0c0d8c5b1831074588f2e" s60v5len="2756441" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=771055217bf0c0d8c5b1831074588f2e&amp;filekey=30440201010430302e02016e0402535a0420373731303535323137626630633064386335623138333130373435383866326502032a0f59040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f00064fe561bf27bc0000006e01004fb1535a24267bc1e6dd7c75c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=3ef3f4ada792e12ab819f3e45d1f7f06&amp;filekey=30440201010430302e02016e0402535a0420336566336634616461373932653132616238313966336534356431663766303602032a0f60040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f0008ff0a61bf27bc0000006e02004fb2535a24267bc1e6dd7c786&amp;ef=2&amp;bizid=1022" aeskey="fc4006a48e6f4385af52021c4ad9d113" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=05ccfbfdf6d3c65e95974a6e9c5c7b4b&amp;filekey=30440201010430302e02016e0402535a04203035636366626664663664336336356539353937346136653963356337623462020301f140040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26888975f000bf49261bf27bc0000006e03004fb3535a24267bc1e6dd7c7b9&amp;ef=3&amp;bizid=1022" externmd5="5e679291c174c052a3ab4cbb7d12a2d6" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404585, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_1EIFuDAB|v1_97An1kaO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2981137936905492367, 'MsgSeq': 871430016}
2025-08-05 22:36:13 | INFO | 收到表情消息: 消息ID:1382935154 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 MD5:771055217bf0c0d8c5b1831074588f2e 大小:2756441
2025-08-05 22:36:13 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2981137936905492367
2025-08-05 22:36:21 | DEBUG | 收到消息: {'MsgId': 2101271190, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n排位连跪4把'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404593, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_cpD80JvL|v1_HZ2Ih+NS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2583813686825852603, 'MsgSeq': 871430017}
2025-08-05 22:36:21 | INFO | 收到文本消息: 消息ID:2101271190 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:排位连跪4把
2025-08-05 22:36:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '排位连跪4把' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 22:36:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['排位连跪4把']
2025-08-05 22:36:21 | DEBUG | 处理消息内容: '排位连跪4把'
2025-08-05 22:36:21 | DEBUG | 消息内容 '排位连跪4把' 不匹配任何命令，忽略
2025-08-05 22:36:40 | DEBUG | 收到消息: {'MsgId': 564015055, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="0ed9e53cdd2dae0c281f9418868fd4af" len="209877" productid="" androidmd5="0ed9e53cdd2dae0c281f9418868fd4af" androidlen="209877" s60v3md5="0ed9e53cdd2dae0c281f9418868fd4af" s60v3len="209877" s60v5md5="0ed9e53cdd2dae0c281f9418868fd4af" s60v5len="209877" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=0ed9e53cdd2dae0c281f9418868fd4af&amp;filekey=30350201010421301f020201060402535a04100ed9e53cdd2dae0c281f9418868fd4af02030333d5040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303232303832303031303733343030303462653166303030303030303030623766623530623030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=40383d5673beb41a26234261d47e342f&amp;filekey=30350201010421301f0202010604025348041040383d5673beb41a26234261d47e342f02030333e0040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303832303031303733343030303965613962303030303030303033346435393630393030303030313036&amp;bizid=1023" aeskey="4909738e0c12a5caef2537a3eb523edd" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=8490451c9a3871d73d2857b560c7eab2&amp;filekey=30340201010420301e020201060402534804108490451c9a3871d73d2857b560c7eab202025490040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303832303031303733343030306430386335303030303030303064643564393930623030303030313036&amp;bizid=1023" externmd5="62c7d2197d0fae209dde1ea4f8e8860b" width="586" height="640" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404612, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_Fj+s4ytT|v1_UWuqaJhB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 2042701388172594923, 'MsgSeq': 871430018}
2025-08-05 22:36:40 | INFO | 收到表情消息: 消息ID:564015055 来自:48097389945@chatroom 发送人:last--exile MD5:0ed9e53cdd2dae0c281f9418868fd4af 大小:209877
2025-08-05 22:36:41 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2042701388172594923
2025-08-05 22:36:59 | DEBUG | 收到消息: {'MsgId': 655873610, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n单排10把赢2把'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404630, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_lFIt3iF+|v1_yLI10I6K</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6971760132515393623, 'MsgSeq': 871430019}
2025-08-05 22:36:59 | INFO | 收到文本消息: 消息ID:655873610 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:单排10把赢2把
2025-08-05 22:36:59 | DEBUG | [DouBaoImageToImage] 收到文本消息: '单排10把赢2把' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 22:36:59 | DEBUG | [DouBaoImageToImage] 命令解析: ['单排10把赢2把']
2025-08-05 22:36:59 | DEBUG | 处理消息内容: '单排10把赢2把'
2025-08-05 22:36:59 | DEBUG | 消息内容 '单排10把赢2把' 不匹配任何命令，忽略
2025-08-05 22:37:01 | DEBUG | 收到消息: {'MsgId': 1317612913, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n求个师傅教能让我少按p'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404633, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_H39TP31I|v1_wf6VXMqP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6926620246937577563, 'MsgSeq': 871430020}
2025-08-05 22:37:01 | INFO | 收到文本消息: 消息ID:1317612913 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:求个师傅教能让我少按p
2025-08-05 22:37:01 | DEBUG | [DouBaoImageToImage] 收到文本消息: '求个师傅教能让我少按p' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 22:37:01 | DEBUG | [DouBaoImageToImage] 命令解析: ['求个师傅教能让我少按p']
2025-08-05 22:37:01 | DEBUG | 处理消息内容: '求个师傅教能让我少按p'
2025-08-05 22:37:01 | DEBUG | 消息内容 '求个师傅教能让我少按p' 不匹配任何命令，忽略
2025-08-05 22:37:03 | DEBUG | 收到消息: {'MsgId': 1927921039, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<msg><emoji fromusername="wxid_x4s6k999g6qg22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="5b6b73ed604b1e8cf7ef60b5d1e3b802" len="27217" productid="" androidmd5="5b6b73ed604b1e8cf7ef60b5d1e3b802" androidlen="27217" s60v3md5="5b6b73ed604b1e8cf7ef60b5d1e3b802" s60v3len="27217" s60v5md5="5b6b73ed604b1e8cf7ef60b5d1e3b802" s60v5len="27217" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=5b6b73ed604b1e8cf7ef60b5d1e3b802&amp;filekey=3043020101042f302d02016e0402535a0420356236623733656436303462316538636637656636306235643165336238303202026a51040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266b089670005e3fbf1bd91ce0000006e01004fb1535a212498809671ca8b0&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=543abb17a57a282d2b612e5b48d2e96c&amp;filekey=3043020101042f302d02016e0402535a0420353433616262313761353761323832643262363132653562343864326539366302026a60040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266b0896700065bfff1bd91ce0000006e02004fb2535a212498809671ca8c0&amp;ef=2&amp;bizid=1022" aeskey="3c96aeebb9124e259ec890d9911ffd16" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=ce46df2c28016fa6b792b05669cd71c6&amp;filekey=3043020101042f302d02016e0402535a0420636534366466326332383031366661366237393262303536363963643731633602022580040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266b089670006ced8f1bd91ce0000006e03004fb3535a212498809671ca8d7&amp;ef=3&amp;bizid=1022" externmd5="f0037964c04ba842a03f6fd7ec0000bf" width="640" height="637" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404634, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_6RldzHoM|v1_SNBhQ8Dt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7685584154904731618, 'MsgSeq': 871430021}
2025-08-05 22:37:03 | INFO | 收到表情消息: 消息ID:1927921039 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 MD5:5b6b73ed604b1e8cf7ef60b5d1e3b802 大小:27217
2025-08-05 22:37:03 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7685584154904731618
2025-08-05 22:37:13 | DEBUG | 收到消息: {'MsgId': 2007946562, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>同道中人</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>6971760132515393623</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_x4s6k999g6qg22</chatusr>\n\t\t\t<displayname>栀栀</displayname>\n\t\t\t<content>单排10把赢2把</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777843216&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_Jsd0RDV5|v1_RVWFKPvW&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754404630</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754404645, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>e50861037b5d5584cfd8b5171ac6e2e5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_0V5thOFC|v1_aw96k1mJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4227504244135850405, 'MsgSeq': 871430022}
2025-08-05 22:37:13 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-05 22:37:13 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:37:13 | INFO | 收到引用消息: 消息ID:2007946562 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 内容:同道中人 引用类型:1
2025-08-05 22:37:13 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:37:13 | INFO | [DouBaoImageToImage] 消息内容: '同道中人' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 22:37:13 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['同道中人']
2025-08-05 22:37:13 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:37:13 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:37:13 | INFO |   - 消息内容: 同道中人
2025-08-05 22:37:13 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 22:37:13 | INFO |   - 发送人: wxid_c3jkq1ylevnb12
2025-08-05 22:37:13 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '单排10把赢2把', 'Msgid': '6971760132515393623', 'NewMsgId': '6971760132515393623', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '栀栀', 'MsgSource': '<msgsource><sequence_id>777843216</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Jsd0RDV5|v1_RVWFKPvW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754404630', 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-05 22:37:13 | INFO |   - 引用消息ID: 
2025-08-05 22:37:13 | INFO |   - 引用消息类型: 
2025-08-05 22:37:13 | INFO |   - 引用消息内容: 单排10把赢2把
2025-08-05 22:37:13 | INFO |   - 引用消息发送人: wxid_c3jkq1ylevnb12
