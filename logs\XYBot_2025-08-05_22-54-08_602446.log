2025-08-05 22:54:09 | SUCCESS | 读取主设置成功
2025-08-05 22:54:09 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 22:54:10 | INFO | 2025/08/05 22:54:10 GetRedisAddr: 127.0.0.1:6379
2025-08-05 22:54:10 | INFO | 2025/08/05 22:54:10 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 22:54:10 | INFO | 2025/08/05 22:54:10 Server start at :9000
2025-08-05 22:54:10 | SUCCESS | WechatAPI服务已启动
2025-08-05 22:54:11 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 22:54:11 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 22:54:11 | SUCCESS | 登录成功
2025-08-05 22:54:11 | SUCCESS | 已开启自动心跳
2025-08-05 22:54:11 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 22:54:11 | SUCCESS | 数据库初始化成功
2025-08-05 22:54:11 | SUCCESS | 定时任务已启动
2025-08-05 22:54:11 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 22:54:11 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 22:54:12 | INFO | 播客API初始化成功
2025-08-05 22:54:12 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 22:54:12 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 22:54:12 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 22:54:12 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 22:54:12 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 22:54:12 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 22:54:12 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 22:54:12 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 22:54:12 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 22:54:12 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 22:54:12 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 22:54:12 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 22:54:12 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 22:54:12 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 22:54:12 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 22:54:12 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 22:54:12 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 22:54:12 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 22:54:12 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 22:54:12 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 22:54:12 | DEBUG |   - 启用状态: True
2025-08-05 22:54:12 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 22:54:12 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 22:54:12 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 22:54:12 | DEBUG |   - Cookies配置: 已配置
2025-08-05 22:54:12 | DEBUG |   - 限制机制: 已禁用
2025-08-05 22:54:12 | DEBUG |   - 数字选择超时: 120秒
2025-08-05 22:54:12 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 22:54:13 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-05 22:54:13 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 22:54:13 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 22:54:13 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 22:54:13 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 22:54:13 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 22:54:13 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 22:54:13 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 22:54:13 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 22:54:13 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 22:54:13 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 22:54:13 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 22:54:13 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 22:54:13 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 22:54:13 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-05 22:54:13 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 22:54:14 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 22:54:14 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 22:54:14 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 22:54:14 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 22:54:14 | INFO | [yuanbao] 插件初始化完成
2025-08-05 22:54:14 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 22:54:14 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 22:54:14 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 22:54:14 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 22:54:14 | INFO | 处理堆积消息中
2025-08-05 22:54:15 | SUCCESS | 处理堆积消息完毕
2025-08-05 22:54:15 | SUCCESS | 开始处理消息
2025-08-05 22:54:22 | DEBUG | 收到消息: {'MsgId': 831805963, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1864" length="2567" bufid="0" aeskey="1e44c95020a7461495be76e9134da64e" voiceurl="3052020100044b30490201000204a95c809d02032df9270204d4089324020468921b2a042436633635376330372d333433302d346131622d386365332d35306566383833313835383102040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600312254080525e376c318d69105" fromusername="wxid_ubbh6q832tcs21" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405674, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_tdsubTmH|v1_X59dSBy+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段语音', 'NewMsgId': 7399934331075284847, 'MsgSeq': 871430089}
2025-08-05 22:54:22 | INFO | 收到语音消息: 消息ID:831805963 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1864" length="2567" bufid="0" aeskey="1e44c95020a7461495be76e9134da64e" voiceurl="3052020100044b30490201000204a95c809d02032df9270204d4089324020468921b2a042436633635376330372d333433302d346131622d386365332d35306566383833313835383102040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600312254080525e376c318d69105" fromusername="wxid_ubbh6q832tcs21" /></msg>
2025-08-05 22:54:28 | DEBUG | 收到消息: {'MsgId': 217713967, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n好的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405680, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ZGNf0fp4|v1_AB4e4lxi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5580695551156262728, 'MsgSeq': 871430090}
2025-08-05 22:54:28 | INFO | 收到文本消息: 消息ID:217713967 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:好的
2025-08-05 22:54:28 | DEBUG | [DouBaoImageToImage] 收到文本消息: '好的' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 22:54:28 | DEBUG | [DouBaoImageToImage] 命令解析: ['好的']
2025-08-05 22:54:28 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 22:54:28 | DEBUG | 处理消息内容: '好的'
2025-08-05 22:54:28 | DEBUG | 消息内容 '好的' 不匹配任何命令，忽略
2025-08-05 22:54:33 | DEBUG | 收到消息: {'MsgId': 462535141, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>7399934331075284847</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:1864:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754405673</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405685, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>3e7a80942faba6b106d9e07ab32d0e6f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_vwDJsvoc|v1_fD8G1FVc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': *****************12, 'MsgSeq': 871430091}
2025-08-05 22:54:33 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 22:54:33 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:54:33 | INFO | 收到引用消息: 消息ID:462535141 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 22:54:33 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:54:33 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 22:54:33 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 22:54:33 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:54:33 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:54:33 | INFO |   - 消息内容: 跟着唱
2025-08-05 22:54:33 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 22:54:33 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 22:54:33 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:1864:0\n', 'Msgid': '7399934331075284847', 'NewMsgId': '7399934331075284847', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754405673', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 22:54:33 | INFO |   - 引用消息ID: 
2025-08-05 22:54:33 | INFO |   - 引用消息类型: 
2025-08-05 22:54:33 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:1864:0

2025-08-05 22:54:33 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 22:54:33 | ERROR | [VoiceTest] 处理引用语音消息异常: Extra data: line 1 column 69 (char 68)
2025-08-05 22:54:34 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 处理引用语音时出错
2025-08-05 22:56:01 | DEBUG | 收到消息: {'MsgId': 1517091667, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>多勾搭点进来，能多消费点最好</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>717286257765137854</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_xfxd40diz3bd22</chatusr>\n\t\t\t<displayname>初见</displayname>\n\t\t\t<content>像现在进团的，是活动结束前能消费1.5w的就行吧？</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;810053526&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_iJTyk0Ri|v1_tLxcOG12&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754405525</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_x4s6k999g6qg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405772, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>54a0a87b6d2325dc70d508889d66915d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_+OBVFhAY|v1_oX65HV7L</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4312046901354680692, 'MsgSeq': 871430094}
2025-08-05 22:56:01 | DEBUG | 从群聊消息中提取发送者: wxid_x4s6k999g6qg22
2025-08-05 22:56:01 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:56:01 | INFO | 收到引用消息: 消息ID:1517091667 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 内容:多勾搭点进来，能多消费点最好 引用类型:1
2025-08-05 22:56:01 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:56:01 | INFO | [DouBaoImageToImage] 消息内容: '多勾搭点进来，能多消费点最好' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 22:56:01 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['多勾搭点进来，能多消费点最好']
2025-08-05 22:56:01 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:56:01 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:56:01 | INFO |   - 消息内容: 多勾搭点进来，能多消费点最好
2025-08-05 22:56:01 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 22:56:01 | INFO |   - 发送人: wxid_x4s6k999g6qg22
2025-08-05 22:56:01 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '像现在进团的，是活动结束前能消费1.5w的就行吧？', 'Msgid': '717286257765137854', 'NewMsgId': '717286257765137854', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '初见', 'MsgSource': '<msgsource><sequence_id>810053526</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_iJTyk0Ri|v1_tLxcOG12</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754405525', 'SenderWxid': 'wxid_x4s6k999g6qg22'}
2025-08-05 22:56:01 | INFO |   - 引用消息ID: 
2025-08-05 22:56:01 | INFO |   - 引用消息类型: 
2025-08-05 22:56:01 | INFO |   - 引用消息内容: 像现在进团的，是活动结束前能消费1.5w的就行吧？
2025-08-05 22:56:01 | INFO |   - 引用消息发送人: wxid_x4s6k999g6qg22
2025-08-05 22:56:05 | DEBUG | 收到消息: {'MsgId': 4589868, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<msg><emoji fromusername="wxid_x4s6k999g6qg22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="8a88a8e86c0100d15114df02df88b774" len="625025" productid="" androidmd5="8a88a8e86c0100d15114df02df88b774" androidlen="625025" s60v3md5="8a88a8e86c0100d15114df02df88b774" s60v3len="625025" s60v5md5="8a88a8e86c0100d15114df02df88b774" s60v5len="625025" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=8a88a8e86c0100d15114df02df88b774&amp;filekey=30350201010421301f020201060402535a04108a88a8e86c0100d15114df02df88b7740203098981040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26621cc47000061f4166369090000010600004f50535a28f64bc1e7858f9df&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=36058121b86f83e1db492291488e14c1&amp;filekey=30350201010421301f020201060402535a041036058121b86f83e1db492291488e14c10203098990040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26621cc4700035921166369090000010600004f50535a27bf601156898cebd&amp;bizid=1023" aeskey="067ec23b6c122552842192469e352ce4" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=a037f765668b7d92f9f6b0ba267499f6&amp;filekey=30350201010421301f020201060402535a0410a037f765668b7d92f9f6b0ba267499f60203014d80040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26621cc4800005026166369090000010600004f50535a0d849880972fae3ff&amp;bizid=1023" externmd5="bcbfc2f16b6b28c1be98948f9b2c35c1" width="280" height="280" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405777, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_PZQ4Db2L|v1_w4+Jqdet</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5168302936216803256, 'MsgSeq': 871430095}
2025-08-05 22:56:05 | INFO | 收到表情消息: 消息ID:4589868 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 MD5:8a88a8e86c0100d15114df02df88b774 大小:625025
2025-08-05 22:56:05 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5168302936216803256
2025-08-05 22:56:18 | DEBUG | 收到消息: {'MsgId': 1424330376, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n哈哈哈哈，我也是房间里有小美问我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405790, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_CkV5tCIw|v1_9uGv38fR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7627178737011562577, 'MsgSeq': 871430096}
2025-08-05 22:56:18 | INFO | 收到文本消息: 消息ID:1424330376 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:哈哈哈哈，我也是房间里有小美问我
2025-08-05 22:56:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈哈哈，我也是房间里有小美问我' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 22:56:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈哈哈，我也是房间里有小美问我']
2025-08-05 22:56:18 | DEBUG | 处理消息内容: '哈哈哈哈，我也是房间里有小美问我'
2025-08-05 22:56:18 | DEBUG | 消息内容 '哈哈哈哈，我也是房间里有小美问我' 不匹配任何命令，忽略
2025-08-05 22:56:34 | DEBUG | 收到消息: {'MsgId': 907339439, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n把前任都勾搭进来'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405805, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_zgsOTjEi|v1_FzRwFMsA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8769663473467513555, 'MsgSeq': 871430097}
2025-08-05 22:56:34 | INFO | 收到文本消息: 消息ID:907339439 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:把前任都勾搭进来
2025-08-05 22:56:34 | DEBUG | [DouBaoImageToImage] 收到文本消息: '把前任都勾搭进来' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 22:56:34 | DEBUG | [DouBaoImageToImage] 命令解析: ['把前任都勾搭进来']
2025-08-05 22:56:34 | DEBUG | 处理消息内容: '把前任都勾搭进来'
2025-08-05 22:56:34 | DEBUG | 消息内容 '把前任都勾搭进来' 不匹配任何命令，忽略
2025-08-05 22:56:38 | DEBUG | 收到消息: {'MsgId': 1986731587, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n表现好爬位包了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405810, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_xRGRjx6y|v1_pGuAiXnT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8008704128405253562, 'MsgSeq': 871430098}
2025-08-05 22:56:38 | INFO | 收到文本消息: 消息ID:1986731587 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:表现好爬位包了
2025-08-05 22:56:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '表现好爬位包了' from tianen532965049 in 27852221909@chatroom
2025-08-05 22:56:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['表现好爬位包了']
2025-08-05 22:56:38 | DEBUG | 处理消息内容: '表现好爬位包了'
2025-08-05 22:56:38 | DEBUG | 消息内容 '表现好爬位包了' 不匹配任何命令，忽略
2025-08-05 22:56:40 | DEBUG | 收到消息: {'MsgId': 1676473065, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="dc8439fe4537b0472e844ccab8422c70" len = "367061" productid="" androidmd5="dc8439fe4537b0472e844ccab8422c70" androidlen="367061" s60v3md5 = "dc8439fe4537b0472e844ccab8422c70" s60v3len="367061" s60v5md5 = "dc8439fe4537b0472e844ccab8422c70" s60v5len="367061" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=dc8439fe4537b0472e844ccab8422c70&amp;filekey=30350201010421301f020201060402535a0410dc8439fe4537b0472e844ccab8422c7002030599d5040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f26660000baa91ccfef41d0000010600004f50535a2c863bc1e786d4cfe&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=809f312713a09fe8d034b9564e7e7f5e&amp;filekey=30350201010421301f020201060402535a0410809f312713a09fe8d034b9564e7e7f5e02030599e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f26660000e8c3cccfef41d0000010600004f50535a26267bc1e7e006ddd&amp;bizid=1023" aeskey= "c9460931467dfa2c505f01fcc133ff99" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=4aea69e6407a246b54f3db2303e0d0b6&amp;filekey=30350201010421301f020201060402535a04104aea69e6407a246b54f3db2303e0d0b6020301d6d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f2a6b00003fdba153237b40000010600004f50535a2134b88096734fc87&amp;bizid=1023" externmd5 = "5079bddab4225575c258e8b98b98ab6c" width= "111" height= "81" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405812, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_jWcuzlkO|v1_vHepQ1iW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7876365471183315579, 'MsgSeq': 871430099}
2025-08-05 22:56:40 | INFO | 收到表情消息: 消息ID:1676473065 来自:27852221909@chatroom 发送人:tianen532965049 MD5:dc8439fe4537b0472e844ccab8422c70 大小:367061
2025-08-05 22:56:40 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7876365471183315579
2025-08-05 22:56:47 | DEBUG | 收到消息: {'MsgId': 911949047, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n有实力的前任'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405819, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_JqgnxLaC|v1_GNUT9iy8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3599027323174935195, 'MsgSeq': 871430100}
2025-08-05 22:56:47 | INFO | 收到文本消息: 消息ID:911949047 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:有实力的前任
2025-08-05 22:56:47 | DEBUG | [DouBaoImageToImage] 收到文本消息: '有实力的前任' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 22:56:47 | DEBUG | [DouBaoImageToImage] 命令解析: ['有实力的前任']
2025-08-05 22:56:47 | DEBUG | 处理消息内容: '有实力的前任'
2025-08-05 22:56:47 | DEBUG | 消息内容 '有实力的前任' 不匹配任何命令，忽略
2025-08-05 22:56:55 | DEBUG | 收到消息: {'MsgId': 1508673210, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n对'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405826, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_XUBGTcMx|v1_YZA6XhFl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3603715950952415114, 'MsgSeq': 871430101}
2025-08-05 22:56:55 | INFO | 收到文本消息: 消息ID:1508673210 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:对
2025-08-05 22:56:55 | DEBUG | [DouBaoImageToImage] 收到文本消息: '对' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 22:56:55 | DEBUG | [DouBaoImageToImage] 命令解析: ['对']
2025-08-05 22:56:55 | DEBUG | 处理消息内容: '对'
2025-08-05 22:56:55 | DEBUG | 消息内容 '对' 不匹配任何命令，忽略
2025-08-05 22:57:02 | DEBUG | 收到消息: {'MsgId': 214785490, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n没实力的前任让他滚'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405833, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_W/4ux48G|v1_UwhBLzqk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5074797760157595024, 'MsgSeq': 871430102}
2025-08-05 22:57:02 | INFO | 收到文本消息: 消息ID:214785490 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:没实力的前任让他滚
2025-08-05 22:57:02 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没实力的前任让他滚' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 22:57:02 | DEBUG | [DouBaoImageToImage] 命令解析: ['没实力的前任让他滚']
2025-08-05 22:57:02 | DEBUG | 处理消息内容: '没实力的前任让他滚'
2025-08-05 22:57:02 | DEBUG | 消息内容 '没实力的前任让他滚' 不匹配任何命令，忽略
2025-08-05 22:57:11 | DEBUG | 收到消息: {'MsgId': 439487315, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n✓'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405842, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_pS3p+JMR|v1_qQsvP0+C</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6524602127961293340, 'MsgSeq': 871430103}
2025-08-05 22:57:11 | INFO | 收到文本消息: 消息ID:439487315 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:✓
2025-08-05 22:57:11 | DEBUG | [DouBaoImageToImage] 收到文本消息: '✓' from tianen532965049 in 27852221909@chatroom
2025-08-05 22:57:11 | DEBUG | [DouBaoImageToImage] 命令解析: ['✓']
2025-08-05 22:57:11 | DEBUG | 处理消息内容: '✓'
2025-08-05 22:57:11 | DEBUG | 消息内容 '✓' 不匹配任何命令，忽略
2025-08-05 22:57:39 | DEBUG | 收到消息: {'MsgId': 978935646, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n8万姐有了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405871, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_PsjIaUXi|v1_5NETWP+u</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3261440472835131274, 'MsgSeq': 871430104}
2025-08-05 22:57:39 | INFO | 收到文本消息: 消息ID:978935646 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:8万姐有了
2025-08-05 22:57:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '8万姐有了' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 22:57:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['8万姐有了']
2025-08-05 22:57:39 | DEBUG | 处理消息内容: '8万姐有了'
2025-08-05 22:57:39 | DEBUG | 消息内容 '8万姐有了' 不匹配任何命令，忽略
2025-08-05 22:57:45 | DEBUG | 收到消息: {'MsgId': 847079559, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>请问我的你可以包了吗？爬位</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8008704128405253562</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>tianen532965049</chatusr>\n\t\t\t<displayname>饿飞</displayname>\n\t\t\t<content>表现好爬位包了</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;777843361&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_0JZYEFLH|v1_aAFI6VVy&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754405810</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_c3jkq1ylevnb12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405877, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>0fad3f446d289b80be968eaa7ab61621_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_wcJcAehv|v1_E7wVmhOW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1625960961007922722, 'MsgSeq': 871430105}
2025-08-05 22:57:45 | DEBUG | 从群聊消息中提取发送者: wxid_c3jkq1ylevnb12
2025-08-05 22:57:45 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:57:45 | INFO | 收到引用消息: 消息ID:847079559 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 内容:请问我的你可以包了吗？爬位 引用类型:1
2025-08-05 22:57:45 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:57:45 | INFO | [DouBaoImageToImage] 消息内容: '请问我的你可以包了吗？爬位' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 22:57:45 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['请问我的你可以包了吗？爬位']
2025-08-05 22:57:45 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:57:45 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:57:45 | INFO |   - 消息内容: 请问我的你可以包了吗？爬位
2025-08-05 22:57:45 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 22:57:45 | INFO |   - 发送人: wxid_c3jkq1ylevnb12
2025-08-05 22:57:45 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '表现好爬位包了', 'Msgid': '8008704128405253562', 'NewMsgId': '8008704128405253562', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '饿飞', 'MsgSource': '<msgsource><sequence_id>777843361</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_0JZYEFLH|v1_aAFI6VVy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754405810', 'SenderWxid': 'wxid_c3jkq1ylevnb12'}
2025-08-05 22:57:46 | INFO |   - 引用消息ID: 
2025-08-05 22:57:46 | INFO |   - 引用消息类型: 
2025-08-05 22:57:46 | INFO |   - 引用消息内容: 表现好爬位包了
2025-08-05 22:57:46 | INFO |   - 引用消息发送人: wxid_c3jkq1ylevnb12
2025-08-05 22:57:50 | DEBUG | 收到消息: {'MsgId': 1988631920, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n希望来一个8万哥'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405882, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_gHH1UCo+|v1_0OXMHEf/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7463957369955481781, 'MsgSeq': 871430106}
2025-08-05 22:57:50 | INFO | 收到文本消息: 消息ID:1988631920 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:希望来一个8万哥
2025-08-05 22:57:50 | DEBUG | [DouBaoImageToImage] 收到文本消息: '希望来一个8万哥' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 22:57:50 | DEBUG | [DouBaoImageToImage] 命令解析: ['希望来一个8万哥']
2025-08-05 22:57:50 | DEBUG | 处理消息内容: '希望来一个8万哥'
2025-08-05 22:57:50 | DEBUG | 消息内容 '希望来一个8万哥' 不匹配任何命令，忽略
2025-08-05 22:58:26 | DEBUG | 收到消息: {'MsgId': 1243236619, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n希望再来一个喜欢打排位的八万姐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405917, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VRBQ31wm|v1_5W8IR5pw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2753818515548193553, 'MsgSeq': 871430107}
2025-08-05 22:58:26 | INFO | 收到文本消息: 消息ID:1243236619 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:希望再来一个喜欢打排位的八万姐
2025-08-05 22:58:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '希望再来一个喜欢打排位的八万姐' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 22:58:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['希望再来一个喜欢打排位的八万姐']
2025-08-05 22:58:26 | DEBUG | 处理消息内容: '希望再来一个喜欢打排位的八万姐'
2025-08-05 22:58:26 | DEBUG | 消息内容 '希望再来一个喜欢打排位的八万姐' 不匹配任何命令，忽略
2025-08-05 22:58:28 | DEBUG | 收到消息: {'MsgId': 1742179769, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n现在差距10w'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405918, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_m/9AJChV|v1_T8RdK07A</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1064455938622157044, 'MsgSeq': 871430108}
2025-08-05 22:58:28 | INFO | 收到文本消息: 消息ID:1742179769 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:现在差距10w
2025-08-05 22:58:28 | DEBUG | [DouBaoImageToImage] 收到文本消息: '现在差距10w' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 22:58:28 | DEBUG | [DouBaoImageToImage] 命令解析: ['现在差距10w']
2025-08-05 22:58:28 | DEBUG | 处理消息内容: '现在差距10w'
2025-08-05 22:58:28 | DEBUG | 消息内容 '现在差距10w' 不匹配任何命令，忽略
2025-08-05 22:58:54 | DEBUG | 收到消息: {'MsgId': 2045478076, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n追得很紧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405946, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_fm4P0pfw|v1_4VL9g8CC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 156118713646396745, 'MsgSeq': 871430109}
2025-08-05 22:58:54 | INFO | 收到文本消息: 消息ID:2045478076 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:追得很紧
2025-08-05 22:58:54 | DEBUG | [DouBaoImageToImage] 收到文本消息: '追得很紧' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 22:58:54 | DEBUG | [DouBaoImageToImage] 命令解析: ['追得很紧']
2025-08-05 22:58:54 | DEBUG | 处理消息内容: '追得很紧'
2025-08-05 22:58:54 | DEBUG | 消息内容 '追得很紧' 不匹配任何命令，忽略
2025-08-05 22:58:56 | DEBUG | 收到消息: {'MsgId': 525933100, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_g173eyu8nbm522:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>是星团与我们团的差距吗</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1064455938622157044</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_xfxd40diz3bd22</chatusr>\n\t\t\t<displayname>初见</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_OmFohl8t|v1_EuszmfqD&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n现在差距10w</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754405918</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_g173eyu8nbm522</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405947, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>a9be128e519b6ffa6625d2c3595dcd47_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_JG8rAZ7u|v1_NULq71ry</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3425572950548463392, 'MsgSeq': 871430110}
2025-08-05 22:58:56 | DEBUG | 从群聊消息中提取发送者: wxid_g173eyu8nbm522
2025-08-05 22:58:56 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 22:58:56 | INFO | 收到引用消息: 消息ID:525933100 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 内容:是星团与我们团的差距吗 引用类型:1
2025-08-05 22:58:56 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 22:58:56 | INFO | [DouBaoImageToImage] 消息内容: '是星团与我们团的差距吗' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 22:58:56 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['是星团与我们团的差距吗']
2025-08-05 22:58:56 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 22:58:56 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 22:58:56 | INFO |   - 消息内容: 是星团与我们团的差距吗
2025-08-05 22:58:56 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 22:58:56 | INFO |   - 发送人: wxid_g173eyu8nbm522
2025-08-05 22:58:56 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n现在差距10w', 'Msgid': '1064455938622157044', 'NewMsgId': '1064455938622157044', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '初见', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_OmFohl8t|v1_EuszmfqD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754405918', 'SenderWxid': 'wxid_g173eyu8nbm522'}
2025-08-05 22:58:56 | INFO |   - 引用消息ID: 
2025-08-05 22:58:56 | INFO |   - 引用消息类型: 
2025-08-05 22:58:56 | INFO |   - 引用消息内容: 
现在差距10w
2025-08-05 22:58:56 | INFO |   - 引用消息发送人: wxid_g173eyu8nbm522
2025-08-05 22:59:00 | DEBUG | 收到消息: {'MsgId': 969492815, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n对'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405952, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_h60jvYrz|v1_Q9oy6Dls</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 195979747913065086, 'MsgSeq': 871430111}
2025-08-05 22:59:00 | INFO | 收到文本消息: 消息ID:969492815 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:对
2025-08-05 22:59:00 | DEBUG | [DouBaoImageToImage] 收到文本消息: '对' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 22:59:00 | DEBUG | [DouBaoImageToImage] 命令解析: ['对']
2025-08-05 22:59:00 | DEBUG | 处理消息内容: '对'
2025-08-05 22:59:00 | DEBUG | 消息内容 '对' 不匹配任何命令，忽略
2025-08-05 22:59:04 | DEBUG | 收到消息: {'MsgId': 1441315192, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n没关系'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405955, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_R4HTPjTn|v1_mWztpzgb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5342709145211953217, 'MsgSeq': 871430112}
2025-08-05 22:59:04 | INFO | 收到文本消息: 消息ID:1441315192 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:没关系
2025-08-05 22:59:04 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没关系' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 22:59:04 | DEBUG | [DouBaoImageToImage] 命令解析: ['没关系']
2025-08-05 22:59:04 | DEBUG | 处理消息内容: '没关系'
2025-08-05 22:59:04 | DEBUG | 消息内容 '没关系' 不匹配任何命令，忽略
2025-08-05 22:59:11 | DEBUG | 收到消息: {'MsgId': 905934944, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n他们喜欢做老二'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405962, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_o1Q6b+FU|v1_qF0ibhyY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8805797236897214767, 'MsgSeq': 871430113}
2025-08-05 22:59:11 | INFO | 收到文本消息: 消息ID:905934944 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:他们喜欢做老二
2025-08-05 22:59:11 | DEBUG | [DouBaoImageToImage] 收到文本消息: '他们喜欢做老二' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 22:59:11 | DEBUG | [DouBaoImageToImage] 命令解析: ['他们喜欢做老二']
2025-08-05 22:59:11 | DEBUG | 处理消息内容: '他们喜欢做老二'
2025-08-05 22:59:11 | DEBUG | 消息内容 '他们喜欢做老二' 不匹配任何命令，忽略
2025-08-05 22:59:15 | DEBUG | 收到消息: {'MsgId': 160791911, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n有能力消费的赶紧消费起来'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405967, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_TW3fuQuG|v1_RdNqEAX0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8095195045536837146, 'MsgSeq': 871430114}
2025-08-05 22:59:15 | INFO | 收到文本消息: 消息ID:160791911 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:有能力消费的赶紧消费起来
2025-08-05 22:59:15 | DEBUG | [DouBaoImageToImage] 收到文本消息: '有能力消费的赶紧消费起来' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 22:59:15 | DEBUG | [DouBaoImageToImage] 命令解析: ['有能力消费的赶紧消费起来']
2025-08-05 22:59:15 | DEBUG | 处理消息内容: '有能力消费的赶紧消费起来'
2025-08-05 22:59:15 | DEBUG | 消息内容 '有能力消费的赶紧消费起来' 不匹配任何命令，忽略
2025-08-05 22:59:17 | DEBUG | 收到消息: {'MsgId': 592497275, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_g173eyu8nbm522:\n那确实追的紧啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405967, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VPUqsOEW|v1_tVsplnLf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7679682537752478069, 'MsgSeq': 871430115}
2025-08-05 22:59:17 | INFO | 收到文本消息: 消息ID:592497275 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 @:[] 内容:那确实追的紧啊
2025-08-05 22:59:17 | DEBUG | [DouBaoImageToImage] 收到文本消息: '那确实追的紧啊' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 22:59:17 | DEBUG | [DouBaoImageToImage] 命令解析: ['那确实追的紧啊']
2025-08-05 22:59:17 | DEBUG | 处理消息内容: '那确实追的紧啊'
2025-08-05 22:59:17 | DEBUG | 消息内容 '那确实追的紧啊' 不匹配任何命令，忽略
2025-08-05 22:59:31 | DEBUG | 收到消息: {'MsgId': 890186959, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_g173eyu8nbm522:\n加油加油'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405983, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_gzTkTEMN|v1_dsfPhsNA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2750557681059886975, 'MsgSeq': 871430116}
2025-08-05 22:59:31 | INFO | 收到文本消息: 消息ID:890186959 来自:27852221909@chatroom 发送人:wxid_g173eyu8nbm522 @:[] 内容:加油加油
2025-08-05 22:59:31 | DEBUG | [DouBaoImageToImage] 收到文本消息: '加油加油' from wxid_g173eyu8nbm522 in 27852221909@chatroom
2025-08-05 22:59:31 | DEBUG | [DouBaoImageToImage] 命令解析: ['加油加油']
2025-08-05 22:59:31 | DEBUG | 处理消息内容: '加油加油'
2025-08-05 22:59:31 | DEBUG | 消息内容 '加油加油' 不匹配任何命令，忽略
2025-08-05 22:59:33 | DEBUG | 收到消息: {'MsgId': 767823350, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n钻也不能带到现实生活中花'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405983, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_t9oFOlRm|v1_JGj6S6rx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9051720666881073433, 'MsgSeq': 871430117}
2025-08-05 22:59:33 | INFO | 收到文本消息: 消息ID:767823350 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:钻也不能带到现实生活中花
2025-08-05 22:59:33 | DEBUG | [DouBaoImageToImage] 收到文本消息: '钻也不能带到现实生活中花' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 22:59:33 | DEBUG | [DouBaoImageToImage] 命令解析: ['钻也不能带到现实生活中花']
2025-08-05 22:59:33 | DEBUG | 处理消息内容: '钻也不能带到现实生活中花'
2025-08-05 22:59:33 | DEBUG | 消息内容 '钻也不能带到现实生活中花' 不匹配任何命令，忽略
2025-08-05 22:59:35 | DEBUG | 收到消息: {'MsgId': 1054095056, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n昨晚差距也挺大，一下子就追上来了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405984, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_liU9lTsU|v1_3l0iGGfY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2572478290197071547, 'MsgSeq': 871430118}
2025-08-05 22:59:35 | INFO | 收到文本消息: 消息ID:1054095056 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:昨晚差距也挺大，一下子就追上来了
2025-08-05 22:59:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '昨晚差距也挺大，一下子就追上来了' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 22:59:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['昨晚差距也挺大，一下子就追上来了']
2025-08-05 22:59:35 | DEBUG | 处理消息内容: '昨晚差距也挺大，一下子就追上来了'
2025-08-05 22:59:35 | DEBUG | 消息内容 '昨晚差距也挺大，一下子就追上来了' 不匹配任何命令，忽略
2025-08-05 22:59:41 | DEBUG | 收到消息: {'MsgId': 1633167715, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n主要是现在没有新活动'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405993, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_X/a6BF7e|v1_jxNfxqmK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2657276691505201961, 'MsgSeq': 871430119}
2025-08-05 22:59:41 | INFO | 收到文本消息: 消息ID:1633167715 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:主要是现在没有新活动
2025-08-05 22:59:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '主要是现在没有新活动' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 22:59:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['主要是现在没有新活动']
2025-08-05 22:59:41 | DEBUG | 处理消息内容: '主要是现在没有新活动'
2025-08-05 22:59:41 | DEBUG | 消息内容 '主要是现在没有新活动' 不匹配任何命令，忽略
2025-08-05 22:59:46 | DEBUG | 收到消息: {'MsgId': 2120893736, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n钻只能买霸屏了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754405998, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_7ReviAwr|v1_H5pDUg7O</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6347424197319631618, 'MsgSeq': 871430120}
2025-08-05 22:59:46 | INFO | 收到文本消息: 消息ID:2120893736 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:钻只能买霸屏了
2025-08-05 22:59:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '钻只能买霸屏了' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 22:59:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['钻只能买霸屏了']
2025-08-05 22:59:46 | DEBUG | 处理消息内容: '钻只能买霸屏了'
2025-08-05 22:59:46 | DEBUG | 消息内容 '钻只能买霸屏了' 不匹配任何命令，忽略
2025-08-05 22:59:56 | DEBUG | 收到消息: {'MsgId': 1995426102, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n更想留给新活动使'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406008, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_whpApqtj|v1_UJzMkMB+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7202945863676204380, 'MsgSeq': 871430121}
2025-08-05 22:59:56 | INFO | 收到文本消息: 消息ID:1995426102 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:更想留给新活动使
2025-08-05 22:59:56 | DEBUG | [DouBaoImageToImage] 收到文本消息: '更想留给新活动使' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 22:59:56 | DEBUG | [DouBaoImageToImage] 命令解析: ['更想留给新活动使']
2025-08-05 22:59:56 | DEBUG | 处理消息内容: '更想留给新活动使'
2025-08-05 22:59:56 | DEBUG | 消息内容 '更想留给新活动使' 不匹配任何命令，忽略
2025-08-05 23:00:01 | DEBUG | 收到消息: {'MsgId': 997548308, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n可以买戒指'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406013, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VtQFhhIP|v1_r4pDi7ot</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3990554112778123851, 'MsgSeq': 871430122}
2025-08-05 23:00:01 | INFO | 收到文本消息: 消息ID:997548308 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:可以买戒指
2025-08-05 23:00:01 | DEBUG | [DouBaoImageToImage] 收到文本消息: '可以买戒指' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:00:01 | DEBUG | [DouBaoImageToImage] 命令解析: ['可以买戒指']
2025-08-05 23:00:01 | DEBUG | 处理消息内容: '可以买戒指'
2025-08-05 23:00:01 | DEBUG | 消息内容 '可以买戒指' 不匹配任何命令，忽略
2025-08-05 23:00:08 | DEBUG | 收到消息: {'MsgId': 125957252, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n商城戒指 衣服'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406020, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_P1a8Zgxt|v1_QzXth8et</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5949154587825132739, 'MsgSeq': 871430123}
2025-08-05 23:00:08 | INFO | 收到文本消息: 消息ID:125957252 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:商城戒指 衣服
2025-08-05 23:00:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '商城戒指 衣服' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:00:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['商城戒指', '衣服']
2025-08-05 23:00:08 | DEBUG | 处理消息内容: '商城戒指 衣服'
2025-08-05 23:00:08 | DEBUG | 消息内容 '商城戒指 衣服' 不匹配任何命令，忽略
2025-08-05 23:00:12 | DEBUG | 收到消息: {'MsgId': 1389791086, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n可以买坐骑'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406024, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_m6Rj8n4Z|v1_taXOFQRZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4905220786771282215, 'MsgSeq': *********}
2025-08-05 23:00:12 | INFO | 收到文本消息: 消息ID:1389791086 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:可以买坐骑
2025-08-05 23:00:12 | DEBUG | [DouBaoImageToImage] 收到文本消息: '可以买坐骑' from wxid_x4s6k999g6qg22 in 27852221909@chatroom
2025-08-05 23:00:12 | DEBUG | [DouBaoImageToImage] 命令解析: ['可以买坐骑']
2025-08-05 23:00:12 | DEBUG | 处理消息内容: '可以买坐骑'
2025-08-05 23:00:12 | DEBUG | 消息内容 '可以买坐骑' 不匹配任何命令，忽略
2025-08-05 23:00:23 | DEBUG | 收到消息: {'MsgId': 2119409607, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c3jkq1ylevnb12:\n[捂脸]有砖石还怕花不出去吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406034, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_PLSDdbv3|v1_HlJ6YyFJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3700834414654919569, 'MsgSeq': 871430125}
2025-08-05 23:00:23 | INFO | 收到文本消息: 消息ID:2119409607 来自:27852221909@chatroom 发送人:wxid_c3jkq1ylevnb12 @:[] 内容:[捂脸]有砖石还怕花不出去吗
2025-08-05 23:00:23 | DEBUG | [DouBaoImageToImage] 收到文本消息: '[捂脸]有砖石还怕花不出去吗' from wxid_c3jkq1ylevnb12 in 27852221909@chatroom
2025-08-05 23:00:23 | DEBUG | [DouBaoImageToImage] 命令解析: ['[捂脸]有砖石还怕花不出去吗']
2025-08-05 23:00:23 | DEBUG | 处理消息内容: '[捂脸]有砖石还怕花不出去吗'
2025-08-05 23:00:23 | DEBUG | 消息内容 '[捂脸]有砖石还怕花不出去吗' 不匹配任何命令，忽略
