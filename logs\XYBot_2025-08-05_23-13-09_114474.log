2025-08-05 23:13:10 | SUCCESS | 读取主设置成功
2025-08-05 23:13:10 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 23:13:10 | INFO | 2025/08/05 23:13:10 GetRedisAddr: 127.0.0.1:6379
2025-08-05 23:13:10 | INFO | 2025/08/05 23:13:10 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 23:13:10 | INFO | 2025/08/05 23:13:10 Server start at :9000
2025-08-05 23:13:10 | SUCCESS | WechatAPI服务已启动
2025-08-05 23:13:11 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 23:13:11 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 23:13:11 | SUCCESS | 登录成功
2025-08-05 23:13:11 | SUCCESS | 已开启自动心跳
2025-08-05 23:13:11 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:13:11 | SUCCESS | 数据库初始化成功
2025-08-05 23:13:11 | SUCCESS | 定时任务已启动
2025-08-05 23:13:11 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 23:13:11 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:13:12 | INFO | 播客API初始化成功
2025-08-05 23:13:12 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:13:12 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:13:12 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 23:13:12 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 23:13:12 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 23:13:12 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 23:13:12 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 23:13:12 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 23:13:12 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 23:13:13 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 23:13:13 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 23:13:13 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 23:13:13 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 23:13:13 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 23:13:13 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 23:13:13 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 23:13:13 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 23:13:13 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 23:13:13 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 23:13:13 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 23:13:13 | DEBUG |   - 启用状态: True
2025-08-05 23:13:13 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 23:13:13 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 23:13:13 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 23:13:13 | DEBUG |   - Cookies配置: 已配置
2025-08-05 23:13:13 | DEBUG |   - 限制机制: 已禁用
2025-08-05 23:13:13 | DEBUG |   - 数字选择超时: 120秒
2025-08-05 23:13:13 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 23:13:13 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-05 23:13:13 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 23:13:13 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 23:13:13 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 23:13:13 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 23:13:13 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 23:13:13 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:13:13 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 23:13:13 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 23:13:13 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 23:13:13 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 23:13:13 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 23:13:13 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 23:13:13 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 23:13:13 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 23:13:14 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-05 23:13:14 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 23:13:14 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 23:13:15 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 23:13:15 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:13:15 | INFO | [yuanbao] 插件初始化完成
2025-08-05 23:13:15 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 23:13:15 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 23:13:15 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 23:13:15 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 23:13:15 | INFO | 处理堆积消息中
2025-08-05 23:13:15 | SUCCESS | 处理堆积消息完毕
2025-08-05 23:13:15 | SUCCESS | 开始处理消息
2025-08-05 23:13:25 | DEBUG | 收到消息: {'MsgId': 551086277, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1843" length="2651" bufid="0" aeskey="cc37bc2f497268eaba991d409d758705" voiceurl="3052020100044b30490201000204a95c809d02032df927020478089324020468921fa1042462653530336632372d336138332d346230332d613766342d38626238643936356161396602040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600352313080525e376c3101f6103" fromusername="wxid_ubbh6q832tcs21" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406817, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_hIGesYzk|v1_khgekoor</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段语音', 'NewMsgId': 7778320769424520699, 'MsgSeq': 871430199}
2025-08-05 23:13:25 | INFO | 收到语音消息: 消息ID:551086277 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1843" length="2651" bufid="0" aeskey="cc37bc2f497268eaba991d409d758705" voiceurl="3052020100044b30490201000204a95c809d02032df927020478089324020468921fa1042462653530336632372d336138332d346230332d613766342d38626238643936356161396602040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600352313080525e376c3101f6103" fromusername="wxid_ubbh6q832tcs21" /></msg>
2025-08-05 23:13:26 | DEBUG | [VoiceTest] 缓存语音 MsgId: 551086277
2025-08-05 23:13:26 | DEBUG | [VoiceTest] 缓存语音 NewMsgId: 7778320769424520699
2025-08-05 23:13:26 | INFO | [VoiceTest] 已缓存语音消息: MsgId=551086277, NewMsgId=7778320769424520699
2025-08-05 23:13:35 | DEBUG | 收到消息: {'MsgId': 1726288789, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>7778320769424520699</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:1843:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754406817</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406827, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>50ebdc32702fcada698af8c857ce6c70_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_X3gLe5bb|v1_QGo1jZrW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': 6179345298553873946, 'MsgSeq': 871430200}
2025-08-05 23:13:35 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 23:13:35 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:13:35 | INFO | 收到引用消息: 消息ID:1726288789 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 23:13:36 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:13:36 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 23:13:36 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 23:13:36 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:13:36 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:13:36 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:13:36 | INFO |   - 消息内容: 跟着唱
2025-08-05 23:13:36 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 23:13:36 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 23:13:36 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:1843:0\n', 'Msgid': '7778320769424520699', 'NewMsgId': '7778320769424520699', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754406817', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 23:13:36 | INFO |   - 引用消息ID: 
2025-08-05 23:13:36 | INFO |   - 引用消息类型: 
2025-08-05 23:13:36 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:1843:0

2025-08-05 23:13:36 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 23:13:36 | ERROR | [VoiceTest] 处理引用语音消息异常: Extra data: line 1 column 69 (char 68)
2025-08-05 23:13:36 | ERROR | [VoiceTest] 异常堆栈: Traceback (most recent call last):
  File "C:\XYBotV2\plugins\VoiceTest\main.py", line 360, in handle_quote
    silk_base64 = await bot.download_voice(original_msg_id, voiceurl, length)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\WechatAPI\Client\tool.py", line 69, in download_voice
    json_resp = await response.json()
                ^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\aiohttp\client_reqrep.py", line 1298, in json
    return loads(stripped.decode(encoding))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\json\decoder.py", line 340, in decode
    raise JSONDecodeError("Extra data", s, end)
json.decoder.JSONDecodeError: Extra data: line 1 column 69 (char 68)

2025-08-05 23:13:37 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 处理引用语音时出错
2025-08-05 23:13:37 | DEBUG | 收到消息: {'MsgId': 897278753, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n逆天'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406829, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_JUxg4Liu|v1_V0qhM3YR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 逆天', 'NewMsgId': 384427885646707793, 'MsgSeq': 871430203}
2025-08-05 23:13:37 | INFO | 收到文本消息: 消息ID:897278753 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:逆天
2025-08-05 23:13:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '逆天' from wxid_l9koi6kli78i22 in 48097389945@chatroom
2025-08-05 23:13:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['逆天']
2025-08-05 23:13:38 | DEBUG | 处理消息内容: '逆天'
2025-08-05 23:13:38 | DEBUG | 消息内容 '逆天' 不匹配任何命令，忽略
2025-08-05 23:13:44 | DEBUG | 收到消息: {'MsgId': 448723775, 'FromUserName': {'string': 'gh_73626e201810'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': '<msg>\n    <appmsg appid="" sdkver="0">\n        <title><![CDATA[速度！又一个网盘被破了！]]></title>\n        <des><![CDATA[]]></des>\n        <action></action>\n        <type>5</type>\n        <showtype>1</showtype>\n        <content><![CDATA[]]></content>\n        <contentattr>1</contentattr>\n        <url><![CDATA[http://mp.weixin.qq.com/s?__biz=Mzg3NzU5NDUwOA==&mid=2247504678&idx=1&sn=7604bcafee293b8c123dcf10cd430aae&chksm=ce97ee284e297d7e1f138ec66a348927c5664bb91c251d5777b17544b81a5865b9634014448e&scene=0&xtrack=1#rd]]></url>\n        <lowurl><![CDATA[]]></lowurl>\n        <appattach>\n            <totallen>0</totallen>\n            <attachid></attachid>\n            <fileext></fileext>\n        </appattach>\n        <extinfo></extinfo>\n        <mmreader>\n            <category type="20" count="1">\n                <name><![CDATA[插眼情报S]]></name>\n                <topnew>\n                    <cover><![CDATA[https://mmbiz.qpic.cn/sz_mmbiz_jpg/IaXO6rETnquXZJHYQrZ5NpA3GQPVZHxFnN1Nk802EpN2AyNhxW7Ssvaf04d9RYXe3dMBcblJmhQnkia1fWaRc2w/640?wxtype=jpeg&wxfrom=0]]></cover>\n                    <width>0</width>\n                    <height>0</height>\n                    <digest><![CDATA[]]></digest>\n                </topnew>\n                \n                <item>\n                    <has_redpacket_cover>0</has_redpacket_cover>\n                    <text_title><![CDATA[]]></text_title>\n                    <itemshowtype>0</itemshowtype>\n                    <title><![CDATA[速度！又一个网盘被破了！]]></title>\n                    <title_v2><![CDATA[速度！又一个网盘被破了！]]></title_v2>\n                    <url><![CDATA[http://mp.weixin.qq.com/s?__biz=Mzg3NzU5NDUwOA==&mid=2247504678&idx=1&sn=7604bcafee293b8c123dcf10cd430aae&chksm=ce720cf30b9952a5c0a88f8ee18506bfff8e94a561c40c865f023ff800b27a45c727bf67208f&scene=0&xtrack=1#rd]]></url>\n                    <shorturl><![CDATA[]]></shorturl>\n                    <longurl><![CDATA[]]></longurl>\n                    <pub_time>1754406772</pub_time>\n                    <summary><![CDATA[点击蓝字 关注我们由于公众号改版，推送规则大幅改变。希望小伙伴们在主页右上角设为星标，这样就可以第一时间收到我的推送了，先提前感谢大家的支持啦~PREFACE上期回顾：海量资源，一键安装最近好多人开始]]></summary>\n                    <cover><![CDATA[https://mmbiz.qpic.cn/sz_mmbiz_jpg/IaXO6rETnquXZJHYQrZ5NpA3GQPVZHxFnN1Nk802EpN2AyNhxW7Ssvaf04d9RYXe3dMBcblJmhQnkia1fWaRc2w/640?wxtype=jpeg&wxfrom=0]]></cover>\n                    <tweetid></tweetid>\n                    <digest><![CDATA[]]></digest>\n                    <fileid>100021026</fileid>\n                    <sources>\n                        <source>\n                            <name><![CDATA[插眼情报S]]></name>\n                        </source>\n                    </sources>\n                    <styles></styles>\n                    <native_url></native_url>\n                    <del_flag>0</del_flag>\n                    <contentattr>1</contentattr>\n                    <play_length>0</play_length>\n                    <play_url><![CDATA[]]></play_url>\n                    <voice_id><![CDATA[]]></voice_id>\n                    <tid><![CDATA[]]></tid>\n                    <nonce_id><![CDATA[]]></nonce_id>\n                    <voice_type>0</voice_type>\n<player><![CDATA[]]></player>\n                     \n                    \n                    <music_source>0</music_source>\n                    <pic_num>0</pic_num>\n                    <vid></vid>\n                    <author><![CDATA[]]></author>\n                    <recommendation><![CDATA[]]></recommendation>\n                    <pic_urls></pic_urls>\n                    <multi_picture_cover></multi_picture_cover>\n                    <comment_topic_id>4107305775909748738</comment_topic_id>\n                    <cover_235_1><![CDATA[https://mmbiz.qpic.cn/sz_mmbiz_jpg/IaXO6rETnquXZJHYQrZ5NpA3GQPVZHxFnN1Nk802EpN2AyNhxW7Ssvaf04d9RYXe3dMBcblJmhQnkia1fWaRc2w/640?wxtype=jpeg&wxfrom=0]]></cover_235_1>\n                    <cover_1_1><![CDATA[https://mmbiz.qpic.cn/sz_mmbiz_jpg/IaXO6rETnquXZJHYQrZ5NpA3GQPVZHxFXZvd9GBYLzBf6l8WhiancSZpt14l0q4R3UhxwrbW1eYxDKiaic6mVbIvA/300?wxtype=jpeg&wxfrom=0]]></cover_1_1>\n                    <cover_16_9><![CDATA[]]></cover_16_9>\n                    <appmsg_like_type>2</appmsg_like_type>\n                    <video_width>0</video_width>\n                    <video_height>0</video_height>\n                    <is_pay_subscribe>0</is_pay_subscribe>\n\t\t\t<finder_feed>\n\t\t\t    <object_id><![CDATA[]]></object_id>\n\t\t\t    <object_nonce_id><![CDATA[]]></object_nonce_id>\n\t\t\t    <feed_type>0</feed_type>\n\t\t\t    <nickname><![CDATA[]]></nickname>\n\t\t\t    <avatar><![CDATA[]]></avatar>\n\t\t\t    <desc><![CDATA[]]></desc>\n\t\t\t    <media_count>0</media_count>\n\t\t\t    <media_list>\n\t\t\t\t\n\t\t\t    </media_list>\n\t\t\t    <mega_video>\n\t\t\t\t<object_id><![CDATA[]]></object_id>\n\t\t\t\t<object_nonce_id><![CDATA[]]></object_nonce_id>\n\t\t\t    </mega_video>\n\t\t\t</finder_feed>\n                        <finder_live>\n                            <finder_username><![CDATA[]]></finder_username>\n                            <category><![CDATA[]]></category>\n                            <finder_nonce_id><![CDATA[]]></finder_nonce_id>\n                            <export_id><![CDATA[]]></export_id>\n                            <nickname><![CDATA[]]></nickname>\n                            <head_url><![CDATA[]]></head_url>\n                            <desc><![CDATA[]]></desc>\n                            <live_status></live_status>\n                            <live_source_type_str><![CDATA[]]></live_source_type_str>\n                            <ext_flag></ext_flag>\n                            <auth_icon_url><![CDATA[]]></auth_icon_url>\n                            <auth_icon_type_str><![CDATA[]]></auth_icon_type_str>\n                            <media>\n                                <cover_url><![CDATA[]]></cover_url>\n                                <height></height>\n                                <width></width>\n                            </media>\n                        </finder_live>\n                    <product_activity>\n                        <activity_type></activity_type>\n                        <activity_tag><![CDATA[]]></activity_tag>\n                        <activity_content><![CDATA[]]></activity_content>\n                    </product_activity>\n                    <has_redpacket_cover_v2>0</has_redpacket_cover_v2>\n                    <has_gift_activity>0</has_gift_activity>\n                    <mall_activity>\n                        <mall_id></mall_id>\n                        <mall_version></mall_version>\n                        <ecs_jump_info_base64><![CDATA[]]></ecs_jump_info_base64>\n                        <mall_tag><![CDATA[]]></mall_tag>\n                        <mall_product_cnt></mall_product_cnt>\n                    </mall_activity>\n                </item>\n                \n            </category>\n            <publisher>\n                <username><![CDATA[gh_73626e201810]]></username>\n                <nickname><![CDATA[插眼情报S]]></nickname>\n            </publisher>\n            <template_header></template_header>\n            <template_detail></template_detail>\n            <forbid_forward>0</forbid_forward>\n        </mmreader>\n        <thumburl><![CDATA[https://mmbiz.qpic.cn/sz_mmbiz_jpg/IaXO6rETnquXZJHYQrZ5NpA3GQPVZHxFnN1Nk802EpN2AyNhxW7Ssvaf04d9RYXe3dMBcblJmhQnkia1fWaRc2w/640?wxtype=jpeg&wxfrom=0]]></thumburl>\n    </appmsg>\n    <fromusername><![CDATA[gh_73626e201810]]></fromusername>\n    <appinfo>\n        <version></version>\n        <appname><![CDATA[插眼情报S]]></appname>\n        <isforceupdate>1</isforceupdate>\n    </appinfo>\n    \n    \n    \n    \n    \n    \n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406835, 'MsgSource': '<msgsource>\n\t<bizmsg>\n\t\t<bizclientmsgid><![CDATA[mmbizcluster_1_3877594508_1000000615]]></bizclientmsgid>\n\t\t<msg_predict>200</msg_predict>\n\t</bizmsg>\n\t<bizflag>16</bizflag>\n\t<msg_cluster_type>3</msg_cluster_type>\n\t<service_type>0</service_type>\n\t<signature>N0_V1_t3D2g3QV|v1_5lbNLcGr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5824649392853191666, 'MsgSeq': 871430204}
2025-08-05 23:13:44 | DEBUG | 直接从公众号接收到文章，公众号ID: gh_73626e201810
2025-08-05 23:13:44 | INFO | 收到公众号文章消息: 消息ID:448723775 来自:gh_73626e201810
2025-08-05 23:13:44 | DEBUG | 使用直接提供的公众号ID: gh_73626e201810
2025-08-05 23:13:44 | DEBUG | 从publisher/nickname获取到公众号名称: 插眼情报S
2025-08-05 23:13:44 | DEBUG | 公众号「插眼情报S」不在监控列表中，跳过处理
2025-08-05 23:14:09 | DEBUG | 收到消息: {'MsgId': 1723902311, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n能的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406860, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_PN/CU+do|v1_lejVHBYm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8079021283945414261, 'MsgSeq': 871430205}
2025-08-05 23:14:09 | INFO | 收到文本消息: 消息ID:1723902311 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:能的
2025-08-05 23:14:09 | DEBUG | [DouBaoImageToImage] 收到文本消息: '能的' from wxid_idzryo4rneok22 in 27852221909@chatroom
2025-08-05 23:14:09 | DEBUG | [DouBaoImageToImage] 命令解析: ['能的']
2025-08-05 23:14:09 | DEBUG | 处理消息内容: '能的'
2025-08-05 23:14:09 | DEBUG | 消息内容 '能的' 不匹配任何命令，忽略
2025-08-05 23:14:13 | DEBUG | 收到消息: {'MsgId': 2022098022, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n煮啵'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406861, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ctOXfueb|v1_8GCSzbLz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2233285195126133807, 'MsgSeq': 871430206}
2025-08-05 23:14:13 | INFO | 收到文本消息: 消息ID:2022098022 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:煮啵
2025-08-05 23:14:13 | DEBUG | [DouBaoImageToImage] 收到文本消息: '煮啵' from wxid_idzryo4rneok22 in 27852221909@chatroom
2025-08-05 23:14:13 | DEBUG | [DouBaoImageToImage] 命令解析: ['煮啵']
2025-08-05 23:14:13 | DEBUG | 处理消息内容: '煮啵'
2025-08-05 23:14:13 | DEBUG | 消息内容 '煮啵' 不匹配任何命令，忽略
2025-08-05 23:14:16 | DEBUG | 收到消息: {'MsgId': 576333541, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n包的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406866, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_w9gj27Dz|v1_xg8Ph01Y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8440607959800067090, 'MsgSeq': 871430207}
2025-08-05 23:14:16 | INFO | 收到文本消息: 消息ID:576333541 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:包的
2025-08-05 23:14:16 | DEBUG | [DouBaoImageToImage] 收到文本消息: '包的' from wxid_idzryo4rneok22 in 27852221909@chatroom
2025-08-05 23:14:16 | DEBUG | [DouBaoImageToImage] 命令解析: ['包的']
2025-08-05 23:14:16 | DEBUG | 处理消息内容: '包的'
2025-08-05 23:14:16 | DEBUG | 消息内容 '包的' 不匹配任何命令，忽略
2025-08-05 23:14:24 | DEBUG | 收到消息: {'MsgId': 463215836, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="27852221909@chatroom" type="1" idbuffer="media:0_0" md5="cfd3cc73fbc39eca8dc39b912da89576" len="26820" productid="" androidmd5="cfd3cc73fbc39eca8dc39b912da89576" androidlen="26820" s60v3md5="cfd3cc73fbc39eca8dc39b912da89576" s60v3len="26820" s60v5md5="cfd3cc73fbc39eca8dc39b912da89576" s60v5len="26820" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=cfd3cc73fbc39eca8dc39b912da89576&amp;filekey=3043020101042f302d02016e0402534804206366643363633733666263333965636138646333396239313264613839353736020268c4040d00000004627466730000000132&amp;hy=SH&amp;storeid=266f56c6d000d220ed8430d640000006e01004fb153482773f031567915c1b&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=4fd920f5b0eb4d8ef97dc4b8e79a5052&amp;filekey=3043020101042f302d02016e0402534804203466643932306635623065623464386566393764633462386537396135303532020268d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=266f56c6d000dff8fd8430d640000006e02004fb253482773f031567915c30&amp;ef=2&amp;bizid=1022" aeskey="33bad4372f904110aea2202300878f73" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=c458d5502735c0806c772116af8b6556&amp;filekey=3043020101042f302d02016e040253480420633435386435353032373335633038303663373732313136616638623635353602023010040d00000004627466730000000132&amp;hy=SH&amp;storeid=266f56c6d000e9a52d8430d640000006e03004fb353482773f031567915c3c&amp;ef=3&amp;bizid=1022" externmd5="6f0eb0f9685e53e75feb35afa19f810b" width="467" height="503" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406875, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_BgZfO4Q8|v1_Wf792Oye</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6047162768036188607, 'MsgSeq': 871430208}
2025-08-05 23:14:24 | INFO | 收到表情消息: 消息ID:463215836 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 MD5:cfd3cc73fbc39eca8dc39b912da89576 大小:26820
2025-08-05 23:14:24 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6047162768036188607
2025-08-05 23:14:33 | DEBUG | 收到消息: {'MsgId': 1260777491, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="f0e1bf07631d7f5bbb226340bdf0141f" encryver="1" cdnthumbaeskey="f0e1bf07631d7f5bbb226340bdf0141f" cdnthumburl="3057020100044b304902010002049363814102032f514902041f312275020468921fe4042439313734383466342d313532362d346632382d396330632d373932363361363862306261020405250a020201000405004c55cd00" cdnthumblength="3249" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902041f312275020468921fe4042439313734383466342d313532362d346632382d396330632d373932363361363862306261020405250a020201000405004c55cd00" length="16761" md5="e6447de886d715a4cb451e7b97d675e8" originsourcemd5="9fdf59f49e056942614d5ec0aebf2eb9">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406884, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>8025a48e45b72530de1f55df98e4580c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_0ASwYZ3o|v1_3gQpM8F5</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 2116591622226490727, 'MsgSeq': 871430209}
2025-08-05 23:14:33 | INFO | 收到图片消息: 消息ID:1260777491 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="f0e1bf07631d7f5bbb226340bdf0141f" encryver="1" cdnthumbaeskey="f0e1bf07631d7f5bbb226340bdf0141f" cdnthumburl="3057020100044b304902010002049363814102032f514902041f312275020468921fe4042439313734383466342d313532362d346632382d396330632d373932363361363862306261020405250a020201000405004c55cd00" cdnthumblength="3249" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902041f312275020468921fe4042439313734383466342d313532362d346632382d396330632d373932363361363862306261020405250a020201000405004c55cd00" length="16761" md5="e6447de886d715a4cb451e7b97d675e8" originsourcemd5="9fdf59f49e056942614d5ec0aebf2eb9"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-05 23:14:34 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-05 23:14:34 | INFO | [TimerTask] 缓存图片消息: 1260777491
2025-08-05 23:14:38 | DEBUG | 收到消息: {'MsgId': 2044632203, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="438849a397f5a1ed0288d8177a856857" len = "87404" productid="" androidmd5="438849a397f5a1ed0288d8177a856857" androidlen="87404" s60v3md5 = "438849a397f5a1ed0288d8177a856857" s60v3len="87404" s60v5md5 = "438849a397f5a1ed0288d8177a856857" s60v5len="87404" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=438849a397f5a1ed0288d8177a856857&amp;filekey=30350201010421301f02020106040253480410438849a397f5a1ed0288d8177a856857020301556c040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630cd542000826d5000000000000010600004f5053480e267b40b78d4ce32&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=27b710f75b7d0b53357e29e138211610&amp;filekey=30350201010421301f020201060402535a041027b710f75b7d0b53357e29e1382116100203015570040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2630cd542000de7e5000000000000010600004f50535a01c278809676a5b8d&amp;bizid=1023" aeskey= "82825e0be1ff1c3fa763f9e6f59bce1b" externurl = "" externmd5 = "" width= "640" height= "172" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406889, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_3W4JzZgX|v1_DVf52Svm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1523308537292107723, 'MsgSeq': 871430210}
2025-08-05 23:14:38 | INFO | 收到表情消息: 消息ID:2044632203 来自:27852221909@chatroom 发送人:tianen532965049 MD5:438849a397f5a1ed0288d8177a856857 大小:87404
2025-08-05 23:14:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1523308537292107723
2025-08-05 23:15:00 | DEBUG | 收到消息: {'MsgId': 1706889379, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="7848d86466b6d5f5076da4e064f3eaf3" encryver="1" cdnthumbaeskey="7848d86466b6d5f5076da4e064f3eaf3" cdnthumburl="3057020100044b30490201000204e68b505d02032f779302041db9206f020468921f7b042465626636386165382d306231332d346235362d626234382d653437663135333234343631020405250a020201000405004c550700" cdnthumblength="4750" cdnthumbheight="110" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204e68b505d02032f779302041db9206f020468921f7b042465626636386165382d306231332d346235362d626234382d653437663135333234343631020405250a020201000405004c550700" length="65492" md5="33c2b50ec29b97e44ca9b91239d09801" originsourcemd5="0c58a357ad54d381899eaf01a0427bf7">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406912, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>b1917dc4e7e677437c53eb06e38ff075_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_8kZ/Ckj/|v1_PHivrM6d</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 1116707018481996434, 'MsgSeq': 871430211}
2025-08-05 23:15:00 | INFO | 收到图片消息: 消息ID:1706889379 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="7848d86466b6d5f5076da4e064f3eaf3" encryver="1" cdnthumbaeskey="7848d86466b6d5f5076da4e064f3eaf3" cdnthumburl="3057020100044b30490201000204e68b505d02032f779302041db9206f020468921f7b042465626636386165382d306231332d346235362d626234382d653437663135333234343631020405250a020201000405004c550700" cdnthumblength="4750" cdnthumbheight="110" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204e68b505d02032f779302041db9206f020468921f7b042465626636386165382d306231332d346235362d626234382d653437663135333234343631020405250a020201000405004c550700" length="65492" md5="33c2b50ec29b97e44ca9b91239d09801" originsourcemd5="0c58a357ad54d381899eaf01a0427bf7"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-05 23:15:00 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-08-05 23:15:00 | INFO | [TimerTask] 缓存图片消息: 1706889379
2025-08-05 23:15:04 | DEBUG | 收到消息: {'MsgId': 517676683, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>彩</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>2116591622226490727</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;5&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;8025a48e45b72530de1f55df98e4580c_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="16761" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;71&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_GBLuWOVQ|v1_+HmbySYz&lt;/signature&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="f0e1bf07631d7f5bbb226340bdf0141f" encryver="1" cdnthumbaeskey="f0e1bf07631d7f5bbb226340bdf0141f" cdnthumburl="3057020100044b304902010002049363814102032f514902041f312275020468921fe4042439313734383466342d313532362d346632382d396330632d373932363361363862306261020405250a020201000405004c55cd00" cdnthumblength="3249" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902041f312275020468921fe4042439313734383466342d313532362d346632382d396330632d373932363361363862306261020405250a020201000405004c55cd00" length="16761" md5="e6447de886d715a4cb451e7b97d675e8" originsourcemd5="9fdf59f49e056942614d5ec0aebf2eb9"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754406884</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_l9koi6kli78i22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406916, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>8025a48e45b72530de1f55df98e4580c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_Z7UeX6w3|v1_/C7esmPx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 彩', 'NewMsgId': 734325577169568636, 'MsgSeq': 871430212}
2025-08-05 23:15:04 | DEBUG | 从群聊消息中提取发送者: wxid_l9koi6kli78i22
2025-08-05 23:15:04 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:15:04 | INFO | 收到引用消息: 消息ID:517676683 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 内容:彩 引用类型:3
2025-08-05 23:15:04 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:15:04 | INFO | [DouBaoImageToImage] 消息内容: '彩' from wxid_l9koi6kli78i22 in 48097389945@chatroom
2025-08-05 23:15:04 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['彩']
2025-08-05 23:15:04 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:15:04 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:15:04 | INFO |   - 消息内容: 彩
2025-08-05 23:15:04 | INFO |   - 群组ID: 48097389945@chatroom
2025-08-05 23:15:04 | INFO |   - 发送人: wxid_l9koi6kli78i22
2025-08-05 23:15:04 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>彩</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>2116591622226490727</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;5&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;8025a48e45b72530de1f55df98e4580c_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="16761" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;71&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_GBLuWOVQ|v1_+HmbySYz&lt;/signature&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="f0e1bf07631d7f5bbb226340bdf0141f" encryver="1" cdnthumbaeskey="f0e1bf07631d7f5bbb226340bdf0141f" cdnthumburl="3057020100044b304902010002049363814102032f514902041f312275020468921fe4042439313734383466342d313532362d346632382d396330632d373932363361363862306261020405250a020201000405004c55cd00" cdnthumblength="3249" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902041f312275020468921fe4042439313734383466342d313532362d346632382d396330632d373932363361363862306261020405250a020201000405004c55cd00" length="16761" md5="e6447de886d715a4cb451e7b97d675e8" originsourcemd5="9fdf59f49e056942614d5ec0aebf2eb9"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754406884</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_l9koi6kli78i22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n', 'Msgid': '2116591622226490727', 'NewMsgId': '2116591622226490727', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>8025a48e45b72530de1f55df98e4580c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="16761" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>71</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_GBLuWOVQ|v1_+HmbySYz</signature>\n</msgsource>\n', 'Createtime': '1754406884', 'SenderWxid': 'wxid_l9koi6kli78i22'}
2025-08-05 23:15:04 | INFO |   - 引用消息ID: 
2025-08-05 23:15:04 | INFO |   - 引用消息类型: 
2025-08-05 23:15:04 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>彩</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>2116591622226490727</svrid>
			<fromusr>48097389945@chatroom</fromusr>
			<chatusr>xiaomaochong</chatusr>
			<displayname>小爱</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;alnode&gt;
		&lt;fr&gt;5&lt;/fr&gt;
	&lt;/alnode&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;8025a48e45b72530de1f55df98e4580c_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="16761" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;71&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_GBLuWOVQ|v1_+HmbySYz&lt;/signature&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="f0e1bf07631d7f5bbb226340bdf0141f" encryver="1" cdnthumbaeskey="f0e1bf07631d7f5bbb226340bdf0141f" cdnthumburl="3057020100044b304902010002049363814102032f514902041f312275020468921fe4042439313734383466342d313532362d346632382d396330632d373932363361363862306261020405250a020201000405004c55cd00" cdnthumblength="3249" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902041f312275020468921fe4042439313734383466342d313532362d346632382d396330632d373932363361363862306261020405250a020201000405004c55cd00" length="16761" md5="e6447de886d715a4cb451e7b97d675e8" originsourcemd5="9fdf59f49e056942614d5ec0aebf2eb9"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1754406884</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>wxid_l9koi6kli78i22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-05 23:15:04 | INFO |   - 引用消息发送人: wxid_l9koi6kli78i22
2025-08-05 23:15:10 | DEBUG | 收到消息: {'MsgId': 2050205815, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n@饿飞\u2005我现在王者。能不能爬上荣耀'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406922, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[tianen532965049]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_o8bZWMu5|v1_6CwzhyHB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 395792988068479149, 'MsgSeq': 871430213}
2025-08-05 23:15:10 | INFO | 收到文本消息: 消息ID:2050205815 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:['tianen532965049'] 内容:@饿飞 我现在王者。能不能爬上荣耀
2025-08-05 23:15:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@饿飞 我现在王者。能不能爬上荣耀' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:15:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['@饿飞\u2005我现在王者。能不能爬上荣耀']
2025-08-05 23:15:10 | DEBUG | 处理消息内容: '@饿飞 我现在王者。能不能爬上荣耀'
2025-08-05 23:15:10 | DEBUG | 消息内容 '@饿飞 我现在王者。能不能爬上荣耀' 不匹配任何命令，忽略
2025-08-05 23:15:13 | DEBUG | 收到消息: {'MsgId': 1334183672, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_4183511832012:\n<msg><emoji fromusername = "wxid_4183511832012" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="917f700a493e0e4bc30d7f7592bbcb5e" len = "1269409" productid="" androidmd5="917f700a493e0e4bc30d7f7592bbcb5e" androidlen="1269409" s60v3md5 = "917f700a493e0e4bc30d7f7592bbcb5e" s60v3len="1269409" s60v5md5 = "917f700a493e0e4bc30d7f7592bbcb5e" s60v5len="1269409" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=917f700a493e0e4bc30d7f7592bbcb5e&amp;filekey=30440201010430302e02016e04025348042039313766373030613439336530653462633330643766373539326262636235650203135ea1040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb69100092717a5d6dab50000006e01004fb1534820ce91b1500d04f2e&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=da003382f14ab370fbb1a3356a964745&amp;filekey=30440201010430302e02016e04025348042064613030333338326631346162333730666262316133333536613936343734350203135eb0040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb691000b2e9aa5d6dab50000006e02004fb2534820ce91b1500d04f5a&amp;ef=2&amp;bizid=1022" aeskey= "e6218e27599f4c76a354927025d3ebdb" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=00a909f7ccf92ab327a136f29f787dac&amp;filekey=30440201010430302e02016e0402534804203030613930396637636366393261623332376131333666323966373837646163020301a110040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb691000cb2efa5d6dab50000006e03004fb3534820ce91b1500d04f69&amp;ef=3&amp;bizid=1022" externmd5 = "d1e90465ee834f2adbe5be15cb819fec" width= "240" height= "235" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406924, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_XhOnWzEQ|v1_shINS9p7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1343488184655822979, 'MsgSeq': 871430214}
2025-08-05 23:15:13 | INFO | 收到表情消息: 消息ID:1334183672 来自:27852221909@chatroom 发送人:wxid_4183511832012 MD5:917f700a493e0e4bc30d7f7592bbcb5e 大小:1269409
2025-08-05 23:15:13 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1343488184655822979
2025-08-05 23:15:13 | DEBUG | 收到消息: {'MsgId': 1373251945, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<msg><emoji fromusername="wxid_l9koi6kli78i22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="d884353358b361d736650d0c545aac07" len="50865" productid="" androidmd5="d884353358b361d736650d0c545aac07" androidlen="50865" s60v3md5="d884353358b361d736650d0c545aac07" s60v3len="50865" s60v5md5="d884353358b361d736650d0c545aac07" s60v5len="50865" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=d884353358b361d736650d0c545aac07&amp;filekey=30440201010430302e02016e0402534804206438383433353333353862333631643733363635306430633534356161633037020300c6b1040d00000004627466730000000132&amp;hy=SH&amp;storeid=26552d3b2000bfe256c8a355e0000006e01004fb153481843603156573f36c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=f43b05c3968c31b7d82993757e59a91d&amp;filekey=30440201010430302e02016e0402534804206634336230356333393638633331623764383239393337353765353961393164020300c6c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26552d3b2000c83936c8a355e0000006e02004fb253481843603156573f382&amp;ef=2&amp;bizid=1022" aeskey="119a7327626d4b24b7c42d52abb96865" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=5b407b405193abd441e88f6d3b0b34dd&amp;filekey=3043020101042f302d02016e040253480420356234303762343035313933616264343431653838663664336230623334646402021080040d00000004627466730000000132&amp;hy=SH&amp;storeid=26552d3b2000e1e096c8a355e0000006e03004fb353481843603156573f3a8&amp;ef=3&amp;bizid=1022" externmd5="ac72583a1d0162cd2943243c3e15f83c" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406925, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_cBPqnEHV|v1_JmEXYAne</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五在群聊中发了一个表情', 'NewMsgId': 3414251104085489365, 'MsgSeq': 871430215}
2025-08-05 23:15:13 | INFO | 收到表情消息: 消息ID:1373251945 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 MD5:d884353358b361d736650d0c545aac07 大小:50865
2025-08-05 23:15:14 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3414251104085489365
