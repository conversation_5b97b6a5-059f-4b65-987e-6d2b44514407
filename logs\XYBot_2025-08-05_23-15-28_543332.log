2025-08-05 23:15:29 | SUCCESS | 读取主设置成功
2025-08-05 23:15:29 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 23:15:29 | INFO | 2025/08/05 23:15:29 GetRedisAddr: 127.0.0.1:6379
2025-08-05 23:15:29 | INFO | 2025/08/05 23:15:29 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 23:15:29 | INFO | 2025/08/05 23:15:29 Server start at :9000
2025-08-05 23:15:30 | SUCCESS | WechatAPI服务已启动
2025-08-05 23:15:30 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 23:15:30 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 23:15:30 | SUCCESS | 登录成功
2025-08-05 23:15:30 | SUCCESS | 已开启自动心跳
2025-08-05 23:15:30 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:15:30 | SUCCESS | 数据库初始化成功
2025-08-05 23:15:30 | SUCCESS | 定时任务已启动
2025-08-05 23:15:30 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 23:15:30 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:15:32 | INFO | 播客API初始化成功
2025-08-05 23:15:32 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:15:32 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 23:15:32 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 23:15:32 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 23:15:32 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 23:15:32 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 23:15:32 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 23:15:32 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 23:15:32 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 23:15:32 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 23:15:32 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 23:15:32 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 23:15:32 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 23:15:32 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 23:15:32 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 23:15:32 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 23:15:32 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 23:15:32 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 23:15:32 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 23:15:32 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 23:15:32 | DEBUG |   - 启用状态: True
2025-08-05 23:15:32 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 23:15:32 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 23:15:32 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 23:15:32 | DEBUG |   - Cookies配置: 已配置
2025-08-05 23:15:32 | DEBUG |   - 限制机制: 已禁用
2025-08-05 23:15:32 | DEBUG |   - 数字选择超时: 120秒
2025-08-05 23:15:32 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 23:15:32 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-05 23:15:32 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 23:15:32 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 23:15:32 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 23:15:32 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 23:15:32 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 23:15:33 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:15:33 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 23:15:33 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 23:15:33 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 23:15:33 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 23:15:33 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 23:15:33 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 23:15:33 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 23:15:33 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 23:15:33 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-05 23:15:34 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 23:15:34 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 23:15:34 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 23:15:34 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 23:15:34 | INFO | [yuanbao] 插件初始化完成
2025-08-05 23:15:34 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 23:15:34 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 23:15:34 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 23:15:34 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 23:15:34 | INFO | 处理堆积消息中
2025-08-05 23:15:35 | SUCCESS | 处理堆积消息完毕
2025-08-05 23:15:35 | SUCCESS | 开始处理消息
2025-08-05 23:15:41 | DEBUG | 收到消息: {'MsgId': 1035781818, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1915" length="2878" bufid="0" aeskey="6ddd6e14d48b360f91b0f1c82af7e750" voiceurl="3052020100044b30490201000204a95c809d02032df927020478089324020468922029042430646562343338652d303739332d343936322d393334612d66373632336166356530373602040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600512315080525e376c3115a3102" fromusername="wxid_ubbh6q832tcs21" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406953, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_1MUrkLxC|v1_0N8pRKUk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段语音', 'NewMsgId': 1941229921699071847, 'MsgSeq': 871430216}
2025-08-05 23:15:41 | INFO | 收到语音消息: 消息ID:1035781818 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1915" length="2878" bufid="0" aeskey="6ddd6e14d48b360f91b0f1c82af7e750" voiceurl="3052020100044b30490201000204a95c809d02032df927020478089324020468922029042430646562343338652d303739332d343936322d393334612d66373632336166356530373602040528000f0201000400" voicemd5="" clientmsgid="41346165353065353032633535366600512315080525e376c3115a3102" fromusername="wxid_ubbh6q832tcs21" /></msg>
2025-08-05 23:15:42 | DEBUG | [VoiceTest] 缓存语音 MsgId: 1035781818
2025-08-05 23:15:42 | DEBUG | [VoiceTest] 缓存语音 NewMsgId: 1941229921699071847
2025-08-05 23:15:42 | INFO | [VoiceTest] 已缓存语音消息: MsgId=1035781818, NewMsgId=1941229921699071847
2025-08-05 23:15:47 | DEBUG | 收到消息: {'MsgId': 113086145, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>1941229921699071847</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource />\n\t\t\t<content>wxid_ubbh6q832tcs21:1915:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754406953</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406959, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>34daed7a2db99492dedd2e34db31e488_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_Pyu/Re7g|v1_gJUkIKZA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 跟着唱', 'NewMsgId': 6619368977070852258, 'MsgSeq': 871430217}
2025-08-05 23:15:47 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-05 23:15:47 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:15:47 | INFO | 收到引用消息: 消息ID:113086145 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-05 23:15:48 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:15:48 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 23:15:48 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-05 23:15:48 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:15:48 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-05 23:15:48 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:15:48 | INFO |   - 消息内容: 跟着唱
2025-08-05 23:15:48 | INFO |   - 群组ID: 55878994168@chatroom
2025-08-05 23:15:48 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-05 23:15:48 | INFO |   - 引用信息: {'MsgType': 34, 'Content': 'wxid_ubbh6q832tcs21:1915:0\n', 'Msgid': '1941229921699071847', 'NewMsgId': '1941229921699071847', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '郭', 'MsgSource': None, 'Createtime': '1754406953', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-05 23:15:48 | INFO |   - 引用消息ID: 
2025-08-05 23:15:48 | INFO |   - 引用消息类型: 
2025-08-05 23:15:48 | INFO |   - 引用消息内容: wxid_ubbh6q832tcs21:1915:0

2025-08-05 23:15:48 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-05 23:15:48 | ERROR | [VoiceTest] 处理引用语音消息异常: Invalid Parameters
2025-08-05 23:15:48 | ERROR | [VoiceTest] 异常堆栈: Traceback (most recent call last):
  File "C:\XYBotV2\plugins\VoiceTest\main.py", line 360, in handle_quote
    silk_base64 = await bot.download_voice(original_msg_id, voiceurl, length)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\WechatAPI\Client\tool.py", line 94, in download_voice
    self.error_handler(json_resp)
  File "C:\XYBotV2\WechatAPI\Client\base.py", line 82, in error_handler
    raise ValueError(json_resp.get("Message"))
ValueError: Invalid Parameters

2025-08-05 23:15:49 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 处理引用语音时出错
2025-08-05 23:16:03 | DEBUG | 收到消息: {'MsgId': 764788230, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n我现在贡献已经11w了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406975, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_mDxxeLPX|v1_tVjkwRVN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3774816276171620641, 'MsgSeq': 871430220}
2025-08-05 23:16:03 | INFO | 收到文本消息: 消息ID:764788230 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:[] 内容:我现在贡献已经11w了
2025-08-05 23:16:03 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我现在贡献已经11w了' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:16:03 | DEBUG | [DouBaoImageToImage] 命令解析: ['我现在贡献已经11w了']
2025-08-05 23:16:03 | DEBUG | 处理消息内容: '我现在贡献已经11w了'
2025-08-05 23:16:03 | DEBUG | 消息内容 '我现在贡献已经11w了' 不匹配任何命令，忽略
2025-08-05 23:16:11 | DEBUG | 收到消息: {'MsgId': 142670237, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_4183511832012:\n<msg><emoji fromusername = "wxid_4183511832012" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="917f700a493e0e4bc30d7f7592bbcb5e" len = "1269409" productid="" androidmd5="917f700a493e0e4bc30d7f7592bbcb5e" androidlen="1269409" s60v3md5 = "917f700a493e0e4bc30d7f7592bbcb5e" s60v3len="1269409" s60v5md5 = "917f700a493e0e4bc30d7f7592bbcb5e" s60v5len="1269409" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=917f700a493e0e4bc30d7f7592bbcb5e&amp;filekey=30440201010430302e02016e04025348042039313766373030613439336530653462633330643766373539326262636235650203135ea1040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb69100092717a5d6dab50000006e01004fb1534820ce91b1500d04f2e&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=da003382f14ab370fbb1a3356a964745&amp;filekey=30440201010430302e02016e04025348042064613030333338326631346162333730666262316133333536613936343734350203135eb0040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb691000b2e9aa5d6dab50000006e02004fb2534820ce91b1500d04f5a&amp;ef=2&amp;bizid=1022" aeskey= "e6218e27599f4c76a354927025d3ebdb" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=00a909f7ccf92ab327a136f29f787dac&amp;filekey=30440201010430302e02016e0402534804203030613930396637636366393261623332376131333666323966373837646163020301a110040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb691000cb2efa5d6dab50000006e03004fb3534820ce91b1500d04f69&amp;ef=3&amp;bizid=1022" externmd5 = "d1e90465ee834f2adbe5be15cb819fec" width= "240" height= "235" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406982, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_40tpaGzg|v1_Z4WGLTwy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8584256454510921526, 'MsgSeq': 871430221}
2025-08-05 23:16:11 | INFO | 收到表情消息: 消息ID:142670237 来自:27852221909@chatroom 发送人:wxid_4183511832012 MD5:917f700a493e0e4bc30d7f7592bbcb5e 大小:1269409
2025-08-05 23:16:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8584256454510921526
2025-08-05 23:16:19 | DEBUG | 收到消息: {'MsgId': 461234939, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_4183511832012:\n<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>155486197</msgid><newmsgid>1343488184655822979</newmsgid><replacemsg><![CDATA["小艺" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406985, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1223864540501979755, 'MsgSeq': 871430222}
2025-08-05 23:16:19 | DEBUG | 系统消息类型: revokemsg
2025-08-05 23:16:19 | INFO | 未知的系统消息类型: {'MsgId': 461234939, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>155486197</msgid><newmsgid>1343488184655822979</newmsgid><replacemsg><![CDATA["小艺" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406985, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1223864540501979755, 'MsgSeq': 871430222, 'FromWxid': '27852221909@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_4183511832012'}
2025-08-05 23:16:25 | DEBUG | 收到消息: {'MsgId': 323734976, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n没问题8w姐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754406997, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_r1cAbSGI|v1_s0beCHEp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8265139105364584894, 'MsgSeq': 871430223}
2025-08-05 23:16:25 | INFO | 收到文本消息: 消息ID:323734976 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:没问题8w姐
2025-08-05 23:16:25 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没问题8w姐' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:16:25 | DEBUG | [DouBaoImageToImage] 命令解析: ['没问题8w姐']
2025-08-05 23:16:25 | DEBUG | 处理消息内容: '没问题8w姐'
2025-08-05 23:16:25 | DEBUG | 消息内容 '没问题8w姐' 不匹配任何命令，忽略
2025-08-05 23:16:44 | DEBUG | 收到消息: {'MsgId': 1658899229, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="1b19eeaed0678d8348652dc1c710538a" len="679900" productid="" androidmd5="1b19eeaed0678d8348652dc1c710538a" androidlen="679900" s60v3md5="1b19eeaed0678d8348652dc1c710538a" s60v3len="679900" s60v5md5="1b19eeaed0678d8348652dc1c710538a" s60v5len="679900" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=1b19eeaed0678d8348652dc1c710538a&amp;filekey=30440201010430302e02016e0402535a0420316231396565616564303637386438333438363532646331633731303533386102030a5fdc040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265ede2aa000c7b5c1f4d68350000006e01004fb1535a2d7f60115671ffeed&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=42c6fac924b1f30bae1f2c4b82c7999f&amp;filekey=30440201010430302e02016e0402535a0420343263366661633932346231663330626165316632633462383263373939396602030a5fe0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265ede2aa000e517f1f4d68350000006e02004fb2535a2d7f60115671fff06&amp;ef=2&amp;bizid=1022" aeskey="bb5b6ad5bdc74036a4d3298b50b518c7" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=b4d063af9eb06006cf9160d3a3b7bfac&amp;filekey=30440201010430302e02016e0402535a0420623464303633616639656230363030366366393136306433613362376266616302030117e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265ede2ab00006f2a1f4d68350000006e03004fb3535a2d7f60115671fff21&amp;ef=3&amp;bizid=1022" externmd5="dc82eb5ac42831b23620287cb622b581" width="640" height="640" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407016, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_DwVhrBAT|v1_OOouFAdF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 9048297544260923524, 'MsgSeq': 871430224}
2025-08-05 23:16:44 | INFO | 收到表情消息: 消息ID:1658899229 来自:48097389945@chatroom 发送人:last--exile MD5:1b19eeaed0678d8348652dc1c710538a 大小:679900
2025-08-05 23:16:44 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9048297544260923524
2025-08-05 23:16:50 | DEBUG | 收到消息: {'MsgId': 1186678223, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n好哇'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407022, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Sv6jerOF|v1_0nrPsRNU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1196582825216282588, 'MsgSeq': 871430225}
2025-08-05 23:16:50 | INFO | 收到文本消息: 消息ID:1186678223 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:[] 内容:好哇
2025-08-05 23:16:50 | DEBUG | [DouBaoImageToImage] 收到文本消息: '好哇' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:16:50 | DEBUG | [DouBaoImageToImage] 命令解析: ['好哇']
2025-08-05 23:16:50 | DEBUG | 处理消息内容: '好哇'
2025-08-05 23:16:50 | DEBUG | 消息内容 '好哇' 不匹配任何命令，忽略
2025-08-05 23:16:55 | DEBUG | 收到消息: {'MsgId': 1776480132, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_4183511832012:\n<msg><emoji fromusername = "wxid_4183511832012" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="f7a3a0479f8e2a4528c4e7f688615f86" len = "9181" productid="" androidmd5="f7a3a0479f8e2a4528c4e7f688615f86" androidlen="9181" s60v3md5 = "f7a3a0479f8e2a4528c4e7f688615f86" s60v3len="9181" s60v5md5 = "f7a3a0479f8e2a4528c4e7f688615f86" s60v5len="9181" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=f7a3a0479f8e2a4528c4e7f688615f86&amp;filekey=3043020101042f302d02016e0402535a04206637613361303437396638653261343532386334653766363838363135663836020223dd040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2676e1e2200062b2370d762d90000006e01004fb1535a1bc645415677de234&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=3a35f85acae621e62a46b45c8e61633a&amp;filekey=3043020101042f302d02016e0402535a04203361333566383561636165363231653632613436623435633865363136333361020223e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2676e1e220006ebae70d762d90000006e02004fb2535a1bc645415677de23b&amp;ef=2&amp;bizid=1022" aeskey= "2d06b1474b0f4fa78307069ebc964041" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=cfaaab721ef10c14e36911ef51532ea4&amp;filekey=3043020101042f302d02016e0402535a0420636661616162373231656631306331346533363931316566353135333265613402020f50040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2676e1e220007b9f270d762d90000006e03004fb3535a1bc645415677de246&amp;ef=3&amp;bizid=1022" externmd5 = "82e105d1515b8dfe5b0db12430b3261d" width= "404" height= "235" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407027, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ct8kGe8f|v1_xiQCsPSl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 357357161608995402, 'MsgSeq': 871430226}
2025-08-05 23:16:55 | INFO | 收到表情消息: 消息ID:1776480132 来自:27852221909@chatroom 发送人:wxid_4183511832012 MD5:f7a3a0479f8e2a4528c4e7f688615f86 大小:9181
2025-08-05 23:16:55 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 357357161608995402
2025-08-05 23:16:56 | DEBUG | 收到消息: {'MsgId': 1623014738, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n不能清晰点？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407028, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_WqPSvZuM|v1_uH6zC4RS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 不能清晰点？', 'NewMsgId': 8920063652682845405, 'MsgSeq': 871430227}
2025-08-05 23:16:56 | INFO | 收到文本消息: 消息ID:1623014738 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:不能清晰点？
2025-08-05 23:16:56 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不能清晰点？' from last--exile in 48097389945@chatroom
2025-08-05 23:16:56 | DEBUG | [DouBaoImageToImage] 命令解析: ['不能清晰点？']
2025-08-05 23:16:56 | DEBUG | 处理消息内容: '不能清晰点？'
2025-08-05 23:16:56 | DEBUG | 消息内容 '不能清晰点？' 不匹配任何命令，忽略
2025-08-05 23:17:13 | DEBUG | 收到消息: {'MsgId': 1886586349, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>你青铜他也给你打</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>395792988068479149</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4183511832012</chatusr>\n\t\t\t<displayname>小艺</displayname>\n\t\t\t<content>@饿飞\u2005我现在王者。能不能爬上荣耀</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;859854723&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;&lt;![CDATA[tianen532965049]]&gt;&lt;/atuserlist&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_pcvWhIjF|v1_ooR7y88A&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754406922</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_xv01lkcmn48l22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407045, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>97997179e1e630a781cbfbb4536cf30c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_u+tI8VOS|v1_q6mAvGFy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6688414335380635663, 'MsgSeq': 871430228}
2025-08-05 23:17:13 | DEBUG | 从群聊消息中提取发送者: wxid_xv01lkcmn48l22
2025-08-05 23:17:13 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:17:13 | INFO | 收到引用消息: 消息ID:1886586349 来自:27852221909@chatroom 发送人:wxid_xv01lkcmn48l22 内容:你青铜他也给你打 引用类型:1
2025-08-05 23:17:13 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:17:13 | INFO | [DouBaoImageToImage] 消息内容: '你青铜他也给你打' from wxid_xv01lkcmn48l22 in 27852221909@chatroom
2025-08-05 23:17:13 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['你青铜他也给你打']
2025-08-05 23:17:13 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:17:13 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:17:13 | INFO |   - 消息内容: 你青铜他也给你打
2025-08-05 23:17:13 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:17:13 | INFO |   - 发送人: wxid_xv01lkcmn48l22
2025-08-05 23:17:13 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@饿飞\u2005我现在王者。能不能爬上荣耀', 'Msgid': '395792988068479149', 'NewMsgId': '395792988068479149', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '小艺', 'MsgSource': '<msgsource><sequence_id>859854723</sequence_id>\n\t<atuserlist><![CDATA[tianen532965049]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_pcvWhIjF|v1_ooR7y88A</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754406922', 'SenderWxid': 'wxid_xv01lkcmn48l22'}
2025-08-05 23:17:13 | INFO |   - 引用消息ID: 
2025-08-05 23:17:13 | INFO |   - 引用消息类型: 
2025-08-05 23:17:13 | INFO |   - 引用消息内容: @饿飞 我现在王者。能不能爬上荣耀
2025-08-05 23:17:13 | INFO |   - 引用消息发送人: wxid_xv01lkcmn48l22
2025-08-05 23:17:15 | DEBUG | 收到消息: {'MsgId': 271870603, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="438849a397f5a1ed0288d8177a856857" len = "87404" productid="" androidmd5="438849a397f5a1ed0288d8177a856857" androidlen="87404" s60v3md5 = "438849a397f5a1ed0288d8177a856857" s60v3len="87404" s60v5md5 = "438849a397f5a1ed0288d8177a856857" s60v5len="87404" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=438849a397f5a1ed0288d8177a856857&amp;filekey=30350201010421301f02020106040253480410438849a397f5a1ed0288d8177a856857020301556c040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630cd542000826d5000000000000010600004f5053480e267b40b78d4ce32&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=27b710f75b7d0b53357e29e138211610&amp;filekey=30350201010421301f020201060402535a041027b710f75b7d0b53357e29e1382116100203015570040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2630cd542000de7e5000000000000010600004f50535a01c278809676a5b8d&amp;bizid=1023" aeskey= "82825e0be1ff1c3fa763f9e6f59bce1b" externurl = "" externmd5 = "" width= "640" height= "172" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407047, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_oxbpto8h|v1_y4OvF7pm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3901214528967760817, 'MsgSeq': 871430229}
2025-08-05 23:17:15 | INFO | 收到表情消息: 消息ID:271870603 来自:27852221909@chatroom 发送人:tianen532965049 MD5:438849a397f5a1ed0288d8177a856857 大小:87404
2025-08-05 23:17:15 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3901214528967760817
2025-08-05 23:17:19 | DEBUG | 收到消息: {'MsgId': 785600057, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n🫰'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407050, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Rvo2X7ta|v1_KefRD7AW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6517449357470360181, 'MsgSeq': 871430230}
2025-08-05 23:17:19 | INFO | 收到文本消息: 消息ID:785600057 来自:27852221909@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:🫰
2025-08-05 23:17:19 | DEBUG | [DouBaoImageToImage] 收到文本消息: '🫰' from wxid_xv01lkcmn48l22 in 27852221909@chatroom
2025-08-05 23:17:19 | DEBUG | [DouBaoImageToImage] 命令解析: ['🫰']
2025-08-05 23:17:19 | DEBUG | 处理消息内容: '🫰'
2025-08-05 23:17:19 | DEBUG | 消息内容 '🫰' 不匹配任何命令，忽略
2025-08-05 23:17:21 | DEBUG | 收到消息: {'MsgId': 1002098900, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14713234390417999893</objectId>\n\t\t\t<objectNonceId>4242548082790407036_6_25_12_3_1754406939447869_0b970350-720f-11f0-96da-8156bea4fa8f</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>太和小夫妻</nickname>\n\t\t\t<username>v2_060000231003b20faec8c7e68a1ac4ddcf00ea37b077c8297d04dc1c50a8fd07cac4e82e3989@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/snyl3eAAAgfibv3n8cNXGcz4qQI8MRtral6sFoZ6zjUGLBzebxEIPSOB9dfEwuhrMhwsf8yibibezicD1V9soms352tibPdI0o4uVy5daGWE06Pk/0]]></avatar>\n\t\t\t<desc>多大点事，顺手也就办了……#万万没想到 #意想不到的结局 #夫妻日常搞笑</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>1</authIconType>\n\t\t\t<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eez3Y79SxtvVL0L7CkPM6dFibFeI6caGYwFELeT5lnqfmKw4aeEESFOOFftm5NfXRHBeWKBRNpltRVh8BBpuEH8GrHLM1jdKRGLhYeyOUD5AUMVoZPgOibSPEqvOL8oUs7ia2uuAicpO0XicLmw&hy=SH&idx=1&m=65936db52c7aa11b9561c6c35badf77e&uzid=7a15c&token=cztXnd9GyrHuZHabPTl5Lsc5uzSI4Mwt7kUC40ZlY4YWv1ABmdUZc8HibEWiaxvsdBp8ibatOIeagglPeppcx7ODkvzyqR8fykQqXlqec0dfsxnTttwiahD0hcLicgHYkg1OOSiazYgiaYsnOI6P55SN3wOAf7miae4VqLUkpibwEAj0Zl2Q&basedata=CAESBnhXVDEyMRoGeFdUMTExGgZ4V1QxMTIaBnhXVDExMxoGeFdUMTI2GgZ4V1QxMjcaBnhXVDEyMRoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwjoHBAAGAI&sign=HR4CccdU_1Xc2BKeJX7VG_6cGQj3Rl0vluFVA2QRT6bX9cmd56sCtNnQCO9dcSUe_yfo7hehflasOeffCY8ZCA&ctsc=25&extg=10ab100&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAACBdLB5oaSPu5ZtHSCSaBAAAADnaHZTnGbFfAj9RgZXfw6VU%2BzHMiwZ50wRDCovJhKou0ldSA5FoivBlQdUNe%2BQx5mMQqr5oL9y8Q%3D%3D&svrnonce=1754406941]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv1jTgk1AjsjTQ1H6H5ibVW62axibDwXM7HzsalXCWVg4ID5YxxAYZZupG6CN8jsVQLDaXmicwLpLvUIO5IswplwgInjVrua4KutumQo1XTTicOGY&hy=SH&idx=1&m=eb8af1254127f38a682aaad7be426467&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxiaBE9necGSCgPDnwnXWbpOMvKkSb1jQ6jYfWqTWskhNc1qtvQZtztlenlpJ5L3bIg0Owh9dL4NwknibGPUcHTcSpTGUlh2C6ia5FoS0rKIHVV3rPzBtCQ8oicaaPKm8lYc6Tdib88VFwwOYic&ctsc=2-25]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv1jTgk1AjsjTQ1H6H5ibVW62axibDwXM7HzsalXCWVg4ID5YxxAYZZupG6CN8jsVQLDaXmicwLpLvUIO5IswplwgInjVrua4KutumQo1XTTicOGY&hy=SH&idx=1&m=eb8af1254127f38a682aaad7be426467&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxiaBE9necGSCgPDnwnXWbpOMvKkSb1jQ6jYfWqTWskhNc1qtvQZtztlenlpJ5L3bIg0Owh9dL4NwknibGPUcHTcSpTGUlh2C6ia5FoS0rKIHVV3rPzBtCQ8oicaaPKm8lYc6Tdib88VFwwOYic&ctsc=2-25]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1080.0</width>\n\t\t\t\t\t<height>1920.0</height>\n\t\t\t\t\t<videoPlayDuration>10</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>25</sourceCommentScene>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407051, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>f0693bc06fbe578c45a13ed169102c52_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_EwgXcN1y|v1_VMWZbKAp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 3215944646254676223, 'MsgSeq': 871430231}
2025-08-05 23:17:21 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-08-05 23:17:21 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14713234390417999893</objectId>
			<objectNonceId>4242548082790407036_6_25_12_3_1754406939447869_0b970350-720f-11f0-96da-8156bea4fa8f</objectNonceId>
			<feedType>4</feedType>
			<nickname>太和小夫妻</nickname>
			<username>v2_060000231003b20faec8c7e68a1ac4ddcf00ea37b077c8297d04dc1c50a8fd07cac4e82e3989@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/snyl3eAAAgfibv3n8cNXGcz4qQI8MRtral6sFoZ6zjUGLBzebxEIPSOB9dfEwuhrMhwsf8yibibezicD1V9soms352tibPdI0o4uVy5daGWE06Pk/0]]></avatar>
			<desc>多大点事，顺手也就办了……#万万没想到 #意想不到的结局 #夫妻日常搞笑</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>1</authIconType>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eez3Y79SxtvVL0L7CkPM6dFibFeI6caGYwFELeT5lnqfmKw4aeEESFOOFftm5NfXRHBeWKBRNpltRVh8BBpuEH8GrHLM1jdKRGLhYeyOUD5AUMVoZPgOibSPEqvOL8oUs7ia2uuAicpO0XicLmw&hy=SH&idx=1&m=65936db52c7aa11b9561c6c35badf77e&uzid=7a15c&token=cztXnd9GyrHuZHabPTl5Lsc5uzSI4Mwt7kUC40ZlY4YWv1ABmdUZc8HibEWiaxvsdBp8ibatOIeagglPeppcx7ODkvzyqR8fykQqXlqec0dfsxnTttwiahD0hcLicgHYkg1OOSiazYgiaYsnOI6P55SN3wOAf7miae4VqLUkpibwEAj0Zl2Q&basedata=CAESBnhXVDEyMRoGeFdUMTExGgZ4V1QxMTIaBnhXVDExMxoGeFdUMTI2GgZ4V1QxMjcaBnhXVDEyMRoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwjoHBAAGAI&sign=HR4CccdU_1Xc2BKeJX7VG_6cGQj3Rl0vluFVA2QRT6bX9cmd56sCtNnQCO9dcSUe_yfo7hehflasOeffCY8ZCA&ctsc=25&extg=10ab100&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAACBdLB5oaSPu5ZtHSCSaBAAAADnaHZTnGbFfAj9RgZXfw6VU%2BzHMiwZ50wRDCovJhKou0ldSA5FoivBlQdUNe%2BQx5mMQqr5oL9y8Q%3D%3D&svrnonce=1754406941]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv1jTgk1AjsjTQ1H6H5ibVW62axibDwXM7HzsalXCWVg4ID5YxxAYZZupG6CN8jsVQLDaXmicwLpLvUIO5IswplwgInjVrua4KutumQo1XTTicOGY&hy=SH&idx=1&m=eb8af1254127f38a682aaad7be426467&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxiaBE9necGSCgPDnwnXWbpOMvKkSb1jQ6jYfWqTWskhNc1qtvQZtztlenlpJ5L3bIg0Owh9dL4NwknibGPUcHTcSpTGUlh2C6ia5FoS0rKIHVV3rPzBtCQ8oicaaPKm8lYc6Tdib88VFwwOYic&ctsc=2-25]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv1jTgk1AjsjTQ1H6H5ibVW62axibDwXM7HzsalXCWVg4ID5YxxAYZZupG6CN8jsVQLDaXmicwLpLvUIO5IswplwgInjVrua4KutumQo1XTTicOGY&hy=SH&idx=1&m=eb8af1254127f38a682aaad7be426467&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxiaBE9necGSCgPDnwnXWbpOMvKkSb1jQ6jYfWqTWskhNc1qtvQZtztlenlpJ5L3bIg0Owh9dL4NwknibGPUcHTcSpTGUlh2C6ia5FoS0rKIHVV3rPzBtCQ8oicaaPKm8lYc6Tdib88VFwwOYic&ctsc=2-25]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>10</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>25</sourceCommentScene>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-05 23:17:21 | DEBUG | XML消息类型: 51
2025-08-05 23:17:21 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-05 23:17:21 | DEBUG | XML消息描述: None
2025-08-05 23:17:21 | DEBUG | 附件信息 totallen: 0
2025-08-05 23:17:21 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-05 23:17:21 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-05 23:17:21 | INFO | 未知的XML消息类型: 51
2025-08-05 23:17:21 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-05 23:17:21 | INFO | 消息描述: None
2025-08-05 23:17:21 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-05 23:17:21 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14713234390417999893</objectId>
			<objectNonceId>4242548082790407036_6_25_12_3_1754406939447869_0b970350-720f-11f0-96da-8156bea4fa8f</objectNonceId>
			<feedType>4</feedType>
			<nickname>太和小夫妻</nickname>
			<username>v2_060000231003b20faec8c7e68a1ac4ddcf00ea37b077c8297d04dc1c50a8fd07cac4e82e3989@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/snyl3eAAAgfibv3n8cNXGcz4qQI8MRtral6sFoZ6zjUGLBzebxEIPSOB9dfEwuhrMhwsf8yibibezicD1V9soms352tibPdI0o4uVy5daGWE06Pk/0]]></avatar>
			<desc>多大点事，顺手也就办了……#万万没想到 #意想不到的结局 #夫妻日常搞笑</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>1</authIconType>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eez3Y79SxtvVL0L7CkPM6dFibFeI6caGYwFELeT5lnqfmKw4aeEESFOOFftm5NfXRHBeWKBRNpltRVh8BBpuEH8GrHLM1jdKRGLhYeyOUD5AUMVoZPgOibSPEqvOL8oUs7ia2uuAicpO0XicLmw&hy=SH&idx=1&m=65936db52c7aa11b9561c6c35badf77e&uzid=7a15c&token=cztXnd9GyrHuZHabPTl5Lsc5uzSI4Mwt7kUC40ZlY4YWv1ABmdUZc8HibEWiaxvsdBp8ibatOIeagglPeppcx7ODkvzyqR8fykQqXlqec0dfsxnTttwiahD0hcLicgHYkg1OOSiazYgiaYsnOI6P55SN3wOAf7miae4VqLUkpibwEAj0Zl2Q&basedata=CAESBnhXVDEyMRoGeFdUMTExGgZ4V1QxMTIaBnhXVDExMxoGeFdUMTI2GgZ4V1QxMjcaBnhXVDEyMRoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwjoHBAAGAI&sign=HR4CccdU_1Xc2BKeJX7VG_6cGQj3Rl0vluFVA2QRT6bX9cmd56sCtNnQCO9dcSUe_yfo7hehflasOeffCY8ZCA&ctsc=25&extg=10ab100&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAACBdLB5oaSPu5ZtHSCSaBAAAADnaHZTnGbFfAj9RgZXfw6VU%2BzHMiwZ50wRDCovJhKou0ldSA5FoivBlQdUNe%2BQx5mMQqr5oL9y8Q%3D%3D&svrnonce=1754406941]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv1jTgk1AjsjTQ1H6H5ibVW62axibDwXM7HzsalXCWVg4ID5YxxAYZZupG6CN8jsVQLDaXmicwLpLvUIO5IswplwgInjVrua4KutumQo1XTTicOGY&hy=SH&idx=1&m=eb8af1254127f38a682aaad7be426467&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxiaBE9necGSCgPDnwnXWbpOMvKkSb1jQ6jYfWqTWskhNc1qtvQZtztlenlpJ5L3bIg0Owh9dL4NwknibGPUcHTcSpTGUlh2C6ia5FoS0rKIHVV3rPzBtCQ8oicaaPKm8lYc6Tdib88VFwwOYic&ctsc=2-25]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv1jTgk1AjsjTQ1H6H5ibVW62axibDwXM7HzsalXCWVg4ID5YxxAYZZupG6CN8jsVQLDaXmicwLpLvUIO5IswplwgInjVrua4KutumQo1XTTicOGY&hy=SH&idx=1&m=eb8af1254127f38a682aaad7be426467&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxiaBE9necGSCgPDnwnXWbpOMvKkSb1jQ6jYfWqTWskhNc1qtvQZtztlenlpJ5L3bIg0Owh9dL4NwknibGPUcHTcSpTGUlh2C6ia5FoS0rKIHVV3rPzBtCQ8oicaaPKm8lYc6Tdib88VFwwOYic&ctsc=2-25]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>10</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>25</sourceCommentScene>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-05 23:17:41 | DEBUG | 收到消息: {'MsgId': 164198688, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407073, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_2akdxZNo|v1_Q0rRU2kg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7078588528006697283, 'MsgSeq': 871430232}
2025-08-05 23:17:41 | INFO | 收到文本消息: 消息ID:164198688 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:[] 内容:哈哈
2025-08-05 23:17:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '哈哈' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:17:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['哈哈']
2025-08-05 23:17:41 | DEBUG | 处理消息内容: '哈哈'
2025-08-05 23:17:41 | DEBUG | 消息内容 '哈哈' 不匹配任何命令，忽略
2025-08-05 23:17:51 | DEBUG | 收到消息: {'MsgId': 1530673624, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n青铜接不住'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407083, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_T/nYibtf|v1_6oc1Hm4/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7124618828174729531, 'MsgSeq': 871430233}
2025-08-05 23:17:51 | INFO | 收到文本消息: 消息ID:1530673624 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:青铜接不住
2025-08-05 23:17:51 | DEBUG | [DouBaoImageToImage] 收到文本消息: '青铜接不住' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:17:51 | DEBUG | [DouBaoImageToImage] 命令解析: ['青铜接不住']
2025-08-05 23:17:51 | DEBUG | 处理消息内容: '青铜接不住'
2025-08-05 23:17:51 | DEBUG | 消息内容 '青铜接不住' 不匹配任何命令，忽略
2025-08-05 23:17:53 | DEBUG | 收到消息: {'MsgId': 653678226, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n8w姐啥意思啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407083, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_2SZnJ77C|v1_M9n9l2DE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4294598525985751729, 'MsgSeq': 871430234}
2025-08-05 23:17:53 | INFO | 收到文本消息: 消息ID:653678226 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:8w姐啥意思啊
2025-08-05 23:17:53 | DEBUG | [DouBaoImageToImage] 收到文本消息: '8w姐啥意思啊' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:17:53 | DEBUG | [DouBaoImageToImage] 命令解析: ['8w姐啥意思啊']
2025-08-05 23:17:53 | DEBUG | 处理消息内容: '8w姐啥意思啊'
2025-08-05 23:17:53 | DEBUG | 消息内容 '8w姐啥意思啊' 不匹配任何命令，忽略
2025-08-05 23:18:07 | DEBUG | 收到消息: {'MsgId': 1594694372, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n青铜我来 打到钻石 然后他来'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407098, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_yP7vsbOr|v1_dECZJzIg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 137366003381943756, 'MsgSeq': 871430235}
2025-08-05 23:18:07 | INFO | 收到文本消息: 消息ID:1594694372 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:青铜我来 打到钻石 然后他来
2025-08-05 23:18:07 | DEBUG | [DouBaoImageToImage] 收到文本消息: '青铜我来 打到钻石 然后他来' from wxid_idzryo4rneok22 in 27852221909@chatroom
2025-08-05 23:18:07 | DEBUG | [DouBaoImageToImage] 命令解析: ['青铜我来', '打到钻石', '然后他来']
2025-08-05 23:18:07 | DEBUG | 处理消息内容: '青铜我来 打到钻石 然后他来'
2025-08-05 23:18:07 | DEBUG | 消息内容 '青铜我来 打到钻石 然后他来' 不匹配任何命令，忽略
2025-08-05 23:18:11 | DEBUG | 收到消息: {'MsgId': 1422002236, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="27852221909@chatroom" type="1" idbuffer="media:0_0" md5="cfd3cc73fbc39eca8dc39b912da89576" len="26820" productid="" androidmd5="cfd3cc73fbc39eca8dc39b912da89576" androidlen="26820" s60v3md5="cfd3cc73fbc39eca8dc39b912da89576" s60v3len="26820" s60v5md5="cfd3cc73fbc39eca8dc39b912da89576" s60v5len="26820" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=cfd3cc73fbc39eca8dc39b912da89576&amp;filekey=3043020101042f302d02016e0402534804206366643363633733666263333965636138646333396239313264613839353736020268c4040d00000004627466730000000132&amp;hy=SH&amp;storeid=266f56c6d000d220ed8430d640000006e01004fb153482773f031567915c1b&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=4fd920f5b0eb4d8ef97dc4b8e79a5052&amp;filekey=3043020101042f302d02016e0402534804203466643932306635623065623464386566393764633462386537396135303532020268d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=266f56c6d000dff8fd8430d640000006e02004fb253482773f031567915c30&amp;ef=2&amp;bizid=1022" aeskey="33bad4372f904110aea2202300878f73" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=c458d5502735c0806c772116af8b6556&amp;filekey=3043020101042f302d02016e040253480420633435386435353032373335633038303663373732313136616638623635353602023010040d00000004627466730000000132&amp;hy=SH&amp;storeid=266f56c6d000e9a52d8430d640000006e03004fb353482773f031567915c3c&amp;ef=3&amp;bizid=1022" externmd5="6f0eb0f9685e53e75feb35afa19f810b" width="467" height="503" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407103, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_t6koxcH2|v1_bdqzbV9A</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7494312414529531005, 'MsgSeq': 871430236}
2025-08-05 23:18:11 | INFO | 收到表情消息: 消息ID:1422002236 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 MD5:cfd3cc73fbc39eca8dc39b912da89576 大小:26820
2025-08-05 23:18:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7494312414529531005
2025-08-05 23:18:27 | DEBUG | 收到消息: {'MsgId': 1725333675, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n666'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407119, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_HkTArRs7|v1_iY9rgkNy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8942347615212460608, 'MsgSeq': 871430237}
2025-08-05 23:18:27 | INFO | 收到文本消息: 消息ID:1725333675 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:[] 内容:666
2025-08-05 23:18:27 | DEBUG | [DouBaoImageToImage] 收到文本消息: '666' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:18:27 | DEBUG | [DouBaoImageToImage] 命令解析: ['666']
2025-08-05 23:18:27 | DEBUG | [DoubaoVideoSearch] 数字选择超时，距离上次搜索: 1754407107.5秒
2025-08-05 23:18:27 | DEBUG | 处理消息内容: '666'
2025-08-05 23:18:27 | DEBUG | 消息内容 '666' 不匹配任何命令，忽略
2025-08-05 23:18:57 | DEBUG | 收到消息: {'MsgId': 1082960221, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n她8w拍了个游轮 就叫她8w姐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407149, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_/uCHgpTD|v1_TUatxdUo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 96332590078382671, 'MsgSeq': 871430238}
2025-08-05 23:18:57 | INFO | 收到文本消息: 消息ID:1082960221 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:她8w拍了个游轮 就叫她8w姐
2025-08-05 23:18:57 | DEBUG | [DouBaoImageToImage] 收到文本消息: '她8w拍了个游轮 就叫她8w姐' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:18:57 | DEBUG | [DouBaoImageToImage] 命令解析: ['她8w拍了个游轮', '就叫她8w姐']
2025-08-05 23:18:57 | DEBUG | 处理消息内容: '她8w拍了个游轮 就叫她8w姐'
2025-08-05 23:18:57 | DEBUG | 消息内容 '她8w拍了个游轮 就叫她8w姐' 不匹配任何命令，忽略
2025-08-05 23:19:14 | DEBUG | 收到消息: {'MsgId': 714388645, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_4183511832012:\n<msg><emoji fromusername = "wxid_4183511832012" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="9d9b7202d41d6b8d1fa57d0d251753fb" len = "11997" productid="" androidmd5="9d9b7202d41d6b8d1fa57d0d251753fb" androidlen="11997" s60v3md5 = "9d9b7202d41d6b8d1fa57d0d251753fb" s60v3len="11997" s60v5md5 = "9d9b7202d41d6b8d1fa57d0d251753fb" s60v5len="11997" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=9d9b7202d41d6b8d1fa57d0d251753fb&amp;filekey=3043020101042f302d02016e0402535a0420396439623732303264343164366238643166613537643064323531373533666202022edd040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267c855060001377131b186290000006e01004fb1535a02c4b88096c6707eb&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=ba100ab8bd0ec27a888dd35325b24786&amp;filekey=3043020101042f302d02016e0402535a0420626131303061623862643065633237613838386464333533323562323437383602022ee0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267c855060002045e31b186290000006e02004fb2535a02c4b88096c6707fd&amp;ef=2&amp;bizid=1022" aeskey= "b56162ecd7ef4cc2a87c7637ac3e0b65" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0eb5d1d50111c2a8f1ea74a4a65c02cb&amp;filekey=3043020101042f302d02016e0402535a0420306562356431643530313131633261386631656137346134613635633032636202021150040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267c855060002b59a31b186290000006e03004fb3535a02c4b88096c67080e&amp;ef=3&amp;bizid=1022" externmd5 = "ae09bdc488f3417d9bd66deb6692f894" width= "436" height= "432" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407166, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_AkVkiR7Y|v1_Qvq4J7T/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 479782251086472919, 'MsgSeq': 871430239}
2025-08-05 23:19:14 | INFO | 收到表情消息: 消息ID:714388645 来自:27852221909@chatroom 发送人:wxid_4183511832012 MD5:9d9b7202d41d6b8d1fa57d0d251753fb 大小:11997
2025-08-05 23:19:14 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 479782251086472919
2025-08-05 23:19:28 | DEBUG | 收到消息: {'MsgId': 1330542855, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n只要有卡 蹭蹭上分'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407180, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_MgOOjyTV|v1_IonwbRWJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2536560892528259092, 'MsgSeq': 871430240}
2025-08-05 23:19:28 | INFO | 收到文本消息: 消息ID:1330542855 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:只要有卡 蹭蹭上分
2025-08-05 23:19:28 | DEBUG | [DouBaoImageToImage] 收到文本消息: '只要有卡 蹭蹭上分' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:19:28 | DEBUG | [DouBaoImageToImage] 命令解析: ['只要有卡', '蹭蹭上分']
2025-08-05 23:19:28 | DEBUG | 处理消息内容: '只要有卡 蹭蹭上分'
2025-08-05 23:19:28 | DEBUG | 消息内容 '只要有卡 蹭蹭上分' 不匹配任何命令，忽略
2025-08-05 23:19:37 | DEBUG | 收到消息: {'MsgId': 845061648, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>哈哈哈，原来这样啊</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>96332590078382671</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>tianen532965049</chatusr>\n\t\t\t<displayname>饿飞</displayname>\n\t\t\t<content>她8w拍了个游轮 就叫她8w姐</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;861647889&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_M4EPrfE+|v1_pm4QH2+m&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754407149</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_xfxd40diz3bd22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407189, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>2d538ce754edcdf8a0a88d6647020b36_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_mBz4UpZe|v1_6oMhqpW2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 646587119888716473, 'MsgSeq': 871430241}
2025-08-05 23:19:37 | DEBUG | 从群聊消息中提取发送者: wxid_xfxd40diz3bd22
2025-08-05 23:19:37 | DEBUG | 使用已解析的XML处理引用消息
2025-08-05 23:19:37 | INFO | 收到引用消息: 消息ID:845061648 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 内容:哈哈哈，原来这样啊 引用类型:1
2025-08-05 23:19:37 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-05 23:19:37 | INFO | [DouBaoImageToImage] 消息内容: '哈哈哈，原来这样啊' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:19:37 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['哈哈哈，原来这样啊']
2025-08-05 23:19:37 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-05 23:19:37 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-05 23:19:37 | INFO |   - 消息内容: 哈哈哈，原来这样啊
2025-08-05 23:19:37 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-05 23:19:37 | INFO |   - 发送人: wxid_xfxd40diz3bd22
2025-08-05 23:19:37 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '她8w拍了个游轮 就叫她8w姐', 'Msgid': '96332590078382671', 'NewMsgId': '96332590078382671', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '饿飞', 'MsgSource': '<msgsource><sequence_id>861647889</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_M4EPrfE+|v1_pm4QH2+m</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754407149', 'SenderWxid': 'wxid_xfxd40diz3bd22'}
2025-08-05 23:19:37 | INFO |   - 引用消息ID: 
2025-08-05 23:19:37 | INFO |   - 引用消息类型: 
2025-08-05 23:19:37 | INFO |   - 引用消息内容: 她8w拍了个游轮 就叫她8w姐
2025-08-05 23:19:37 | INFO |   - 引用消息发送人: wxid_xfxd40diz3bd22
2025-08-05 23:21:14 | DEBUG | 收到消息: {'MsgId': 463856175, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n叫我小艺就行。别听他的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407286, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_qtpMGlhb|v1_SJg7vObd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3396107394286025730, 'MsgSeq': 871430242}
2025-08-05 23:21:14 | INFO | 收到文本消息: 消息ID:463856175 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:[] 内容:叫我小艺就行。别听他的
2025-08-05 23:21:14 | DEBUG | [DouBaoImageToImage] 收到文本消息: '叫我小艺就行。别听他的' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:21:14 | DEBUG | [DouBaoImageToImage] 命令解析: ['叫我小艺就行。别听他的']
2025-08-05 23:21:14 | DEBUG | 处理消息内容: '叫我小艺就行。别听他的'
2025-08-05 23:21:14 | DEBUG | 消息内容 '叫我小艺就行。别听他的' 不匹配任何命令，忽略
2025-08-05 23:21:26 | DEBUG | 收到消息: {'MsgId': 1318068332, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="dc8439fe4537b0472e844ccab8422c70" len = "367061" productid="" androidmd5="dc8439fe4537b0472e844ccab8422c70" androidlen="367061" s60v3md5 = "dc8439fe4537b0472e844ccab8422c70" s60v3len="367061" s60v5md5 = "dc8439fe4537b0472e844ccab8422c70" s60v5len="367061" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=dc8439fe4537b0472e844ccab8422c70&amp;filekey=30350201010421301f020201060402535a0410dc8439fe4537b0472e844ccab8422c7002030599d5040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f26660000baa91ccfef41d0000010600004f50535a2c863bc1e786d4cfe&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=809f312713a09fe8d034b9564e7e7f5e&amp;filekey=30350201010421301f020201060402535a0410809f312713a09fe8d034b9564e7e7f5e02030599e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f26660000e8c3cccfef41d0000010600004f50535a26267bc1e7e006ddd&amp;bizid=1023" aeskey= "c9460931467dfa2c505f01fcc133ff99" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=4aea69e6407a246b54f3db2303e0d0b6&amp;filekey=30350201010421301f020201060402535a04104aea69e6407a246b54f3db2303e0d0b6020301d6d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266f2a6b00003fdba153237b40000010600004f50535a2134b88096734fc87&amp;bizid=1023" externmd5 = "5079bddab4225575c258e8b98b98ab6c" width= "111" height= "81" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407298, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ug5CUFeU|v1_1igZpE8Y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2758798538437753254, 'MsgSeq': 871430243}
2025-08-05 23:21:26 | INFO | 收到表情消息: 消息ID:1318068332 来自:27852221909@chatroom 发送人:tianen532965049 MD5:dc8439fe4537b0472e844ccab8422c70 大小:367061
2025-08-05 23:21:26 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2758798538437753254
2025-08-05 23:21:30 | DEBUG | 收到消息: {'MsgId': 2022833383, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '27852221909@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_4183511832012</fromusername>\n  <chatusername>27852221909@chatroom</chatusername>\n  <pattedusername>wxid_xfxd40diz3bd22</pattedusername>\n  <patsuffix><![CDATA[真的很无语。]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_4183511832012}" 拍了拍 "${wxid_xfxd40diz3bd22}" 真的很无语。]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407299, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 164398190742196256, 'MsgSeq': 871430244}
2025-08-05 23:21:30 | DEBUG | 系统消息类型: pat
2025-08-05 23:21:30 | INFO | 收到拍一拍消息: 消息ID:2022833383 来自:27852221909@chatroom 发送人:27852221909@chatroom 拍者:wxid_4183511832012 被拍:wxid_xfxd40diz3bd22 后缀:真的很无语。
2025-08-05 23:21:30 | DEBUG | [PatReply] 被拍者 wxid_xfxd40diz3bd22 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-08-05 23:21:33 | DEBUG | 收到消息: {'MsgId': 662859893, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<sysmsg type="gamecenter"><gamecenter msgtype="100" newmsgtype="100"><msg_id>unifiedpush_content_196408_1751817599.6_144849100_9007199254937400</msg_id><appid>wx041c36892884eb22</appid><push_id>unifiedpush_content_196408</push_id><weight>6</weight><time_info><create_time>1754407305</create_time><expire_time>259200</expire_time></time_info><entrance><entrance_red_dot_type>4</entrance_red_dot_type><entrance_icon_url>https://mmgame.qpic.cn/image/73d81c459e8f5d51b02b66516bd6f275a77353c948ae1e799b50057a576d9ec2/h64</entrance_icon_url><entrance_text>您被移出“光之阵营”</entrance_text><entrance_icon_rounded_corner>1</entrance_icon_rounded_corner><entrance_show_control><basic_type>10000</basic_type><ignore_local_control>0</ignore_local_control><reappearable>0</reappearable></entrance_show_control></entrance><tab_info><default_key if_has_native_banner="">gameindex</default_key><red_dot><key></key></red_dot></tab_info><extra_data><preload><![CDATA[{&quot;feed&quot;:{&quot;type&quot;:27,&quot;title&quot;:&quot;《冒险岛：枫之传说》双通道冲刺巅峰！\u200b&quot;,&quot;tag_name&quot;:&quot;&quot;,&quot;pic_url_list&quot;:[],&quot;lazy_pic_url_list&quot;:[],&quot;image_list&quot;:[{&quot;url&quot;:&quot;https://mmgame.qpic.cn/image/02b270953a902230cecce3667701317f35e3bdbec4ba1f1b45492075d8a2c4e6/0&quot;}],&quot;ext_image_list&quot;:[],&quot;is_thumbnail&quot;:true,&quot;id&quot;:196408,&quot;content_id&quot;:&quot;196408&quot;,&quot;extern_info&quot;:&quot;{\\&quot;ReasonID\\&quot;:19,\\&quot;GiftType\\&quot;:2,\\&quot;ContentType\\&quot;:61,\\&quot;red_dot_id\\&quot;: 196408, \\&quot;task_name\\&quot;:\\&quot;recom_game_card\\&quot;,\\&quot;red_idea_id\\&quot;:0}&quot;,&quot;action_info&quot;:{&quot;type&quot;:5,&quot;jump_url&quot;:&quot;https://game.weixin.qq.com/cgi-bin/actnew/newportalact/215393/PVPbDWG5WgcWpUU7o2xV-QA5hTHj1C3_4nmE_ajr0p8/main_page?act_id=215393&amp;k=PVPbDWG5WgcWpUU7o2xV-QA5hTHj1C3_4nmE_ajr0p8&amp;pid=main_page&amp;noticeid=144849100#wechat_redirect&quot;},&quot;user_info&quot;:{&quot;nick_name&quot;:&quot;冒险岛：枫之传说&quot;,&quot;head_img_url&quot;:&quot;https://mmgame.qpic.cn/image/7727a0de215ead9803cd0ccf2875d5a8c54d0c2141fab1e79f810bd158fe39cb/0&quot;,&quot;user_name&quot;:&quot;U1_BgAAr8QAwK7r@gamelife&quot;,&quot;negative_type&quot;:2,&quot;tag_list&quot;:[]},&quot;cluster_info_list&quot;:[],&quot;content_tag_on_screen_list&quot;:[],&quot;need_only_show&quot;:true,&quot;is_red_dot&quot;:true,&quot;video_list&quot;:[],&quot;appid&quot;:&quot;wx041c36892884eb22&quot;,&quot;msg_pack_extend_list&quot;:[],&quot;topic_feed_list&quot;:[],&quot;direct_launch_game&quot;:false,&quot;service_entrance_list&quot;:[],&quot;user_list&quot;:[],&quot;cover_pic_list&quot;:[],&quot;content_entrance_list&quot;:[],&quot;game_list&quot;:[]},&quot;type&quot;:12}]]></preload></extra_data><report><msg_subtype>10</msg_subtype><noticeid>144849100</noticeid><ext_data>{&quot;biz_subtype&quot;:6,&quot;content_id&quot;:&quot;9007199254937400&quot;,&quot;exp_data&quot;:&quot;&quot;,&quot;friend_uin&quot;:0,&quot;interactive&quot;:800504,&quot;recommend_data&quot;:&quot;wxg_gamecenter_mmgame_redpoint_recommend_20220829:2024101804;wxg_gamecenter_mmgame_redpoint_recommend_rerank_20230601:2024022202&quot;,&quot;send_time&quot;:1754407306,&quot;text&quot;:&quot;您被移出“光之阵营”&quot;,&quot;video_id&quot;:&quot;&quot;}</ext_data><business_data>{&quot;biz_type&quot;:12,&quot;data&quot;:&quot;{\\&quot;notice_id\\&quot;:\\&quot;144849100\\&quot;,\\&quot;msg_id\\&quot;:\\&quot;zRpN_BDYyqul-dCjuRvIag\\&quot;}&quot;,&quot;sys_report&quot;:{&quot;msg_type&quot;:10,&quot;interactive&quot;:800504,&quot;biz_subtype&quot;:6}}</business_data></report><jump_info></jump_info><exposure_strategy><history_message_list><msg_id>mmgamemessagedistribution_recall_wx95a3a4d7c627e07d_1031335450_1754254036</msg_id><click_score>92.290344</click_score></history_message_list><history_message_list><msg_id>mmgamemessagedistribution_recall_wx09a75a3a7bb79b59_1034263614_1754254036</msg_id><click_score>57.041645</click_score></history_message_list><history_message_list><msg_id>unifiedpush_content_278348_1753027199.6_145076126_9007199255019340</msg_id><click_score>19.353628</click_score></history_message_list><history_message_list><msg_id>unifiedpush_content_176672_1742745599.6_144784085_9007199254917664</msg_id><click_score>18.897652</click_score></history_message_list><expire_time_strategy><enabled></enabled></expire_time_strategy><battery_strategy><enabled></enabled></battery_strategy><wifi_strategy><enabled></enabled></wifi_strategy><exposure_count>4</exposure_count><channel>2</channel><click_score>39.798019</click_score><ignore_exceed_exposure>1</ignore_exceed_exposure></exposure_strategy><verify_info><signature>BE2F32C352EDE59510F17F7DA597321352DCA99615DEA8A1AE2EA8081589F3A4</signature><nonce>yfrdvblg</nonce><timestamp>1754407305</timestamp><version>1</version></verify_info></gamecenter></sysmsg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407306, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6362833485525755306, 'MsgSeq': 871430245}
2025-08-05 23:21:33 | DEBUG | 系统消息类型: gamecenter
2025-08-05 23:21:33 | INFO | 未知的系统消息类型: {'MsgId': 662859893, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<sysmsg type="gamecenter"><gamecenter msgtype="100" newmsgtype="100"><msg_id>unifiedpush_content_196408_1751817599.6_144849100_9007199254937400</msg_id><appid>wx041c36892884eb22</appid><push_id>unifiedpush_content_196408</push_id><weight>6</weight><time_info><create_time>1754407305</create_time><expire_time>259200</expire_time></time_info><entrance><entrance_red_dot_type>4</entrance_red_dot_type><entrance_icon_url>https://mmgame.qpic.cn/image/73d81c459e8f5d51b02b66516bd6f275a77353c948ae1e799b50057a576d9ec2/h64</entrance_icon_url><entrance_text>您被移出“光之阵营”</entrance_text><entrance_icon_rounded_corner>1</entrance_icon_rounded_corner><entrance_show_control><basic_type>10000</basic_type><ignore_local_control>0</ignore_local_control><reappearable>0</reappearable></entrance_show_control></entrance><tab_info><default_key if_has_native_banner="">gameindex</default_key><red_dot><key></key></red_dot></tab_info><extra_data><preload><![CDATA[{&quot;feed&quot;:{&quot;type&quot;:27,&quot;title&quot;:&quot;《冒险岛：枫之传说》双通道冲刺巅峰！\u200b&quot;,&quot;tag_name&quot;:&quot;&quot;,&quot;pic_url_list&quot;:[],&quot;lazy_pic_url_list&quot;:[],&quot;image_list&quot;:[{&quot;url&quot;:&quot;https://mmgame.qpic.cn/image/02b270953a902230cecce3667701317f35e3bdbec4ba1f1b45492075d8a2c4e6/0&quot;}],&quot;ext_image_list&quot;:[],&quot;is_thumbnail&quot;:true,&quot;id&quot;:196408,&quot;content_id&quot;:&quot;196408&quot;,&quot;extern_info&quot;:&quot;{\\&quot;ReasonID\\&quot;:19,\\&quot;GiftType\\&quot;:2,\\&quot;ContentType\\&quot;:61,\\&quot;red_dot_id\\&quot;: 196408, \\&quot;task_name\\&quot;:\\&quot;recom_game_card\\&quot;,\\&quot;red_idea_id\\&quot;:0}&quot;,&quot;action_info&quot;:{&quot;type&quot;:5,&quot;jump_url&quot;:&quot;https://game.weixin.qq.com/cgi-bin/actnew/newportalact/215393/PVPbDWG5WgcWpUU7o2xV-QA5hTHj1C3_4nmE_ajr0p8/main_page?act_id=215393&amp;k=PVPbDWG5WgcWpUU7o2xV-QA5hTHj1C3_4nmE_ajr0p8&amp;pid=main_page&amp;noticeid=144849100#wechat_redirect&quot;},&quot;user_info&quot;:{&quot;nick_name&quot;:&quot;冒险岛：枫之传说&quot;,&quot;head_img_url&quot;:&quot;https://mmgame.qpic.cn/image/7727a0de215ead9803cd0ccf2875d5a8c54d0c2141fab1e79f810bd158fe39cb/0&quot;,&quot;user_name&quot;:&quot;U1_BgAAr8QAwK7r@gamelife&quot;,&quot;negative_type&quot;:2,&quot;tag_list&quot;:[]},&quot;cluster_info_list&quot;:[],&quot;content_tag_on_screen_list&quot;:[],&quot;need_only_show&quot;:true,&quot;is_red_dot&quot;:true,&quot;video_list&quot;:[],&quot;appid&quot;:&quot;wx041c36892884eb22&quot;,&quot;msg_pack_extend_list&quot;:[],&quot;topic_feed_list&quot;:[],&quot;direct_launch_game&quot;:false,&quot;service_entrance_list&quot;:[],&quot;user_list&quot;:[],&quot;cover_pic_list&quot;:[],&quot;content_entrance_list&quot;:[],&quot;game_list&quot;:[]},&quot;type&quot;:12}]]></preload></extra_data><report><msg_subtype>10</msg_subtype><noticeid>144849100</noticeid><ext_data>{&quot;biz_subtype&quot;:6,&quot;content_id&quot;:&quot;9007199254937400&quot;,&quot;exp_data&quot;:&quot;&quot;,&quot;friend_uin&quot;:0,&quot;interactive&quot;:800504,&quot;recommend_data&quot;:&quot;wxg_gamecenter_mmgame_redpoint_recommend_20220829:2024101804;wxg_gamecenter_mmgame_redpoint_recommend_rerank_20230601:2024022202&quot;,&quot;send_time&quot;:1754407306,&quot;text&quot;:&quot;您被移出“光之阵营”&quot;,&quot;video_id&quot;:&quot;&quot;}</ext_data><business_data>{&quot;biz_type&quot;:12,&quot;data&quot;:&quot;{\\&quot;notice_id\\&quot;:\\&quot;144849100\\&quot;,\\&quot;msg_id\\&quot;:\\&quot;zRpN_BDYyqul-dCjuRvIag\\&quot;}&quot;,&quot;sys_report&quot;:{&quot;msg_type&quot;:10,&quot;interactive&quot;:800504,&quot;biz_subtype&quot;:6}}</business_data></report><jump_info></jump_info><exposure_strategy><history_message_list><msg_id>mmgamemessagedistribution_recall_wx95a3a4d7c627e07d_1031335450_1754254036</msg_id><click_score>92.290344</click_score></history_message_list><history_message_list><msg_id>mmgamemessagedistribution_recall_wx09a75a3a7bb79b59_1034263614_1754254036</msg_id><click_score>57.041645</click_score></history_message_list><history_message_list><msg_id>unifiedpush_content_278348_1753027199.6_145076126_9007199255019340</msg_id><click_score>19.353628</click_score></history_message_list><history_message_list><msg_id>unifiedpush_content_176672_1742745599.6_144784085_9007199254917664</msg_id><click_score>18.897652</click_score></history_message_list><expire_time_strategy><enabled></enabled></expire_time_strategy><battery_strategy><enabled></enabled></battery_strategy><wifi_strategy><enabled></enabled></wifi_strategy><exposure_count>4</exposure_count><channel>2</channel><click_score>39.798019</click_score><ignore_exceed_exposure>1</ignore_exceed_exposure></exposure_strategy><verify_info><signature>BE2F32C352EDE59510F17F7DA597321352DCA99615DEA8A1AE2EA8081589F3A4</signature><nonce>yfrdvblg</nonce><timestamp>1754407305</timestamp><version>1</version></verify_info></gamecenter></sysmsg>', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407306, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6362833485525755306, 'MsgSeq': 871430245, 'FromWxid': 'weixin', 'SenderWxid': 'weixin', 'IsGroup': False}
2025-08-05 23:21:34 | DEBUG | 收到消息: {'MsgId': 1343651223, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<sysmsg type="gamecenter"><gamecenter msgtype="100" newmsgtype="100"><msg_id>unifiedpush_content_286016_1753631999.6_145097094_9007199255027008</msg_id><appid>wx95a3a4d7c627e07d</appid><push_id>unifiedpush_content_286016</push_id><weight>6</weight><time_info><create_time>1754407305</create_time><expire_time>259200</expire_time></time_info><entrance><entrance_red_dot_type>4</entrance_red_dot_type><entrance_icon_url>https://mmocgame.qpic.cn/wechatgame/duc2TvpEgSR53WIVgEfYglMDO8O4iaficqga0EgQichficmUAp9Ydzb0nOggezEttDMJ/0</entrance_icon_url><entrance_text>体验马可波罗皮肤的丝滑</entrance_text><entrance_icon_rounded_corner>1</entrance_icon_rounded_corner><entrance_show_control><basic_type>10000</basic_type><ignore_local_control>0</ignore_local_control><reappearable>0</reappearable></entrance_show_control></entrance><tab_info><default_key if_has_native_banner="">gameindex</default_key><red_dot><key></key></red_dot></tab_info><extra_data><preload><![CDATA[{&quot;feed&quot;:{&quot;type&quot;:48,&quot;title&quot;:&quot;&quot;,&quot;pic_url_list&quot;:[],&quot;lazy_pic_url_list&quot;:[],&quot;image_list&quot;:[],&quot;ext_image_list&quot;:[],&quot;id&quot;:1037276126,&quot;content_id&quot;:&quot;1037276126_20250801&quot;,&quot;cluster_info_list&quot;:[],&quot;content_tag_on_screen_list&quot;:[],&quot;is_red_dot&quot;:true,&quot;video_list&quot;:[],&quot;appid&quot;:&quot;wx95a3a4d7c627e07d&quot;,&quot;msg_pack_extend_list&quot;:[],&quot;topic_feed_list&quot;:[],&quot;service_entrance_list&quot;:[],&quot;user_list&quot;:[],&quot;cover_pic_list&quot;:[],&quot;content_entrance_list&quot;:[],&quot;rich_card&quot;:{&quot;appid&quot;:&quot;wx95a3a4d7c627e07d&quot;,&quot;game_info&quot;:{&quot;type&quot;:1,&quot;app_game&quot;:{&quot;appitem&quot;:{&quot;AppID&quot;:&quot;wx95a3a4d7c627e07d&quot;,&quot;IconURL&quot;:&quot;https://mmocgame.qpic.cn/wechatgame/duc2TvpEgSRNdK802WzT8QyQESjZXhPxznaConvnaPsBboJCLy1FiawbJIvQgdPTm/0&quot;,&quot;Name&quot;:&quot;王者荣耀&quot;,&quot;BriefName&quot;:&quot;王者荣耀&quot;,&quot;Label&quot;:[],&quot;game_tag_list&quot;:[],&quot;package_name_list&quot;:[],&quot;monetization_type_list&quot;:[]},&quot;icon_url_list&quot;:[],&quot;recom_list&quot;:[],&quot;gift_icon_url_list&quot;:[],&quot;nick_name_list&quot;:[]}},&quot;list&quot;:[{&quot;item_id&quot;:&quot;1037276126&quot;,&quot;title&quot;:&quot;&quot;,&quot;type&quot;:0,&quot;type_desc&quot;:&quot;热帖&quot;,&quot;pic_url_list&quot;:[],&quot;jump_url&quot;:&quot;&quot;,&quot;video_info&quot;:{&quot;snapshot&quot;:&quot;&quot;,&quot;url&quot;:&quot;&quot;,&quot;vid&quot;:&quot;&quot;}}]},&quot;game_list&quot;:[]},&quot;type&quot;:32}]]></preload></extra_data><report><msg_subtype>10</msg_subtype><noticeid>145097094</noticeid><ext_data>{&quot;biz_subtype&quot;:6,&quot;content_id&quot;:&quot;9007199255027008&quot;,&quot;exp_data&quot;:&quot;&quot;,&quot;friend_uin&quot;:0,&quot;interactive&quot;:1008,&quot;recommend_data&quot;:&quot;wxg_gamecenter_mmgame_redpoint_recommend_20220829:2024101804;wxg_gamecenter_mmgame_redpoint_recommend_rerank_20230601:2024022202&quot;,&quot;send_time&quot;:1754407306,&quot;text&quot;:&quot;体验马可波罗皮肤的丝滑&quot;,&quot;video_id&quot;:&quot;&quot;}</ext_data><business_data>{&quot;biz_type&quot;:32,&quot;data&quot;:&quot;{\\&quot;notice_id\\&quot;:\\&quot;145097094\\&quot;,\\&quot;msg_id\\&quot;:\\&quot;wx95a3a4d7c627e07d\\&quot;,\\&quot;topic_id\\&quot;:1037276126,\\&quot;tab_id\\&quot;:6,\\&quot;idea_midea_id\\&quot;:27021597764222993,\\&quot;idea_sub_id\\&quot;:27021597764395104}&quot;,&quot;sys_report&quot;:{&quot;msg_type&quot;:10,&quot;interactive&quot;:1008,&quot;biz_subtype&quot;:6}}</business_data></report><jump_info></jump_info><exposure_strategy><history_message_list><msg_id>mmgamemessagedistribution_recall_wx95a3a4d7c627e07d_1031335450_1754254036</msg_id><click_score>92.163681</click_score></history_message_list><history_message_list><msg_id>mmgamemessagedistribution_recall_wx09a75a3a7bb79b59_1034263614_1754254036</msg_id><click_score>56.937336</click_score></history_message_list><history_message_list><msg_id>unifiedpush_content_278348_1753027199.6_145076126_9007199255019340</msg_id><click_score>22.098423</click_score></history_message_list><history_message_list><msg_id>unifiedpush_content_176672_1742745599.6_144784085_9007199254917664</msg_id><click_score>19.329786</click_score></history_message_list><expire_time_strategy><enabled></enabled></expire_time_strategy><battery_strategy><enabled></enabled></battery_strategy><wifi_strategy><enabled></enabled></wifi_strategy><exposure_count>4</exposure_count><channel>2</channel><click_score>18.870831</click_score><ignore_exceed_exposure>1</ignore_exceed_exposure></exposure_strategy><verify_info><signature>EBA2663FF93C551E22C891CBCBF920E568BC2FB1E68FE20A22F5243BBD3B3D86</signature><nonce>tqappubl</nonce><timestamp>1754407305</timestamp><version>1</version></verify_info></gamecenter></sysmsg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407306, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3892665104002546998, 'MsgSeq': 871430246}
2025-08-05 23:21:34 | DEBUG | 系统消息类型: gamecenter
2025-08-05 23:21:34 | INFO | 未知的系统消息类型: {'MsgId': 1343651223, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<sysmsg type="gamecenter"><gamecenter msgtype="100" newmsgtype="100"><msg_id>unifiedpush_content_286016_1753631999.6_145097094_9007199255027008</msg_id><appid>wx95a3a4d7c627e07d</appid><push_id>unifiedpush_content_286016</push_id><weight>6</weight><time_info><create_time>1754407305</create_time><expire_time>259200</expire_time></time_info><entrance><entrance_red_dot_type>4</entrance_red_dot_type><entrance_icon_url>https://mmocgame.qpic.cn/wechatgame/duc2TvpEgSR53WIVgEfYglMDO8O4iaficqga0EgQichficmUAp9Ydzb0nOggezEttDMJ/0</entrance_icon_url><entrance_text>体验马可波罗皮肤的丝滑</entrance_text><entrance_icon_rounded_corner>1</entrance_icon_rounded_corner><entrance_show_control><basic_type>10000</basic_type><ignore_local_control>0</ignore_local_control><reappearable>0</reappearable></entrance_show_control></entrance><tab_info><default_key if_has_native_banner="">gameindex</default_key><red_dot><key></key></red_dot></tab_info><extra_data><preload><![CDATA[{&quot;feed&quot;:{&quot;type&quot;:48,&quot;title&quot;:&quot;&quot;,&quot;pic_url_list&quot;:[],&quot;lazy_pic_url_list&quot;:[],&quot;image_list&quot;:[],&quot;ext_image_list&quot;:[],&quot;id&quot;:1037276126,&quot;content_id&quot;:&quot;1037276126_20250801&quot;,&quot;cluster_info_list&quot;:[],&quot;content_tag_on_screen_list&quot;:[],&quot;is_red_dot&quot;:true,&quot;video_list&quot;:[],&quot;appid&quot;:&quot;wx95a3a4d7c627e07d&quot;,&quot;msg_pack_extend_list&quot;:[],&quot;topic_feed_list&quot;:[],&quot;service_entrance_list&quot;:[],&quot;user_list&quot;:[],&quot;cover_pic_list&quot;:[],&quot;content_entrance_list&quot;:[],&quot;rich_card&quot;:{&quot;appid&quot;:&quot;wx95a3a4d7c627e07d&quot;,&quot;game_info&quot;:{&quot;type&quot;:1,&quot;app_game&quot;:{&quot;appitem&quot;:{&quot;AppID&quot;:&quot;wx95a3a4d7c627e07d&quot;,&quot;IconURL&quot;:&quot;https://mmocgame.qpic.cn/wechatgame/duc2TvpEgSRNdK802WzT8QyQESjZXhPxznaConvnaPsBboJCLy1FiawbJIvQgdPTm/0&quot;,&quot;Name&quot;:&quot;王者荣耀&quot;,&quot;BriefName&quot;:&quot;王者荣耀&quot;,&quot;Label&quot;:[],&quot;game_tag_list&quot;:[],&quot;package_name_list&quot;:[],&quot;monetization_type_list&quot;:[]},&quot;icon_url_list&quot;:[],&quot;recom_list&quot;:[],&quot;gift_icon_url_list&quot;:[],&quot;nick_name_list&quot;:[]}},&quot;list&quot;:[{&quot;item_id&quot;:&quot;1037276126&quot;,&quot;title&quot;:&quot;&quot;,&quot;type&quot;:0,&quot;type_desc&quot;:&quot;热帖&quot;,&quot;pic_url_list&quot;:[],&quot;jump_url&quot;:&quot;&quot;,&quot;video_info&quot;:{&quot;snapshot&quot;:&quot;&quot;,&quot;url&quot;:&quot;&quot;,&quot;vid&quot;:&quot;&quot;}}]},&quot;game_list&quot;:[]},&quot;type&quot;:32}]]></preload></extra_data><report><msg_subtype>10</msg_subtype><noticeid>145097094</noticeid><ext_data>{&quot;biz_subtype&quot;:6,&quot;content_id&quot;:&quot;9007199255027008&quot;,&quot;exp_data&quot;:&quot;&quot;,&quot;friend_uin&quot;:0,&quot;interactive&quot;:1008,&quot;recommend_data&quot;:&quot;wxg_gamecenter_mmgame_redpoint_recommend_20220829:2024101804;wxg_gamecenter_mmgame_redpoint_recommend_rerank_20230601:2024022202&quot;,&quot;send_time&quot;:1754407306,&quot;text&quot;:&quot;体验马可波罗皮肤的丝滑&quot;,&quot;video_id&quot;:&quot;&quot;}</ext_data><business_data>{&quot;biz_type&quot;:32,&quot;data&quot;:&quot;{\\&quot;notice_id\\&quot;:\\&quot;145097094\\&quot;,\\&quot;msg_id\\&quot;:\\&quot;wx95a3a4d7c627e07d\\&quot;,\\&quot;topic_id\\&quot;:1037276126,\\&quot;tab_id\\&quot;:6,\\&quot;idea_midea_id\\&quot;:27021597764222993,\\&quot;idea_sub_id\\&quot;:27021597764395104}&quot;,&quot;sys_report&quot;:{&quot;msg_type&quot;:10,&quot;interactive&quot;:1008,&quot;biz_subtype&quot;:6}}</business_data></report><jump_info></jump_info><exposure_strategy><history_message_list><msg_id>mmgamemessagedistribution_recall_wx95a3a4d7c627e07d_1031335450_1754254036</msg_id><click_score>92.163681</click_score></history_message_list><history_message_list><msg_id>mmgamemessagedistribution_recall_wx09a75a3a7bb79b59_1034263614_1754254036</msg_id><click_score>56.937336</click_score></history_message_list><history_message_list><msg_id>unifiedpush_content_278348_1753027199.6_145076126_9007199255019340</msg_id><click_score>22.098423</click_score></history_message_list><history_message_list><msg_id>unifiedpush_content_176672_1742745599.6_144784085_9007199254917664</msg_id><click_score>19.329786</click_score></history_message_list><expire_time_strategy><enabled></enabled></expire_time_strategy><battery_strategy><enabled></enabled></battery_strategy><wifi_strategy><enabled></enabled></wifi_strategy><exposure_count>4</exposure_count><channel>2</channel><click_score>18.870831</click_score><ignore_exceed_exposure>1</ignore_exceed_exposure></exposure_strategy><verify_info><signature>EBA2663FF93C551E22C891CBCBF920E568BC2FB1E68FE20A22F5243BBD3B3D86</signature><nonce>tqappubl</nonce><timestamp>1754407305</timestamp><version>1</version></verify_info></gamecenter></sysmsg>', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407306, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3892665104002546998, 'MsgSeq': 871430246, 'FromWxid': 'weixin', 'SenderWxid': 'weixin', 'IsGroup': False}
2025-08-05 23:21:38 | DEBUG | 收到消息: {'MsgId': 592076450, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14647389333596608700</objectId>\n\t\t\t<objectNonceId>11259274521706818355_6_25_12_3_1754407023357510_3d97fad0-720f-11f0-b767-23ad69bcee97</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>趣房首席体验官</nickname>\n\t\t\t<username>v2_060000231003b20faec8c7e48111c5d1ce07ee30b0772671a9cb2fb50ff81a9964765db86e5a@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/EWAic18fbafOXBWicKwyXXjZMZEcYHIiboO5nOLGPQW0cSh8VkmU0ic63R2iaRa9UJp7IWchwnWic3aiaAwCmaMlemlpMYq6b2oefKzaDO5To3rfLVERgicCOib5ibUR5qw350pXLe/0]]></avatar>\n\t\t\t<desc>与众不同的入户门凌驾于拐梯之上</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>1</authIconType>\n\t\t\t<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDeia67uRwOBl1t3EsImhz2YgCicm22ibib84s3rr03tMMxIP8o1GAFSFIsfrHDbC8MunyOsmBbHibN1FSROwBKcGveO&hy=SH&idx=1&m=&uzid=7a22e&token=6xykWLEnztJD1V02HxcJfQ7Hco5xFmNUnQtFmUOz0Iph0QsiaiaiccUJTWbjpY2NrGr4NurusjiaYfBbE1f5tCzpHbvAYbmibaiaRkH7L2XjEEhJh7nDfPF2zb2EFxoRD8wlkMuCmickrYkhd03TuMGHRRf6KbH5NkibpLQfI6DVmQYahPg&basedata=CAESBnhXVDEyMRoGeFdUMTExGgZ4V1QxMTIaBnhXVDExMxoGeFdUMTI2GgZ4V1QxMjcaBnhXVDEyMRoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwjoHBAAGAI&sign=6Ce-_0ySsLD5Pb0IxUIS8OtwO40HYV944SUn2vUrsMq9COsTg2xWRIkYsar2CgdlLRKz6fVw25ziv3fpf5bcng&ctsc=25&extg=10ab100&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAAC1LWz6IfQp%2BfBBcSCSaBAAAADnaHZTnGbFfAj9RgZXfw6VbiP5bSpDFQkBbNrcvp43na0IXhrimU4OPhNjGUrGMd0j6ALLNuDFlQ%3D%3D&svrnonce=1754407025]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvLPrMKZvIOgB22NAjusX4Kt4OAq2s95iaBvToolGiayicrK4wzuQRvdu4PYKfkbc7LjzkmLQ58vtufPIQrQNz0Hoatyuly5B9ia4wZknHFUJVnj0&hy=SH&idx=1&m=4ab7ded81197cd3d543a68a3153f64d5&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxgpYJdxicibJ5SsX0zpic0pqse4fBXmxhnHZYovRBdGrIcv7MyKxWJZfXSGdHN0ARZnmmUE3pkxzfTPic5T4kkuDecEufIgwNGPEGcmkmJfzounsWIPMeQPIGcGrkgicnXOwpibL9Zxic9CJZnO&ctsc=2-25]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvLPrMKZvIOgB22NAjusX4Kt4OAq2s95iaBvToolGiayicrK4wzuQRvdu4PYKfkbc7LjzkmLQ58vtufPIQrQNz0Hoatyuly5B9ia4wZknHFUJVnj0&hy=SH&idx=1&m=4ab7ded81197cd3d543a68a3153f64d5&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxgpYJdxicibJ5SsX0zpic0pqse4fBXmxhnHZYovRBdGrIcv7MyKxWJZfXSGdHN0ARZnmmUE3pkxzfTPic5T4kkuDecEufIgwNGPEGcmkmJfzounsWIPMeQPIGcGrkgicnXOwpibL9Zxic9CJZnO&ctsc=2-25]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1080.0</width>\n\t\t\t\t\t<height>1920.0</height>\n\t\t\t\t\t<videoPlayDuration>158</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>25</sourceCommentScene>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407310, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>8221878f18fc8a12c5aebb65ebff0bbc_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_7lNVFnX4|v1_R+JoyNjL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 6719474478460137522, 'MsgSeq': 871430247}
2025-08-05 23:21:38 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-08-05 23:21:38 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14647389333596608700</objectId>
			<objectNonceId>11259274521706818355_6_25_12_3_1754407023357510_3d97fad0-720f-11f0-b767-23ad69bcee97</objectNonceId>
			<feedType>4</feedType>
			<nickname>趣房首席体验官</nickname>
			<username>v2_060000231003b20faec8c7e48111c5d1ce07ee30b0772671a9cb2fb50ff81a9964765db86e5a@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/EWAic18fbafOXBWicKwyXXjZMZEcYHIiboO5nOLGPQW0cSh8VkmU0ic63R2iaRa9UJp7IWchwnWic3aiaAwCmaMlemlpMYq6b2oefKzaDO5To3rfLVERgicCOib5ibUR5qw350pXLe/0]]></avatar>
			<desc>与众不同的入户门凌驾于拐梯之上</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>1</authIconType>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDeia67uRwOBl1t3EsImhz2YgCicm22ibib84s3rr03tMMxIP8o1GAFSFIsfrHDbC8MunyOsmBbHibN1FSROwBKcGveO&hy=SH&idx=1&m=&uzid=7a22e&token=6xykWLEnztJD1V02HxcJfQ7Hco5xFmNUnQtFmUOz0Iph0QsiaiaiccUJTWbjpY2NrGr4NurusjiaYfBbE1f5tCzpHbvAYbmibaiaRkH7L2XjEEhJh7nDfPF2zb2EFxoRD8wlkMuCmickrYkhd03TuMGHRRf6KbH5NkibpLQfI6DVmQYahPg&basedata=CAESBnhXVDEyMRoGeFdUMTExGgZ4V1QxMTIaBnhXVDExMxoGeFdUMTI2GgZ4V1QxMjcaBnhXVDEyMRoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwjoHBAAGAI&sign=6Ce-_0ySsLD5Pb0IxUIS8OtwO40HYV944SUn2vUrsMq9COsTg2xWRIkYsar2CgdlLRKz6fVw25ziv3fpf5bcng&ctsc=25&extg=10ab100&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAAC1LWz6IfQp%2BfBBcSCSaBAAAADnaHZTnGbFfAj9RgZXfw6VbiP5bSpDFQkBbNrcvp43na0IXhrimU4OPhNjGUrGMd0j6ALLNuDFlQ%3D%3D&svrnonce=1754407025]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvLPrMKZvIOgB22NAjusX4Kt4OAq2s95iaBvToolGiayicrK4wzuQRvdu4PYKfkbc7LjzkmLQ58vtufPIQrQNz0Hoatyuly5B9ia4wZknHFUJVnj0&hy=SH&idx=1&m=4ab7ded81197cd3d543a68a3153f64d5&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxgpYJdxicibJ5SsX0zpic0pqse4fBXmxhnHZYovRBdGrIcv7MyKxWJZfXSGdHN0ARZnmmUE3pkxzfTPic5T4kkuDecEufIgwNGPEGcmkmJfzounsWIPMeQPIGcGrkgicnXOwpibL9Zxic9CJZnO&ctsc=2-25]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvLPrMKZvIOgB22NAjusX4Kt4OAq2s95iaBvToolGiayicrK4wzuQRvdu4PYKfkbc7LjzkmLQ58vtufPIQrQNz0Hoatyuly5B9ia4wZknHFUJVnj0&hy=SH&idx=1&m=4ab7ded81197cd3d543a68a3153f64d5&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxgpYJdxicibJ5SsX0zpic0pqse4fBXmxhnHZYovRBdGrIcv7MyKxWJZfXSGdHN0ARZnmmUE3pkxzfTPic5T4kkuDecEufIgwNGPEGcmkmJfzounsWIPMeQPIGcGrkgicnXOwpibL9Zxic9CJZnO&ctsc=2-25]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>158</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>25</sourceCommentScene>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-05 23:21:38 | DEBUG | XML消息类型: 51
2025-08-05 23:21:38 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-05 23:21:38 | DEBUG | XML消息描述: None
2025-08-05 23:21:38 | DEBUG | 附件信息 totallen: 0
2025-08-05 23:21:38 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-05 23:21:38 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-05 23:21:38 | INFO | 未知的XML消息类型: 51
2025-08-05 23:21:38 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-05 23:21:38 | INFO | 消息描述: None
2025-08-05 23:21:38 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-05 23:21:38 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14647389333596608700</objectId>
			<objectNonceId>11259274521706818355_6_25_12_3_1754407023357510_3d97fad0-720f-11f0-b767-23ad69bcee97</objectNonceId>
			<feedType>4</feedType>
			<nickname>趣房首席体验官</nickname>
			<username>v2_060000231003b20faec8c7e48111c5d1ce07ee30b0772671a9cb2fb50ff81a9964765db86e5a@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/EWAic18fbafOXBWicKwyXXjZMZEcYHIiboO5nOLGPQW0cSh8VkmU0ic63R2iaRa9UJp7IWchwnWic3aiaAwCmaMlemlpMYq6b2oefKzaDO5To3rfLVERgicCOib5ibUR5qw350pXLe/0]]></avatar>
			<desc>与众不同的入户门凌驾于拐梯之上</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>1</authIconType>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDeia67uRwOBl1t3EsImhz2YgCicm22ibib84s3rr03tMMxIP8o1GAFSFIsfrHDbC8MunyOsmBbHibN1FSROwBKcGveO&hy=SH&idx=1&m=&uzid=7a22e&token=6xykWLEnztJD1V02HxcJfQ7Hco5xFmNUnQtFmUOz0Iph0QsiaiaiccUJTWbjpY2NrGr4NurusjiaYfBbE1f5tCzpHbvAYbmibaiaRkH7L2XjEEhJh7nDfPF2zb2EFxoRD8wlkMuCmickrYkhd03TuMGHRRf6KbH5NkibpLQfI6DVmQYahPg&basedata=CAESBnhXVDEyMRoGeFdUMTExGgZ4V1QxMTIaBnhXVDExMxoGeFdUMTI2GgZ4V1QxMjcaBnhXVDEyMRoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwjoHBAAGAI&sign=6Ce-_0ySsLD5Pb0IxUIS8OtwO40HYV944SUn2vUrsMq9COsTg2xWRIkYsar2CgdlLRKz6fVw25ziv3fpf5bcng&ctsc=25&extg=10ab100&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAAC1LWz6IfQp%2BfBBcSCSaBAAAADnaHZTnGbFfAj9RgZXfw6VbiP5bSpDFQkBbNrcvp43na0IXhrimU4OPhNjGUrGMd0j6ALLNuDFlQ%3D%3D&svrnonce=1754407025]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvLPrMKZvIOgB22NAjusX4Kt4OAq2s95iaBvToolGiayicrK4wzuQRvdu4PYKfkbc7LjzkmLQ58vtufPIQrQNz0Hoatyuly5B9ia4wZknHFUJVnj0&hy=SH&idx=1&m=4ab7ded81197cd3d543a68a3153f64d5&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxgpYJdxicibJ5SsX0zpic0pqse4fBXmxhnHZYovRBdGrIcv7MyKxWJZfXSGdHN0ARZnmmUE3pkxzfTPic5T4kkuDecEufIgwNGPEGcmkmJfzounsWIPMeQPIGcGrkgicnXOwpibL9Zxic9CJZnO&ctsc=2-25]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvLPrMKZvIOgB22NAjusX4Kt4OAq2s95iaBvToolGiayicrK4wzuQRvdu4PYKfkbc7LjzkmLQ58vtufPIQrQNz0Hoatyuly5B9ia4wZknHFUJVnj0&hy=SH&idx=1&m=4ab7ded81197cd3d543a68a3153f64d5&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxgpYJdxicibJ5SsX0zpic0pqse4fBXmxhnHZYovRBdGrIcv7MyKxWJZfXSGdHN0ARZnmmUE3pkxzfTPic5T4kkuDecEufIgwNGPEGcmkmJfzounsWIPMeQPIGcGrkgicnXOwpibL9Zxic9CJZnO&ctsc=2-25]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>158</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>25</sourceCommentScene>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-05 23:22:21 | DEBUG | 收到消息: {'MsgId': 678975259, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n8w姐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407352, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_uECC17ZE|v1_yFQk9amb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1504415114428526564, 'MsgSeq': 871430248}
2025-08-05 23:22:21 | INFO | 收到文本消息: 消息ID:678975259 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:8w姐
2025-08-05 23:22:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '8w姐' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-05 23:22:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['8w姐']
2025-08-05 23:22:21 | DEBUG | 处理消息内容: '8w姐'
2025-08-05 23:22:21 | DEBUG | 消息内容 '8w姐' 不匹配任何命令，忽略
2025-08-05 23:22:26 | DEBUG | 收到消息: {'MsgId': 1218619805, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="99ad225a3338891041c4c40162f5ff6d" len = "32290" productid="" androidmd5="99ad225a3338891041c4c40162f5ff6d" androidlen="32290" s60v3md5 = "99ad225a3338891041c4c40162f5ff6d" s60v3len="32290" s60v5md5 = "99ad225a3338891041c4c40162f5ff6d" s60v5len="32290" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=99ad225a3338891041c4c40162f5ff6d&amp;filekey=3043020101042f302d02016e040253480420393961643232356133333338383931303431633463343031363266356666366402027e22040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130313231323339343030303035303266336134356561663133373031623964303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4c70bb97fd9b1c39b381ed32fbe63d5a&amp;filekey=3043020101042f302d02016e040253480420346337306262393766643962316333396233383165643332666265363364356102027e30040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130313231323339343030303036343833376134356561663133373031623964303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey= "4da9e16429cb4c829199298e061c5af3" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=396f13334a7ecfd217049b94d83a79d5&amp;filekey=3043020101042f302d02016e0402534804203339366631333333346137656366643231373034396239346438336137396435020214f0040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130313231323339343030303037343730376134356561663133373031623964303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5 = "38c3f69d542f90c0646668d13a26fe5a" width= "227" height= "231" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "CgblnY/msLQ=" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407358, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_t5KTQcMM|v1_gXqbB9hd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7091398054407480300, 'MsgSeq': 871430249}
2025-08-05 23:22:26 | INFO | 收到表情消息: 消息ID:1218619805 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:99ad225a3338891041c4c40162f5ff6d 大小:32290
2025-08-05 23:22:26 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7091398054407480300
2025-08-05 23:22:33 | DEBUG | 收到消息: {'MsgId': 1946990528, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n那个木材房能刷几次啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407365, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_pJs1FOAc|v1_ypW/4djT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9113704738942246962, 'MsgSeq': 871430250}
2025-08-05 23:22:33 | INFO | 收到文本消息: 消息ID:1946990528 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:[] 内容:那个木材房能刷几次啊
2025-08-05 23:22:33 | DEBUG | [DouBaoImageToImage] 收到文本消息: '那个木材房能刷几次啊' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:22:33 | DEBUG | [DouBaoImageToImage] 命令解析: ['那个木材房能刷几次啊']
2025-08-05 23:22:33 | DEBUG | 处理消息内容: '那个木材房能刷几次啊'
2025-08-05 23:22:33 | DEBUG | 消息内容 '那个木材房能刷几次啊' 不匹配任何命令，忽略
2025-08-05 23:22:41 | DEBUG | 收到消息: {'MsgId': 2066769736, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n5'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407373, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_HM4efRXr|v1_r2itJFFo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7602280758764750305, 'MsgSeq': 871430251}
2025-08-05 23:22:41 | INFO | 收到文本消息: 消息ID:2066769736 来自:27852221909@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:5
2025-08-05 23:22:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '5' from wxid_xv01lkcmn48l22 in 27852221909@chatroom
2025-08-05 23:22:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['5']
2025-08-05 23:22:41 | DEBUG | [DoubaoVideoSearch] 数字选择超时，距离上次搜索: 1754407361.8秒
2025-08-05 23:22:41 | DEBUG | 处理消息内容: '5'
2025-08-05 23:22:41 | DEBUG | 消息内容 '5' 不匹配任何命令，忽略
2025-08-05 23:22:52 | DEBUG | 收到消息: {'MsgId': 850107917, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n这房子一千七离谱啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407384, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_UtGq4y6u|v1_fySlDyEF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 这房子一千七离谱啊', 'NewMsgId': 4815348193256720475, 'MsgSeq': 871430252}
2025-08-05 23:22:52 | INFO | 收到文本消息: 消息ID:850107917 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:这房子一千七离谱啊
2025-08-05 23:22:53 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这房子一千七离谱啊' from xiaomaochong in 48097389945@chatroom
2025-08-05 23:22:53 | DEBUG | [DouBaoImageToImage] 命令解析: ['这房子一千七离谱啊']
2025-08-05 23:22:53 | DEBUG | 处理消息内容: '这房子一千七离谱啊'
2025-08-05 23:22:53 | DEBUG | 消息内容 '这房子一千七离谱啊' 不匹配任何命令，忽略
2025-08-05 23:23:09 | DEBUG | 收到消息: {'MsgId': 728407958, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14712470838441478344</objectId>\n\t\t\t<objectNonceId>1030114285673390650_4_20_13_1_1754406938465474_0b19f6da-720f-11f0-aa99-9ddeb787a464</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>有料猫姐</nickname>\n\t\t\t<username>v2_060000231003b20faec8c7ea8e10c7d0c903ea35b07743b4809a829b2203b08886e2ffbdad70@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/Lbnjkg8IAaiaFnPURwU9YDvVnbEGN9gC0nEHEtqc2Xt7kFWwcfoRpwGia1mPwIU0M4VuhqKrpm2X4PiasGtYgZ5ZRvJq6WiaT08TM674bTtqS7eIXFutkZxBmszVd6o70icl3/0]]></avatar>\n\t\t\t<desc>我的兄弟\n你想不想干臭臭的东西\n挣香香的钱</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>0</authIconType>\n\t\t\t<authIconUrl><![CDATA[]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQAX83jonnXecQC6fVWmYRfyoVAWpOfq4FTFR6mJguGYCf3EL1I7eUibr9FpI922plP6rWUtXdduF758SrsxuHCgN&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a1fc&token=cztXnd9GyrHuZHabPTl5Ltk68K9SFoHspEVfmibZ580V8w1XSWLaVwFDqlicQEpuQWuVjicgXPyUWIkzEOMnfs5S6Ug6Et7sc1rhAv0MuBlhqQqhdWpqDnibb1XWYHljz7Piaubd4w2JMAAv1SmbenKTjhKiaeDtye7EnuJic1mwGjTzos&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjcaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=MzTTRdPdOMBERkl8_NJCz4xE_gHvkH5sjHRoAj5wCt1eV8YZ8jpdyVVqHEtZCIgVf89rEsrUguddvDC9_eLe5Q&ctsc=20&extg=108b900&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAAAvLwHacmQkEkVgHSCSaBAAAADnaHZTnGbFfAj9RgZXfw6VU9ilPIMKYhQ0J1NIFEnydlBHap1x0GsDNFX%2F6An7itvI8oZu92vJrPs%3D&svrnonce=1754406941]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzjor7IrvKZld1of10X44D4ia5Yqx5qw9sIykuh6tQMSTGYxbhQp2fpoj1oMtckKB9ZFryymb4YZr8Q5yFsiab5u9w&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxt5Oj7QqArgf2SOicSqZr6GoQRo0QW2HbwxzqmWBXzhQozQlxTyPVzynUVq58xBJetlibMReEWDr9PFsXicqg6H0QHhHvYSrHVd9oaegSMm0k0ribtHtBVdlL6Jib7G5cxW1aplSclP188n9f&ctsc=2-20]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzjor7IrvKZld1of10X44D4ia5Yqx5qw9sIykuh6tQMSTGYxbhQp2fpoj1oMtckKB9ZFryymb4YZr8Q5yFsiab5u9w&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxt5Oj7QqArgf2SOicSqZr6GoQRo0QW2HbwxzqmWBXzhQozQlxTyPVzynUVq58xBJetlibMReEWDr9PFsXicqg6H0QHhHvYSrHVd9oaegSMm0k0ribtHtBVdlL6Jib7G5cxW1aplSclP188n9f&ctsc=2-20]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1080.0</width>\n\t\t\t\t\t<height>1920.0</height>\n\t\t\t\t\t<videoPlayDuration>42</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>20</sourceCommentScene>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754406108849","contextId":"1-1-20-8da0a8d0bf5243b594d03e92681fd138","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407401, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>bd09586e15c068f5f213d47b03822ff5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_rkpX5JgE|v1_RTDpvJtG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 6483360603689821879, 'MsgSeq': 871430253}
2025-08-05 23:23:09 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-08-05 23:23:09 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14712470838441478344</objectId>
			<objectNonceId>1030114285673390650_4_20_13_1_1754406938465474_0b19f6da-720f-11f0-aa99-9ddeb787a464</objectNonceId>
			<feedType>4</feedType>
			<nickname>有料猫姐</nickname>
			<username>v2_060000231003b20faec8c7ea8e10c7d0c903ea35b07743b4809a829b2203b08886e2ffbdad70@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/Lbnjkg8IAaiaFnPURwU9YDvVnbEGN9gC0nEHEtqc2Xt7kFWwcfoRpwGia1mPwIU0M4VuhqKrpm2X4PiasGtYgZ5ZRvJq6WiaT08TM674bTtqS7eIXFutkZxBmszVd6o70icl3/0]]></avatar>
			<desc>我的兄弟
你想不想干臭臭的东西
挣香香的钱</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQAX83jonnXecQC6fVWmYRfyoVAWpOfq4FTFR6mJguGYCf3EL1I7eUibr9FpI922plP6rWUtXdduF758SrsxuHCgN&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a1fc&token=cztXnd9GyrHuZHabPTl5Ltk68K9SFoHspEVfmibZ580V8w1XSWLaVwFDqlicQEpuQWuVjicgXPyUWIkzEOMnfs5S6Ug6Et7sc1rhAv0MuBlhqQqhdWpqDnibb1XWYHljz7Piaubd4w2JMAAv1SmbenKTjhKiaeDtye7EnuJic1mwGjTzos&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjcaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=MzTTRdPdOMBERkl8_NJCz4xE_gHvkH5sjHRoAj5wCt1eV8YZ8jpdyVVqHEtZCIgVf89rEsrUguddvDC9_eLe5Q&ctsc=20&extg=108b900&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAAAvLwHacmQkEkVgHSCSaBAAAADnaHZTnGbFfAj9RgZXfw6VU9ilPIMKYhQ0J1NIFEnydlBHap1x0GsDNFX%2F6An7itvI8oZu92vJrPs%3D&svrnonce=1754406941]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzjor7IrvKZld1of10X44D4ia5Yqx5qw9sIykuh6tQMSTGYxbhQp2fpoj1oMtckKB9ZFryymb4YZr8Q5yFsiab5u9w&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxt5Oj7QqArgf2SOicSqZr6GoQRo0QW2HbwxzqmWBXzhQozQlxTyPVzynUVq58xBJetlibMReEWDr9PFsXicqg6H0QHhHvYSrHVd9oaegSMm0k0ribtHtBVdlL6Jib7G5cxW1aplSclP188n9f&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzjor7IrvKZld1of10X44D4ia5Yqx5qw9sIykuh6tQMSTGYxbhQp2fpoj1oMtckKB9ZFryymb4YZr8Q5yFsiab5u9w&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxt5Oj7QqArgf2SOicSqZr6GoQRo0QW2HbwxzqmWBXzhQozQlxTyPVzynUVq58xBJetlibMReEWDr9PFsXicqg6H0QHhHvYSrHVd9oaegSMm0k0ribtHtBVdlL6Jib7G5cxW1aplSclP188n9f&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>42</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754406108849","contextId":"1-1-20-8da0a8d0bf5243b594d03e92681fd138","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-05 23:23:09 | DEBUG | XML消息类型: 51
2025-08-05 23:23:09 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-05 23:23:09 | DEBUG | XML消息描述: None
2025-08-05 23:23:09 | DEBUG | 附件信息 totallen: 0
2025-08-05 23:23:09 | DEBUG | 附件信息 islargefilemsg: 0
2025-08-05 23:23:09 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-05 23:23:09 | INFO | 未知的XML消息类型: 51
2025-08-05 23:23:09 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-08-05 23:23:09 | INFO | 消息描述: None
2025-08-05 23:23:09 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-08-05 23:23:09 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14712470838441478344</objectId>
			<objectNonceId>1030114285673390650_4_20_13_1_1754406938465474_0b19f6da-720f-11f0-aa99-9ddeb787a464</objectNonceId>
			<feedType>4</feedType>
			<nickname>有料猫姐</nickname>
			<username>v2_060000231003b20faec8c7ea8e10c7d0c903ea35b07743b4809a829b2203b08886e2ffbdad70@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/Lbnjkg8IAaiaFnPURwU9YDvVnbEGN9gC0nEHEtqc2Xt7kFWwcfoRpwGia1mPwIU0M4VuhqKrpm2X4PiasGtYgZ5ZRvJq6WiaT08TM674bTtqS7eIXFutkZxBmszVd6o70icl3/0]]></avatar>
			<desc>我的兄弟
你想不想干臭臭的东西
挣香香的钱</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQAX83jonnXecQC6fVWmYRfyoVAWpOfq4FTFR6mJguGYCf3EL1I7eUibr9FpI922plP6rWUtXdduF758SrsxuHCgN&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a1fc&token=cztXnd9GyrHuZHabPTl5Ltk68K9SFoHspEVfmibZ580V8w1XSWLaVwFDqlicQEpuQWuVjicgXPyUWIkzEOMnfs5S6Ug6Et7sc1rhAv0MuBlhqQqhdWpqDnibb1XWYHljz7Piaubd4w2JMAAv1SmbenKTjhKiaeDtye7EnuJic1mwGjTzos&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjcaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=MzTTRdPdOMBERkl8_NJCz4xE_gHvkH5sjHRoAj5wCt1eV8YZ8jpdyVVqHEtZCIgVf89rEsrUguddvDC9_eLe5Q&ctsc=20&extg=108b900&ftype=606&svrbypass=AAuL%2FQsFAAABAAAAAAAvLwHacmQkEkVgHSCSaBAAAADnaHZTnGbFfAj9RgZXfw6VU9ilPIMKYhQ0J1NIFEnydlBHap1x0GsDNFX%2F6An7itvI8oZu92vJrPs%3D&svrnonce=1754406941]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzjor7IrvKZld1of10X44D4ia5Yqx5qw9sIykuh6tQMSTGYxbhQp2fpoj1oMtckKB9ZFryymb4YZr8Q5yFsiab5u9w&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxt5Oj7QqArgf2SOicSqZr6GoQRo0QW2HbwxzqmWBXzhQozQlxTyPVzynUVq58xBJetlibMReEWDr9PFsXicqg6H0QHhHvYSrHVd9oaegSMm0k0ribtHtBVdlL6Jib7G5cxW1aplSclP188n9f&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzjor7IrvKZld1of10X44D4ia5Yqx5qw9sIykuh6tQMSTGYxbhQp2fpoj1oMtckKB9ZFryymb4YZr8Q5yFsiab5u9w&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxt5Oj7QqArgf2SOicSqZr6GoQRo0QW2HbwxzqmWBXzhQozQlxTyPVzynUVq58xBJetlibMReEWDr9PFsXicqg6H0QHhHvYSrHVd9oaegSMm0k0ribtHtBVdlL6Jib7G5cxW1aplSclP188n9f&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>42</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1754406108849","contextId":"1-1-20-8da0a8d0bf5243b594d03e92681fd138","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-05 23:23:10 | DEBUG | 收到消息: {'MsgId': 535296370, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n我今天一直在那个房间刷春分'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407401, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_94A1uC2M|v1_77G9AQFj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1206479265262232843, 'MsgSeq': 871430254}
2025-08-05 23:23:10 | INFO | 收到文本消息: 消息ID:535296370 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:[] 内容:我今天一直在那个房间刷春分
2025-08-05 23:23:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我今天一直在那个房间刷春分' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:23:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['我今天一直在那个房间刷春分']
2025-08-05 23:23:10 | DEBUG | 处理消息内容: '我今天一直在那个房间刷春分'
2025-08-05 23:23:10 | DEBUG | 消息内容 '我今天一直在那个房间刷春分' 不匹配任何命令，忽略
2025-08-05 23:23:12 | DEBUG | 收到消息: {'MsgId': 2088412555, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n6'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407401, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_TlIFp2bI|v1_+EyHk6SX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5130653756567195249, 'MsgSeq': 871430255}
2025-08-05 23:23:12 | INFO | 收到文本消息: 消息ID:2088412555 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:6
2025-08-05 23:23:12 | DEBUG | [DouBaoImageToImage] 收到文本消息: '6' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-05 23:23:12 | DEBUG | [DouBaoImageToImage] 命令解析: ['6']
2025-08-05 23:23:12 | DEBUG | [DoubaoVideoSearch] 数字选择超时，距离上次搜索: 1754407392.6秒
2025-08-05 23:23:12 | DEBUG | 处理消息内容: '6'
2025-08-05 23:23:12 | DEBUG | 消息内容 '6' 不匹配任何命令，忽略
2025-08-05 23:23:18 | DEBUG | 收到消息: {'MsgId': 2012511621, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n我以为每轮都有呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407410, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_6lm2gUsw|v1_MAwHt4Ar</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7848214110986004125, 'MsgSeq': 871430256}
2025-08-05 23:23:18 | INFO | 收到文本消息: 消息ID:2012511621 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:[] 内容:我以为每轮都有呢
2025-08-05 23:23:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我以为每轮都有呢' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:23:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['我以为每轮都有呢']
2025-08-05 23:23:18 | DEBUG | 处理消息内容: '我以为每轮都有呢'
2025-08-05 23:23:18 | DEBUG | 消息内容 '我以为每轮都有呢' 不匹配任何命令，忽略
2025-08-05 23:23:29 | DEBUG | 收到消息: {'MsgId': 1440701623, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_4183511832012:\n吐了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407421, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_LTAXCzBJ|v1_59+K/9tz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7550934974152613963, 'MsgSeq': *********}
2025-08-05 23:23:29 | INFO | 收到文本消息: 消息ID:1440701623 来自:27852221909@chatroom 发送人:wxid_4183511832012 @:[] 内容:吐了
2025-08-05 23:23:29 | DEBUG | [DouBaoImageToImage] 收到文本消息: '吐了' from wxid_4183511832012 in 27852221909@chatroom
2025-08-05 23:23:29 | DEBUG | [DouBaoImageToImage] 命令解析: ['吐了']
2025-08-05 23:23:29 | DEBUG | 处理消息内容: '吐了'
2025-08-05 23:23:29 | DEBUG | 消息内容 '吐了' 不匹配任何命令，忽略
2025-08-05 23:24:21 | DEBUG | 收到消息: {'MsgId': 1177518886, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n一个月吗？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407473, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_bmF7YUHh|v1_O005GUEL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 一个月吗？', 'NewMsgId': 3883288212956601453, 'MsgSeq': 871430258}
2025-08-05 23:24:21 | INFO | 收到文本消息: 消息ID:1177518886 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:一个月吗？
2025-08-05 23:24:22 | DEBUG | [DouBaoImageToImage] 收到文本消息: '一个月吗？' from wxid_l9koi6kli78i22 in 48097389945@chatroom
2025-08-05 23:24:22 | DEBUG | [DouBaoImageToImage] 命令解析: ['一个月吗？']
2025-08-05 23:24:22 | DEBUG | 处理消息内容: '一个月吗？'
2025-08-05 23:24:22 | DEBUG | 消息内容 '一个月吗？' 不匹配任何命令，忽略
2025-08-05 23:24:24 | DEBUG | 收到消息: {'MsgId': 144201460, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n1.7'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407476, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>71</membercount>\n\t<signature>N0_V1_OSruBlhp|v1_TfE9qSQ0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 1.7', 'NewMsgId': 4313521138865391621, 'MsgSeq': 871430259}
2025-08-05 23:24:24 | INFO | 收到文本消息: 消息ID:144201460 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:1.7
2025-08-05 23:24:25 | DEBUG | [DouBaoImageToImage] 收到文本消息: '1.7' from wxid_l9koi6kli78i22 in 48097389945@chatroom
2025-08-05 23:24:25 | DEBUG | [DouBaoImageToImage] 命令解析: ['1.7']
2025-08-05 23:24:25 | DEBUG | 处理消息内容: '1.7'
2025-08-05 23:24:25 | DEBUG | 消息内容 '1.7' 不匹配任何命令，忽略
2025-08-05 23:24:50 | DEBUG | 收到消息: {'MsgId': 1975553338, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n@初见\u2005老板 您这边本月需要爬位服务吗 '}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407502, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_xfxd40diz3bd22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_vnTI/uGt|v1_dXZNhebg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 464144235429822183, 'MsgSeq': 871430260}
2025-08-05 23:24:50 | INFO | 收到文本消息: 消息ID:1975553338 来自:27852221909@chatroom 发送人:tianen532965049 @:['wxid_xfxd40diz3bd22'] 内容:@初见 老板 您这边本月需要爬位服务吗 
2025-08-05 23:24:50 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@初见 老板 您这边本月需要爬位服务吗' from tianen532965049 in 27852221909@chatroom
2025-08-05 23:24:50 | DEBUG | [DouBaoImageToImage] 命令解析: ['@初见\u2005老板', '您这边本月需要爬位服务吗']
2025-08-05 23:24:50 | DEBUG | 处理消息内容: '@初见 老板 您这边本月需要爬位服务吗'
2025-08-05 23:24:50 | DEBUG | 消息内容 '@初见 老板 您这边本月需要爬位服务吗' 不匹配任何命令，忽略
2025-08-05 23:25:24 | DEBUG | 收到消息: {'MsgId': 1593572909, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xfxd40diz3bd22:\n排位吗？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754407536, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_zll5IMz+|v1_en9ST7jr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8698092626894784897, 'MsgSeq': 871430261}
2025-08-05 23:25:24 | INFO | 收到文本消息: 消息ID:1593572909 来自:27852221909@chatroom 发送人:wxid_xfxd40diz3bd22 @:[] 内容:排位吗？
2025-08-05 23:25:24 | DEBUG | [DouBaoImageToImage] 收到文本消息: '排位吗？' from wxid_xfxd40diz3bd22 in 27852221909@chatroom
2025-08-05 23:25:24 | DEBUG | [DouBaoImageToImage] 命令解析: ['排位吗？']
2025-08-05 23:25:24 | DEBUG | 处理消息内容: '排位吗？'
2025-08-05 23:25:24 | DEBUG | 消息内容 '排位吗？' 不匹配任何命令，忽略
